#!/usr/bin/env python3
"""
GodEye 服务启动和验证脚本
"""

import subprocess
import time
import requests
import sys
import os

def run_command(cmd, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd, timeout=60)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令执行超时"
    except Exception as e:
        return False, "", str(e)

def check_docker():
    """检查Docker是否运行"""
    print("🔍 检查Docker状态...")
    success, stdout, stderr = run_command("docker info")
    if success:
        print("✅ Docker运行正常")
        return True
    else:
        print("❌ Docker未运行或有问题")
        print(f"错误: {stderr}")
        return False

def stop_existing_containers():
    """停止现有容器"""
    print("🛑 停止现有GodEye容器...")
    run_command("docker-compose -f docker-compose.simple.yml down --remove-orphans --volumes")
    print("✅ 容器停止完成")

def build_and_start():
    """构建并启动服务"""
    print("🔨 构建GodEye镜像...")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.simple.yml build --no-cache")
    if not success:
        print("❌ 构建失败")
        print(f"错误: {stderr}")
        return False
    
    print("✅ 镜像构建成功")
    
    print("🚀 启动GodEye服务...")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.simple.yml up -d")
    if not success:
        print("❌ 服务启动失败")
        print(f"错误: {stderr}")
        return False
    
    print("✅ 服务启动命令执行成功")
    return True

def check_containers():
    """检查容器状态"""
    print("📋 检查容器状态...")
    success, stdout, stderr = run_command("docker ps --format 'table {{.Names}}\\t{{.Status}}\\t{{.Ports}}'")
    if success:
        print("容器列表:")
        print(stdout)
        
        # 检查GodEye相关容器
        godeye_containers = []
        for line in stdout.split('\n'):
            if 'godeye' in line.lower():
                godeye_containers.append(line)
        
        if godeye_containers:
            print(f"✅ 找到 {len(godeye_containers)} 个GodEye容器")
            return True
        else:
            print("❌ 未找到GodEye容器")
            return False
    else:
        print("❌ 无法获取容器状态")
        return False

def wait_for_services():
    """等待服务就绪"""
    print("⏳ 等待服务就绪...")
    
    # 等待PostgreSQL
    print("🗄️ 检查PostgreSQL...")
    for i in range(30):
        success, stdout, stderr = run_command("docker exec godeye-postgres pg_isready -U godeye")
        if success:
            print("✅ PostgreSQL已就绪")
            break
        time.sleep(2)
        print(f"   等待PostgreSQL... ({i+1}/30)")
    else:
        print("❌ PostgreSQL启动超时")
        return False
    
    # 等待Redis
    print("💾 检查Redis...")
    for i in range(30):
        success, stdout, stderr = run_command("docker exec godeye-redis redis-cli ping")
        if success and "PONG" in stdout:
            print("✅ Redis已就绪")
            break
        time.sleep(2)
        print(f"   等待Redis... ({i+1}/30)")
    else:
        print("❌ Redis启动超时")
        return False
    
    # 等待GodEye应用
    print("🦅 检查GodEye应用...")
    for i in range(60):
        try:
            response = requests.get("http://localhost/health", timeout=5)
            if response.status_code == 200:
                print("✅ GodEye应用已就绪")
                print(f"   健康检查响应: {response.text[:100]}")
                return True
        except:
            pass
        time.sleep(3)
        print(f"   等待GodEye应用... ({i+1}/60)")
    
    print("⚠️ GodEye应用响应超时，但可能仍在启动中")
    return True

def verify_services():
    """验证服务可用性"""
    print("🔍 验证服务可用性...")
    
    # 检查主页
    try:
        response = requests.get("http://localhost", timeout=10)
        if response.status_code == 200:
            print("✅ GodEye主页可访问")
            print(f"   响应状态: {response.status_code}")
            print(f"   内容长度: {len(response.text)} 字符")
        else:
            print(f"⚠️ GodEye主页响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ GodEye主页无法访问: {e}")
    
    # 检查健康端点
    try:
        response = requests.get("http://localhost/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查端点正常")
            print(f"   响应内容: {response.text}")
        else:
            print(f"⚠️ 健康检查异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")

def show_final_status():
    """显示最终状态"""
    print("\n" + "="*50)
    print("🦅 GodEye 系统状态报告")
    print("="*50)
    
    # 显示容器状态
    print("📋 容器状态:")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.simple.yml ps")
    if success:
        print(stdout)
    
    # 显示端口占用
    print("\n📡 端口状态:")
    ports = [80, 5432, 6379]
    for port in ports:
        success, stdout, stderr = run_command(f"lsof -i :{port}")
        if success and stdout.strip():
            print(f"   ✅ 端口 {port}: 已占用")
        else:
            print(f"   ❌ 端口 {port}: 未占用")
    
    print("\n🌐 访问信息:")
    print("   主应用: http://localhost")
    print("   健康检查: http://localhost/health")
    print("   默认用户: admin")
    print("   默认密码: admin123")
    
    print("\n🚀 GodEye特色功能:")
    print("   ✅ 多平台监控: GitHub + GitLab + Gitee")
    print("   ✅ 全局关键词搜索")
    print("   ✅ 智能账号管理")
    print("   ✅ 多渠道告警通知")
    print("   ✅ 现代化Web界面")
    
    print("\n" + "="*50)

def main():
    """主函数"""
    print("🚀 GodEye 系统启动和验证")
    print("="*30)
    
    # 切换到正确目录
    os.chdir('/Users/<USER>/hawkeye/godeye')
    
    # 检查Docker
    if not check_docker():
        sys.exit(1)
    
    # 停止现有容器
    stop_existing_containers()
    
    # 构建并启动
    if not build_and_start():
        sys.exit(1)
    
    # 等待服务启动
    time.sleep(10)
    
    # 检查容器
    check_containers()
    
    # 等待服务就绪
    wait_for_services()
    
    # 验证服务
    verify_services()
    
    # 显示最终状态
    show_final_status()
    
    print("\n🎉 GodEye系统启动验证完成！")
    print("请访问 http://localhost 开始使用GodEye系统")

if __name__ == "__main__":
    main()
