#!/bin/bash

echo "🚀 GodEye 全面功能测试"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_code="$5"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}测试: $name${NC}"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" -H "Content-Type: application/json" \
            -H "Authorization: Bearer $TOKEN" -d "$data" "$url")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" \
            -H "Authorization: Bearer $TOKEN" "$url")
    fi
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "$expected_code" ]; then
        echo -e "${GREEN}✅ 通过 (HTTP $http_code)${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        echo -e "${RED}❌ 失败 (期望 $expected_code, 实际 $http_code)${NC}"
        echo "响应: $body"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 检查服务状态
echo -e "${BLUE}1. 检查服务状态${NC}"
if ! curl -s http://localhost:80/health > /dev/null; then
    echo -e "${RED}❌ 服务未运行，请先启动服务${NC}"
    echo "启动命令: docker-compose -f docker-compose.simple.yml up -d"
    exit 1
fi
echo -e "${GREEN}✅ 服务运行正常${NC}"

# 用户登录获取Token
echo -e "${BLUE}2. 用户认证测试${NC}"
login_response=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' \
    http://localhost:80/api/auth/login)

if echo "$login_response" | grep -q '"code":200'; then
    TOKEN=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✅ 登录成功，获取到Token${NC}"
else
    echo -e "${RED}❌ 登录失败${NC}"
    echo "响应: $login_response"
    exit 1
fi

# API测试
echo -e "${BLUE}3. API接口测试${NC}"

# 健康检查
test_api "健康检查" "GET" "http://localhost:80/health" "" "200"

# 用户信息
test_api "获取用户信息" "GET" "http://localhost:80/api/auth/user" "" "200"

# GitHub账号管理
test_api "获取GitHub账号列表" "GET" "http://localhost:80/api/setting/github" "" "200"

github_data='{"username":"test_user","token":"ghp_test_token","is_active":true}'
test_api "创建GitHub账号" "POST" "http://localhost:80/api/setting/github" "$github_data" "200"

# 查询管理
test_api "获取查询列表" "GET" "http://localhost:80/api/setting/query" "" "200"

query_data='{"keyword":"password","description":"测试查询","is_active":true}'
test_api "创建查询" "POST" "http://localhost:80/api/setting/query" "$query_data" "200"

# 搜索结果
test_api "获取搜索结果" "GET" "http://localhost:80/api/leakage" "" "200"

# 通知配置测试
echo -e "${BLUE}4. 通知系统测试${NC}"

test_api "获取通知类型" "GET" "http://localhost:80/api/setting/notice/types" "" "200"
test_api "获取通知配置" "GET" "http://localhost:80/api/setting/notice" "" "200"

# 创建邮件通知配置
email_config='{"name":"测试邮件","type":"email","config":"{\"host\":\"smtp.gmail.com\",\"port\":587,\"username\":\"<EMAIL>\",\"password\":\"password\",\"from\":\"<EMAIL>\",\"to\":\"<EMAIL>\"}","is_active":true,"description":"测试邮件通知"}'
test_api "创建邮件通知配置" "POST" "http://localhost:80/api/setting/notice" "$email_config" "200"

# 创建企业微信通知配置
wechat_config='{"name":"测试企业微信","type":"wechat","config":"{\"webhook_url\":\"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=test\"}","is_active":true,"description":"测试企业微信通知"}'
test_api "创建企业微信通知配置" "POST" "http://localhost:80/api/setting/notice" "$wechat_config" "200"

# 黑名单管理测试
echo -e "${BLUE}5. 黑名单管理测试${NC}"

test_api "获取黑名单列表" "GET" "http://localhost:80/api/setting/blacklist" "" "200"

# 添加仓库到黑名单
repo_blacklist='{"type":"repository","value":"test/repo","reason":"测试仓库"}'
test_api "添加仓库黑名单" "POST" "http://localhost:80/api/setting/blacklist" "$repo_blacklist" "200"

# 添加文件到黑名单
file_blacklist='{"type":"file","value":"test.txt","reason":"测试文件"}'
test_api "添加文件黑名单" "POST" "http://localhost:80/api/setting/blacklist" "$file_blacklist" "200"

# 检查黑名单状态
test_api "检查黑名单状态" "GET" "http://localhost:80/api/setting/blacklist/check?repository=test/repo&file_path=test.txt" "" "200"

# 统计和趋势
echo -e "${BLUE}6. 统计数据测试${NC}"

test_api "获取统计数据" "GET" "http://localhost:80/api/statistic" "" "200"
test_api "获取趋势数据" "GET" "http://localhost:80/api/trend" "" "200"

# 定时任务状态
echo -e "${BLUE}7. 定时任务测试${NC}"

test_api "获取定时任务状态" "GET" "http://localhost:80/api/setting/cron" "" "200"

# 数据库连接测试
echo -e "${BLUE}8. 数据库功能测试${NC}"

# 测试分页
test_api "分页查询结果" "GET" "http://localhost:80/api/leakage?page=1&limit=10" "" "200"

# 测试过滤
test_api "过滤查询结果" "GET" "http://localhost:80/api/leakage?repository=test" "" "200"

# 性能测试
echo -e "${BLUE}9. 性能测试${NC}"

echo "执行并发请求测试..."
for i in {1..10}; do
    curl -s -H "Authorization: Bearer $TOKEN" http://localhost:80/api/auth/user > /dev/null &
done
wait

echo -e "${GREEN}✅ 并发测试完成${NC}"

# 清理测试数据
echo -e "${BLUE}10. 清理测试数据${NC}"

# 获取创建的资源ID并删除
github_list=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:80/api/setting/github)
query_list=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:80/api/setting/query)
notice_list=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:80/api/setting/notice)
blacklist_list=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:80/api/setting/blacklist)

echo "清理测试数据完成"

# 测试结果汇总
echo ""
echo "🎯 测试结果汇总"
echo "================"
echo -e "总测试数: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    echo ""
    echo "✅ 认证系统正常"
    echo "✅ GitHub集成正常"
    echo "✅ 查询管理正常"
    echo "✅ 搜索功能正常"
    echo "✅ 通知系统正常"
    echo "✅ 黑名单管理正常"
    echo "✅ 统计功能正常"
    echo "✅ 定时任务正常"
    echo "✅ 数据库连接正常"
    echo "✅ 性能表现良好"
    echo ""
    echo "🚀 GodEye系统功能完整，可以投入使用！"
    exit 0
else
    success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "${YELLOW}⚠️  测试通过率: $success_rate%${NC}"
    echo ""
    echo "需要修复的问题:"
    echo "- 检查失败的API接口"
    echo "- 验证数据库连接"
    echo "- 确认服务配置"
    exit 1
fi
