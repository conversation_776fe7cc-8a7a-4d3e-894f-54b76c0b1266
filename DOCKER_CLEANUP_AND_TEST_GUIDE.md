# GodEye Docker清理和测试指南

## 🚨 当前问题诊断

根据您的反馈，系统访问失败，可能的原因：
1. Docker容器没有正确清理
2. 服务没有正常启动
3. 端口冲突或配置问题

## 🧹 手动清理步骤

### 1. 停止所有GodEye相关容器
```bash
cd godeye

# 停止旧的复杂架构
docker-compose down --remove-orphans

# 停止简化架构
docker-compose -f docker-compose.simple.yml down --remove-orphans

# 强制停止所有容器
docker stop $(docker ps -aq) 2>/dev/null || true
```

### 2. 清理镜像和资源
```bash
# 删除GodEye相关镜像
docker images | grep godeye | awk '{print $3}' | xargs docker rmi -f

# 清理悬空镜像
docker image prune -f

# 清理网络
docker network prune -f

# 清理卷
docker volume prune -f
```

### 3. 检查端口占用
```bash
# 检查80端口
lsof -i :80

# 检查8080端口  
lsof -i :8080

# 检查3000端口
lsof -i :3000

# 如果有占用，杀死进程
sudo kill -9 $(lsof -ti :80) 2>/dev/null || true
sudo kill -9 $(lsof -ti :8080) 2>/dev/null || true
sudo kill -9 $(lsof -ti :3000) 2>/dev/null || true
```

## 🚀 重新构建和启动

### 1. 验证Docker环境
```bash
# 检查Docker状态
docker --version
docker-compose --version
docker info
```

### 2. 构建简化架构
```bash
cd godeye

# 构建镜像（无缓存）
docker-compose -f docker-compose.simple.yml build --no-cache

# 检查构建结果
docker images | grep godeye
```

### 3. 启动服务
```bash
# 启动服务
docker-compose -f docker-compose.simple.yml up -d

# 检查容器状态
docker-compose -f docker-compose.simple.yml ps

# 查看日志
docker-compose -f docker-compose.simple.yml logs -f
```

## 🔍 服务验证步骤

### 1. 健康检查
```bash
# 等待服务启动
sleep 30

# 检查健康端点
curl http://localhost:80/health

# 预期响应: {"status":"ok"}
```

### 2. 登录测试
```bash
# 测试登录API
curl -X POST -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  http://localhost:80/api/auth/login

# 预期响应包含: "code":200 和 "token"
```

### 3. 浏览器访问测试
- 打开浏览器访问: http://localhost:80
- 应该看到登录页面
- 使用 admin/admin123 登录

## 🐛 常见问题排查

### 问题1: 容器启动失败
```bash
# 查看详细日志
docker-compose -f docker-compose.simple.yml logs godeye-app

# 检查数据库连接
docker-compose -f docker-compose.simple.yml logs postgres

# 检查Redis连接
docker-compose -f docker-compose.simple.yml logs redis
```

### 问题2: 端口访问失败
```bash
# 检查容器端口映射
docker port $(docker-compose -f docker-compose.simple.yml ps -q godeye-app)

# 检查防火墙设置
sudo ufw status

# 检查本地网络
netstat -tlnp | grep :80
```

### 问题3: 数据库连接问题
```bash
# 进入应用容器
docker-compose -f docker-compose.simple.yml exec godeye-app sh

# 检查数据库连接
psql -h postgres -U godeye -d godeye

# 检查表结构
\dt
```

## 🧪 完整功能测试

### 自动化测试脚本
```bash
# 运行综合测试
chmod +x comprehensive_test.sh
./comprehensive_test.sh
```

### 手动功能测试清单

#### ✅ 基础功能
- [ ] 系统健康检查 (GET /health)
- [ ] 用户登录 (POST /api/auth/login)
- [ ] 获取用户信息 (GET /api/auth/user)

#### ✅ GitHub集成
- [ ] 获取GitHub账号列表 (GET /api/setting/github)
- [ ] 添加GitHub账号 (POST /api/setting/github)
- [ ] 更新GitHub账号 (PUT /api/setting/github/:id)
- [ ] 删除GitHub账号 (DELETE /api/setting/github/:id)

#### ✅ 查询管理
- [ ] 获取查询列表 (GET /api/setting/query)
- [ ] 创建查询 (POST /api/setting/query)
- [ ] 更新查询 (PUT /api/setting/query/:id)
- [ ] 删除查询 (DELETE /api/setting/query/:id)

#### ✅ 搜索结果
- [ ] 获取搜索结果 (GET /api/leakage)
- [ ] 分页查询 (GET /api/leakage?page=1&limit=10)
- [ ] 过滤查询 (GET /api/leakage?repository=test)

#### ✅ 通知系统
- [ ] 获取通知配置 (GET /api/setting/notice)
- [ ] 创建通知配置 (POST /api/setting/notice)
- [ ] 测试通知 (POST /api/setting/notice/:id/test)
- [ ] 发送通知 (POST /api/setting/notice/send)

#### ✅ 黑名单管理
- [ ] 获取黑名单 (GET /api/setting/blacklist)
- [ ] 添加黑名单 (POST /api/setting/blacklist)
- [ ] 检查黑名单状态 (GET /api/setting/blacklist/check)

#### ✅ 统计功能
- [ ] 获取统计数据 (GET /api/statistic)
- [ ] 获取趋势数据 (GET /api/trend)

## 📊 性能验证

### 并发测试
```bash
# 并发登录测试
for i in {1..10}; do
  curl -X POST -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' \
    http://localhost:80/api/auth/login &
done
wait
```

### 内存和CPU监控
```bash
# 监控容器资源使用
docker stats

# 检查系统资源
top
htop
```

## 🎯 成功标准

### 系统启动成功标准
1. ✅ 所有3个容器正常运行
2. ✅ 健康检查返回200状态
3. ✅ 登录API正常响应
4. ✅ 浏览器可以访问登录页面

### 功能完整性标准
1. ✅ 所有API端点正常响应
2. ✅ 数据库操作正常
3. ✅ 通知系统可以发送消息
4. ✅ 黑名单过滤正常工作

### 性能标准
1. ✅ 系统启动时间 < 60秒
2. ✅ API响应时间 < 2秒
3. ✅ 内存使用 < 1GB
4. ✅ CPU使用 < 50%

## 🚨 紧急修复方案

如果上述步骤仍然失败，请执行以下紧急修复：

### 方案1: 完全重置
```bash
# 停止所有Docker容器
docker stop $(docker ps -aq)

# 删除所有容器
docker rm $(docker ps -aq)

# 删除所有镜像
docker rmi $(docker images -q)

# 重新构建
cd godeye
docker-compose -f docker-compose.simple.yml build --no-cache
docker-compose -f docker-compose.simple.yml up -d
```

### 方案2: 使用原始Hawkeye
```bash
# 如果GodEye仍有问题，临时使用原始Hawkeye
cd ../hawkeye
docker-compose up -d
```

## 📞 支持信息

如果问题持续存在，请提供以下信息：
1. Docker版本: `docker --version`
2. 系统信息: `uname -a`
3. 容器状态: `docker ps -a`
4. 错误日志: `docker-compose -f docker-compose.simple.yml logs`
5. 端口占用: `lsof -i :80`
