package webhook

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/godeye/notification/internal/config"
)

// Service Webhook服务
type Service struct {
	config *config.WebhookConfig
	client *http.Client
}

// WebhookMessage Webhook消息
type WebhookMessage struct {
	URL         string                 `json:"url"`
	Method      string                 `json:"method,omitempty"` // GET, POST, PUT, PATCH
	Headers     map[string]string      `json:"headers,omitempty"`
	Payload     map[string]interface{} `json:"payload"`
	Secret      string                 `json:"secret,omitempty"` // 用于签名验证
	ContentType string                 `json:"content_type,omitempty"`
	Timeout     time.Duration          `json:"timeout,omitempty"`
}

// WebhookResponse Webhook响应
type WebhookResponse struct {
	Success      bool                   `json:"success"`
	StatusCode   int                    `json:"status_code"`
	ResponseBody string                 `json:"response_body,omitempty"`
	Headers      map[string][]string    `json:"headers,omitempty"`
	Error        string                 `json:"error,omitempty"`
	Duration     time.Duration          `json:"duration"`
	SentAt       time.Time              `json:"sent_at"`
}

// SlackMessage Slack消息格式
type SlackMessage struct {
	Text        string            `json:"text,omitempty"`
	Username    string            `json:"username,omitempty"`
	IconEmoji   string            `json:"icon_emoji,omitempty"`
	Channel     string            `json:"channel,omitempty"`
	Attachments []SlackAttachment `json:"attachments,omitempty"`
}

// SlackAttachment Slack附件
type SlackAttachment struct {
	Color      string       `json:"color,omitempty"`
	Title      string       `json:"title,omitempty"`
	TitleLink  string       `json:"title_link,omitempty"`
	Text       string       `json:"text,omitempty"`
	Fields     []SlackField `json:"fields,omitempty"`
	Footer     string       `json:"footer,omitempty"`
	Timestamp  int64        `json:"ts,omitempty"`
}

// SlackField Slack字段
type SlackField struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

// DingTalkMessage 钉钉消息格式
type DingTalkMessage struct {
	MsgType  string                 `json:"msgtype"`
	Text     *DingTalkText          `json:"text,omitempty"`
	Markdown *DingTalkMarkdown      `json:"markdown,omitempty"`
	At       *DingTalkAt            `json:"at,omitempty"`
}

// DingTalkText 钉钉文本消息
type DingTalkText struct {
	Content string `json:"content"`
}

// DingTalkMarkdown 钉钉Markdown消息
type DingTalkMarkdown struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}

// DingTalkAt 钉钉@功能
type DingTalkAt struct {
	AtMobiles []string `json:"atMobiles,omitempty"`
	IsAtAll   bool     `json:"isAtAll,omitempty"`
}

// WeChatWorkMessage 企业微信消息格式
type WeChatWorkMessage struct {
	MsgType  string                 `json:"msgtype"`
	Text     *WeChatWorkText        `json:"text,omitempty"`
	Markdown *WeChatWorkMarkdown    `json:"markdown,omitempty"`
}

// WeChatWorkText 企业微信文本消息
type WeChatWorkText struct {
	Content             string   `json:"content"`
	MentionedList       []string `json:"mentioned_list,omitempty"`
	MentionedMobileList []string `json:"mentioned_mobile_list,omitempty"`
}

// WeChatWorkMarkdown 企业微信Markdown消息
type WeChatWorkMarkdown struct {
	Content string `json:"content"`
}

// FeishuMessage 飞书消息格式
type FeishuMessage struct {
	MsgType string                 `json:"msg_type"`
	Content map[string]interface{} `json:"content"`
}

// FeishuTextContent 飞书文本消息内容
type FeishuTextContent struct {
	Text string `json:"text"`
}

// FeishuRichTextContent 飞书富文本消息内容
type FeishuRichTextContent struct {
	Post map[string]interface{} `json:"post"`
}

// FeishuCardContent 飞书卡片消息内容
type FeishuCardContent struct {
	Config   map[string]interface{} `json:"config"`
	Elements []map[string]interface{} `json:"elements"`
	Header   map[string]interface{} `json:"header"`
}

// NewService 创建Webhook服务
func NewService(config *config.WebhookConfig) *Service {
	client := &http.Client{
		Timeout: config.Timeout,
	}

	return &Service{
		config: config,
		client: client,
	}
}

// SendWebhook 发送Webhook
func (s *Service) SendWebhook(ctx context.Context, message *WebhookMessage) (*WebhookResponse, error) {
	startTime := time.Now()
	response := &WebhookResponse{
		SentAt: startTime,
	}

	// 设置默认值
	if message.Method == "" {
		message.Method = "POST"
	}
	if message.ContentType == "" {
		message.ContentType = "application/json"
	}
	if message.Timeout > 0 {
		ctx, cancel := context.WithTimeout(ctx, message.Timeout)
		defer cancel()
	}

	// 序列化payload
	payloadBytes, err := json.Marshal(message.Payload)
	if err != nil {
		response.Error = fmt.Sprintf("failed to marshal payload: %v", err)
		return response, err
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, message.Method, message.URL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		response.Error = fmt.Sprintf("failed to create request: %v", err)
		return response, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", message.ContentType)
	req.Header.Set("User-Agent", s.config.UserAgent)

	// 添加自定义请求头
	for key, value := range message.Headers {
		req.Header.Set(key, value)
	}

	// 添加签名（如果提供了secret）
	if message.Secret != "" {
		signature := s.generateSignature(payloadBytes, message.Secret)
		req.Header.Set("X-Signature-SHA256", signature)
	}

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		response.Error = fmt.Sprintf("failed to send request: %v", err)
		response.Duration = time.Since(startTime)
		return response, err
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		response.Error = fmt.Sprintf("failed to read response: %v", err)
	} else {
		response.ResponseBody = string(responseBody)
	}

	response.StatusCode = resp.StatusCode
	response.Headers = resp.Header
	response.Duration = time.Since(startTime)
	response.Success = resp.StatusCode >= 200 && resp.StatusCode < 300

	if !response.Success && response.Error == "" {
		response.Error = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, response.ResponseBody)
	}

	return response, nil
}

// SendSlackMessage 发送Slack消息
func (s *Service) SendSlackMessage(ctx context.Context, webhookURL string, message *SlackMessage) (*WebhookResponse, error) {
	payload := make(map[string]interface{})
	
	// 转换为map
	data, _ := json.Marshal(message)
	json.Unmarshal(data, &payload)

	webhookMessage := &WebhookMessage{
		URL:     webhookURL,
		Method:  "POST",
		Payload: payload,
	}

	return s.SendWebhook(ctx, webhookMessage)
}

// SendDingTalkMessage 发送钉钉消息
func (s *Service) SendDingTalkMessage(ctx context.Context, webhookURL string, message *DingTalkMessage) (*WebhookResponse, error) {
	payload := make(map[string]interface{})
	
	// 转换为map
	data, _ := json.Marshal(message)
	json.Unmarshal(data, &payload)

	webhookMessage := &WebhookMessage{
		URL:     webhookURL,
		Method:  "POST",
		Payload: payload,
	}

	return s.SendWebhook(ctx, webhookMessage)
}

// SendWeChatWorkMessage 发送企业微信消息
func (s *Service) SendWeChatWorkMessage(ctx context.Context, webhookURL string, message *WeChatWorkMessage) (*WebhookResponse, error) {
	payload := make(map[string]interface{})

	// 转换为map
	data, _ := json.Marshal(message)
	json.Unmarshal(data, &payload)

	webhookMessage := &WebhookMessage{
		URL:     webhookURL,
		Method:  "POST",
		Payload: payload,
	}

	return s.SendWebhook(ctx, webhookMessage)
}

// SendFeishuMessage 发送飞书消息
func (s *Service) SendFeishuMessage(ctx context.Context, webhookURL string, message *FeishuMessage) (*WebhookResponse, error) {
	payload := make(map[string]interface{})

	// 转换为map
	data, _ := json.Marshal(message)
	json.Unmarshal(data, &payload)

	webhookMessage := &WebhookMessage{
		URL:     webhookURL,
		Method:  "POST",
		Payload: payload,
	}

	return s.SendWebhook(ctx, webhookMessage)
}

// SendGenericWebhook 发送通用Webhook
func (s *Service) SendGenericWebhook(ctx context.Context, webhookURL string, payload map[string]interface{}) (*WebhookResponse, error) {
	webhookMessage := &WebhookMessage{
		URL:     webhookURL,
		Method:  "POST",
		Payload: payload,
	}

	return s.SendWebhook(ctx, webhookMessage)
}

// CreateSlackScanAlert 创建Slack扫描告警消息
func (s *Service) CreateSlackScanAlert(data map[string]interface{}) *SlackMessage {
	color := "good"
	if riskLevel, ok := data["risk_level"].(string); ok {
		switch riskLevel {
		case "high", "critical":
			color = "danger"
		case "medium":
			color = "warning"
		}
	}

	attachment := SlackAttachment{
		Color: color,
		Title: "🔍 GodEye 安全告警",
		Text:  fmt.Sprintf("在监控任务中发现了潜在的安全风险"),
		Fields: []SlackField{
			{
				Title: "任务名称",
				Value: fmt.Sprintf("%v", data["task_name"]),
				Short: true,
			},
			{
				Title: "风险级别",
				Value: fmt.Sprintf("%v", data["risk_level"]),
				Short: true,
			},
			{
				Title: "发现问题",
				Value: fmt.Sprintf("%v 个匹配项", data["match_count"]),
				Short: true,
			},
		},
		Footer:    "GodEye Security",
		Timestamp: time.Now().Unix(),
	}

	if dashboardURL, ok := data["dashboard_url"].(string); ok {
		attachment.TitleLink = dashboardURL
	}

	return &SlackMessage{
		Username:    "GodEye",
		IconEmoji:   ":shield:",
		Attachments: []SlackAttachment{attachment},
	}
}

// CreateDingTalkScanAlert 创建钉钉扫描告警消息
func (s *Service) CreateDingTalkScanAlert(data map[string]interface{}) *DingTalkMessage {
	riskEmoji := "🟡"
	if riskLevel, ok := data["risk_level"].(string); ok {
		switch riskLevel {
		case "critical":
			riskEmoji = "🔴"
		case "high":
			riskEmoji = "🟠"
		case "medium":
			riskEmoji = "🟡"
		case "low":
			riskEmoji = "🟢"
		}
	}

	title := "🔍 GodEye 安全告警"
	content := fmt.Sprintf(`
### %s %s

**任务名称**: %v
**风险级别**: %s %v
**发现问题**: %v 个匹配项
**扫描时间**: %v

> 在您的监控任务中发现了潜在的安全风险，请及时查看和处理。

[查看详情](%v)
`,
		title,
		riskEmoji,
		data["task_name"],
		riskEmoji,
		data["risk_level"],
		data["match_count"],
		data["scan_time"],
		data["dashboard_url"],
	)

	return &DingTalkMessage{
		MsgType: "markdown",
		Markdown: &DingTalkMarkdown{
			Title: title,
			Text:  content,
		},
	}
}

// CreateWeChatWorkScanAlert 创建企业微信扫描告警消息
func (s *Service) CreateWeChatWorkScanAlert(data map[string]interface{}) *WeChatWorkMessage {
	riskEmoji := "🟡"
	if riskLevel, ok := data["risk_level"].(string); ok {
		switch riskLevel {
		case "critical":
			riskEmoji = "🔴"
		case "high":
			riskEmoji = "🟠"
		case "medium":
			riskEmoji = "🟡"
		case "low":
			riskEmoji = "🟢"
		}
	}

	content := fmt.Sprintf(`### 🔍 GodEye 安全告警 %s

**任务名称**: %v
**风险级别**: %s %v
**发现问题**: %v 个匹配项
**扫描时间**: %v

> 在您的监控任务中发现了潜在的安全风险，请及时查看和处理。

[查看详情](%v)`,
		riskEmoji,
		data["task_name"],
		riskEmoji,
		data["risk_level"],
		data["match_count"],
		data["scan_time"],
		data["dashboard_url"],
	)

	return &WeChatWorkMessage{
		MsgType: "markdown",
		Markdown: &WeChatWorkMarkdown{
			Content: content,
		},
	}
}

// CreateFeishuScanAlert 创建飞书扫描告警消息
func (s *Service) CreateFeishuScanAlert(data map[string]interface{}) *FeishuMessage {
	riskColor := "yellow"
	riskEmoji := "🟡"
	if riskLevel, ok := data["risk_level"].(string); ok {
		switch riskLevel {
		case "critical":
			riskColor = "red"
			riskEmoji = "🔴"
		case "high":
			riskColor = "orange"
			riskEmoji = "🟠"
		case "medium":
			riskColor = "yellow"
			riskEmoji = "🟡"
		case "low":
			riskColor = "green"
			riskEmoji = "🟢"
		}
	}

	// 创建飞书卡片消息
	cardContent := &FeishuCardContent{
		Config: map[string]interface{}{
			"wide_screen_mode": true,
		},
		Header: map[string]interface{}{
			"title": map[string]interface{}{
				"content": fmt.Sprintf("🔍 GodEye 安全告警 %s", riskEmoji),
				"tag":     "plain_text",
			},
			"template": riskColor,
		},
		Elements: []map[string]interface{}{
			{
				"tag": "div",
				"text": map[string]interface{}{
					"content": fmt.Sprintf("**任务名称**: %v\n**风险级别**: %s %v\n**发现问题**: %v 个匹配项\n**扫描时间**: %v",
						data["task_name"],
						riskEmoji,
						data["risk_level"],
						data["match_count"],
						data["scan_time"],
					),
					"tag": "lark_md",
				},
			},
			{
				"tag": "note",
				"elements": []map[string]interface{}{
					{
						"content": "在您的监控任务中发现了潜在的安全风险，请及时查看和处理。",
						"tag":     "plain_text",
					},
				},
			},
		},
	}

	// 如果有dashboard链接，添加按钮
	if dashboardURL, ok := data["dashboard_url"].(string); ok && dashboardURL != "" {
		cardContent.Elements = append(cardContent.Elements, map[string]interface{}{
			"tag": "action",
			"actions": []map[string]interface{}{
				{
					"tag": "button",
					"text": map[string]interface{}{
						"content": "查看详情",
						"tag":     "plain_text",
					},
					"url":  dashboardURL,
					"type": "primary",
				},
			},
		})
	}

	return &FeishuMessage{
		MsgType: "interactive",
		Content: map[string]interface{}{
			"card": cardContent,
		},
	}
}

// generateSignature 生成HMAC-SHA256签名
func (s *Service) generateSignature(payload []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(payload)
	return "sha256=" + hex.EncodeToString(h.Sum(nil))
}

// ValidateSignature 验证签名
func (s *Service) ValidateSignature(payload []byte, signature, secret string) bool {
	expectedSignature := s.generateSignature(payload, secret)
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// RetryWebhook 重试Webhook发送
func (s *Service) RetryWebhook(ctx context.Context, message *WebhookMessage, maxRetries int, retryDelay time.Duration) (*WebhookResponse, error) {
	var lastResponse *WebhookResponse
	var lastError error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			select {
			case <-ctx.Done():
				return lastResponse, ctx.Err()
			case <-time.After(retryDelay):
			}
		}

		response, err := s.SendWebhook(ctx, message)
		if err == nil && response.Success {
			return response, nil
		}

		lastResponse = response
		lastError = err

		// 如果是客户端错误（4xx），不重试
		if response != nil && response.StatusCode >= 400 && response.StatusCode < 500 {
			break
		}
	}

	return lastResponse, lastError
}

// TestWebhook 测试Webhook连接
func (s *Service) TestWebhook(ctx context.Context, webhookURL string) (*WebhookResponse, error) {
	testPayload := map[string]interface{}{
		"test":      true,
		"message":   "This is a test webhook from GodEye",
		"timestamp": time.Now().Unix(),
	}

	message := &WebhookMessage{
		URL:     webhookURL,
		Method:  "POST",
		Payload: testPayload,
	}

	return s.SendWebhook(ctx, message)
}

// ParseWebhookType 解析Webhook类型
func (s *Service) ParseWebhookType(webhookURL string) string {
	url := strings.ToLower(webhookURL)

	if strings.Contains(url, "slack.com") {
		return "slack"
	} else if strings.Contains(url, "dingtalk.com") || strings.Contains(url, "oapi.dingtalk.com") {
		return "dingtalk"
	} else if strings.Contains(url, "qyapi.weixin.qq.com") {
		return "wechatwork"
	} else if strings.Contains(url, "open.feishu.cn") || strings.Contains(url, "open.larksuite.com") {
		return "feishu"
	} else if strings.Contains(url, "discord.com") {
		return "discord"
	} else if strings.Contains(url, "teams.microsoft.com") {
		return "teams"
	}

	return "generic"
}
