package rules

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"github.com/godeye/notification/internal/models"
)

// Engine 通知规则引擎
type Engine struct {
	db    *gorm.DB
	redis *redis.Client
}

// Event 事件数据
type Event struct {
	Type      string                 `json:"type"`       // scan_result, task_status, system_alert
	UserID    uuid.UUID              `json:"user_id"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`     // monitor, auth, system
}

// RuleCondition 规则条件
type RuleCondition struct {
	Field    string      `json:"field"`    // 字段名
	Operator string      `json:"operator"` // eq, ne, gt, lt, gte, lte, in, not_in, contains, regex
	Value    interface{} `json:"value"`    // 比较值
}

// ThrottleConfig 限流配置
type ThrottleConfig struct {
	Enabled   bool          `json:"enabled"`
	Window    time.Duration `json:"window"`    // 时间窗口
	MaxCount  int           `json:"max_count"` // 最大次数
	KeyFormat string        `json:"key_format"` // Redis key格式
}

// EvaluationResult 评估结果
type EvaluationResult struct {
	RuleID    uuid.UUID `json:"rule_id"`
	Matched   bool      `json:"matched"`
	Throttled bool      `json:"throttled"`
	Error     string    `json:"error,omitempty"`
}

// NewEngine 创建规则引擎
func NewEngine(db *gorm.DB, redis *redis.Client) *Engine {
	return &Engine{
		db:    db,
		redis: redis,
	}
}

// EvaluateEvent 评估事件
func (e *Engine) EvaluateEvent(ctx context.Context, event *Event) ([]*EvaluationResult, error) {
	// 获取用户的活跃规则
	var rules []models.NotificationRule
	err := e.db.Where("user_id = ? AND is_active = ?", event.UserID, true).
		Order("priority DESC").
		Find(&rules).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get rules: %w", err)
	}

	results := make([]*EvaluationResult, 0, len(rules))

	for _, rule := range rules {
		result := &EvaluationResult{
			RuleID: rule.ID,
		}

		// 评估条件
		matched, err := e.evaluateConditions(rule.Conditions, event)
		if err != nil {
			result.Error = err.Error()
			results = append(results, result)
			continue
		}

		result.Matched = matched

		// 如果匹配，检查限流
		if matched {
			throttled, err := e.checkThrottle(ctx, &rule, event)
			if err != nil {
				result.Error = err.Error()
			} else {
				result.Throttled = throttled
			}
		}

		results = append(results, result)
	}

	return results, nil
}

// evaluateConditions 评估条件
func (e *Engine) evaluateConditions(conditions models.JSONB, event *Event) (bool, error) {
	if len(conditions) == 0 {
		return true, nil // 没有条件则默认匹配
	}

	// 解析条件
	conditionsData, ok := conditions["conditions"]
	if !ok {
		return true, nil
	}

	conditionsList, ok := conditionsData.([]interface{})
	if !ok {
		return false, fmt.Errorf("invalid conditions format")
	}

	// 获取逻辑操作符，默认为AND
	logicOp := "AND"
	if op, exists := conditions["logic"]; exists {
		if opStr, ok := op.(string); ok {
			logicOp = strings.ToUpper(opStr)
		}
	}

	results := make([]bool, len(conditionsList))

	// 评估每个条件
	for i, conditionData := range conditionsList {
		conditionMap, ok := conditionData.(map[string]interface{})
		if !ok {
			return false, fmt.Errorf("invalid condition format at index %d", i)
		}

		condition := &RuleCondition{
			Field:    getString(conditionMap, "field"),
			Operator: getString(conditionMap, "operator"),
			Value:    conditionMap["value"],
		}

		result, err := e.evaluateCondition(condition, event)
		if err != nil {
			return false, fmt.Errorf("failed to evaluate condition %d: %w", i, err)
		}

		results[i] = result
	}

	// 应用逻辑操作符
	switch logicOp {
	case "AND":
		for _, result := range results {
			if !result {
				return false, nil
			}
		}
		return true, nil
	case "OR":
		for _, result := range results {
			if result {
				return true, nil
			}
		}
		return false, nil
	default:
		return false, fmt.Errorf("unsupported logic operator: %s", logicOp)
	}
}

// evaluateCondition 评估单个条件
func (e *Engine) evaluateCondition(condition *RuleCondition, event *Event) (bool, error) {
	// 获取字段值
	fieldValue, err := e.getFieldValue(condition.Field, event)
	if err != nil {
		return false, err
	}

	// 根据操作符进行比较
	switch condition.Operator {
	case "eq":
		return e.compareEqual(fieldValue, condition.Value), nil
	case "ne":
		return !e.compareEqual(fieldValue, condition.Value), nil
	case "gt":
		return e.compareGreater(fieldValue, condition.Value)
	case "lt":
		return e.compareLess(fieldValue, condition.Value)
	case "gte":
		return e.compareGreaterEqual(fieldValue, condition.Value)
	case "lte":
		return e.compareLessEqual(fieldValue, condition.Value)
	case "in":
		return e.compareIn(fieldValue, condition.Value)
	case "not_in":
		result, err := e.compareIn(fieldValue, condition.Value)
		return !result, err
	case "contains":
		return e.compareContains(fieldValue, condition.Value), nil
	case "regex":
		return e.compareRegex(fieldValue, condition.Value)
	default:
		return false, fmt.Errorf("unsupported operator: %s", condition.Operator)
	}
}

// getFieldValue 获取字段值
func (e *Engine) getFieldValue(field string, event *Event) (interface{}, error) {
	// 支持点号分隔的嵌套字段
	parts := strings.Split(field, ".")
	
	var current interface{} = map[string]interface{}{
		"type":      event.Type,
		"user_id":   event.UserID.String(),
		"timestamp": event.Timestamp,
		"source":    event.Source,
		"data":      event.Data,
	}

	for _, part := range parts {
		switch v := current.(type) {
		case map[string]interface{}:
			if val, exists := v[part]; exists {
				current = val
			} else {
				return nil, fmt.Errorf("field not found: %s", field)
			}
		default:
			return nil, fmt.Errorf("cannot access field %s on non-object", part)
		}
	}

	return current, nil
}

// 比较函数
func (e *Engine) compareEqual(a, b interface{}) bool {
	return fmt.Sprintf("%v", a) == fmt.Sprintf("%v", b)
}

func (e *Engine) compareGreater(a, b interface{}) (bool, error) {
	aNum, err := toFloat64(a)
	if err != nil {
		return false, err
	}
	bNum, err := toFloat64(b)
	if err != nil {
		return false, err
	}
	return aNum > bNum, nil
}

func (e *Engine) compareLess(a, b interface{}) (bool, error) {
	aNum, err := toFloat64(a)
	if err != nil {
		return false, err
	}
	bNum, err := toFloat64(b)
	if err != nil {
		return false, err
	}
	return aNum < bNum, nil
}

func (e *Engine) compareGreaterEqual(a, b interface{}) (bool, error) {
	aNum, err := toFloat64(a)
	if err != nil {
		return false, err
	}
	bNum, err := toFloat64(b)
	if err != nil {
		return false, err
	}
	return aNum >= bNum, nil
}

func (e *Engine) compareLessEqual(a, b interface{}) (bool, error) {
	aNum, err := toFloat64(a)
	if err != nil {
		return false, err
	}
	bNum, err := toFloat64(b)
	if err != nil {
		return false, err
	}
	return aNum <= bNum, nil
}

func (e *Engine) compareIn(a, b interface{}) (bool, error) {
	list, ok := b.([]interface{})
	if !ok {
		return false, fmt.Errorf("value must be an array for 'in' operator")
	}

	aStr := fmt.Sprintf("%v", a)
	for _, item := range list {
		if fmt.Sprintf("%v", item) == aStr {
			return true, nil
		}
	}
	return false, nil
}

func (e *Engine) compareContains(a, b interface{}) bool {
	aStr := strings.ToLower(fmt.Sprintf("%v", a))
	bStr := strings.ToLower(fmt.Sprintf("%v", b))
	return strings.Contains(aStr, bStr)
}

func (e *Engine) compareRegex(a, b interface{}) (bool, error) {
	aStr := fmt.Sprintf("%v", a)
	pattern := fmt.Sprintf("%v", b)
	
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return false, fmt.Errorf("invalid regex pattern: %w", err)
	}
	
	return regex.MatchString(aStr), nil
}

// checkThrottle 检查限流
func (e *Engine) checkThrottle(ctx context.Context, rule *models.NotificationRule, event *Event) (bool, error) {
	throttleConfig := rule.GetThrottleConfig()
	if !getBool(throttleConfig, "enabled") {
		return false, nil
	}

	// 解析限流配置
	window := getDuration(throttleConfig, "window", 1*time.Hour)
	maxCount := getInt(throttleConfig, "max_count", 10)
	keyFormat := getString(throttleConfig, "key_format", "throttle:rule:%s:user:%s")

	// 生成Redis key
	key := fmt.Sprintf(keyFormat, rule.ID.String(), event.UserID.String())

	// 检查当前计数
	count, err := e.redis.Get(ctx, key).Int()
	if err != nil && err != redis.Nil {
		return false, fmt.Errorf("failed to get throttle count: %w", err)
	}

	if count >= maxCount {
		return true, nil // 已达到限制
	}

	// 增加计数
	pipe := e.redis.Pipeline()
	pipe.Incr(ctx, key)
	pipe.Expire(ctx, key, window)
	_, err = pipe.Exec(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to update throttle count: %w", err)
	}

	return false, nil
}

// 辅助函数
func getString(m map[string]interface{}, key string) string {
	if val, exists := m[key]; exists {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

func getInt(m map[string]interface{}, key string, defaultValue int) int {
	if val, exists := m[key]; exists {
		if i, ok := val.(int); ok {
			return i
		}
		if f, ok := val.(float64); ok {
			return int(f)
		}
	}
	return defaultValue
}

func getBool(m map[string]interface{}, key string) bool {
	if val, exists := m[key]; exists {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

func getDuration(m map[string]interface{}, key string, defaultValue time.Duration) time.Duration {
	if val, exists := m[key]; exists {
		if str, ok := val.(string); ok {
			if d, err := time.ParseDuration(str); err == nil {
				return d
			}
		}
		if i, ok := val.(int); ok {
			return time.Duration(i) * time.Second
		}
		if f, ok := val.(float64); ok {
			return time.Duration(f) * time.Second
		}
	}
	return defaultValue
}

func toFloat64(v interface{}) (float64, error) {
	switch val := v.(type) {
	case float64:
		return val, nil
	case int:
		return float64(val), nil
	case int64:
		return float64(val), nil
	case string:
		return strconv.ParseFloat(val, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", v)
	}
}
