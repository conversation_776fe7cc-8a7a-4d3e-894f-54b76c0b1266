package email

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"html/template"
	"net/smtp"
	"path/filepath"
	"strings"
	"time"

	"github.com/jordan-wright/email"
	"gopkg.in/gomail.v2"

	"github.com/godeye/notification/internal/config"
)

// Service 邮件服务
type Service struct {
	config    *config.EmailConfig
	templates map[string]*template.Template
	dialer    *gomail.Dialer
}

// EmailMessage 邮件消息
type EmailMessage struct {
	To          []string               `json:"to"`
	CC          []string               `json:"cc,omitempty"`
	BCC         []string               `json:"bcc,omitempty"`
	Subject     string                 `json:"subject"`
	Template    string                 `json:"template,omitempty"`
	Content     string                 `json:"content,omitempty"`
	Variables   map[string]interface{} `json:"variables,omitempty"`
	Attachments []Attachment           `json:"attachments,omitempty"`
	Priority    string                 `json:"priority,omitempty"` // low, normal, high
	IsHTML      bool                   `json:"is_html"`
}

// Attachment 附件
type Attachment struct {
	Name     string `json:"name"`
	Content  []byte `json:"content"`
	MimeType string `json:"mime_type"`
}

// SendResult 发送结果
type SendResult struct {
	Success   bool                   `json:"success"`
	MessageID string                 `json:"message_id,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
	SentAt    time.Time              `json:"sent_at"`
}

// NewService 创建邮件服务
func NewService(config *config.EmailConfig) (*Service, error) {
	service := &Service{
		config:    config,
		templates: make(map[string]*template.Template),
	}

	// 初始化SMTP拨号器
	if err := service.initDialer(); err != nil {
		return nil, fmt.Errorf("failed to initialize dialer: %w", err)
	}

	// 加载邮件模板
	if err := service.loadTemplates(); err != nil {
		return nil, fmt.Errorf("failed to load templates: %w", err)
	}

	return service, nil
}

// initDialer 初始化SMTP拨号器
func (s *Service) initDialer() error {
	s.dialer = gomail.NewDialer(
		s.config.SMTP.Host,
		s.config.SMTP.Port,
		s.config.SMTP.Username,
		s.config.SMTP.Password,
	)

	// 配置TLS
	if s.config.SMTP.UseSSL {
		s.dialer.SSL = true
	} else if s.config.SMTP.UseTLS {
		s.dialer.TLSConfig = &tls.Config{
			ServerName:         s.config.SMTP.Host,
			InsecureSkipVerify: false,
		}
	}

	return nil
}

// loadTemplates 加载邮件模板
func (s *Service) loadTemplates() error {
	if s.config.Templates.Path == "" {
		return nil
	}

	// 定义模板函数
	funcMap := template.FuncMap{
		"formatTime": func(t time.Time) string {
			return t.Format("2006-01-02 15:04:05")
		},
		"formatDate": func(t time.Time) string {
			return t.Format("2006-01-02")
		},
		"upper": strings.ToUpper,
		"lower": strings.ToLower,
		"title": strings.Title,
	}

	// 加载模板文件
	templateFiles := []string{
		"scan_result_alert.html",
		"scan_result_alert.txt",
		"task_status_update.html",
		"task_status_update.txt",
		"weekly_report.html",
		"weekly_report.txt",
		"system_notification.html",
		"system_notification.txt",
	}

	for _, filename := range templateFiles {
		templatePath := filepath.Join(s.config.Templates.Path, filename)
		
		// 解析模板名称（去掉扩展名）
		templateName := strings.TrimSuffix(filename, filepath.Ext(filename))
		
		// 解析模板
		tmpl, err := template.New(templateName).Funcs(funcMap).ParseFiles(templatePath)
		if err != nil {
			// 如果模板文件不存在，跳过
			continue
		}
		
		s.templates[templateName] = tmpl
	}

	return nil
}

// SendEmail 发送邮件
func (s *Service) SendEmail(message *EmailMessage) (*SendResult, error) {
	result := &SendResult{
		SentAt: time.Now(),
	}

	// 验证邮件消息
	if err := s.validateMessage(message); err != nil {
		result.Error = err.Error()
		return result, err
	}

	// 创建邮件
	m := gomail.NewMessage()

	// 设置发件人
	m.SetHeader("From", fmt.Sprintf("%s <%s>", s.config.Sender.Name, s.config.Sender.Email))

	// 设置收件人
	m.SetHeader("To", message.To...)
	if len(message.CC) > 0 {
		m.SetHeader("Cc", message.CC...)
	}
	if len(message.BCC) > 0 {
		m.SetHeader("Bcc", message.BCC...)
	}

	// 设置主题
	m.SetHeader("Subject", message.Subject)

	// 设置优先级
	if message.Priority != "" {
		switch message.Priority {
		case "high":
			m.SetHeader("X-Priority", "1")
			m.SetHeader("X-MSMail-Priority", "High")
		case "low":
			m.SetHeader("X-Priority", "5")
			m.SetHeader("X-MSMail-Priority", "Low")
		}
	}

	// 设置邮件内容
	content, err := s.renderContent(message)
	if err != nil {
		result.Error = err.Error()
		return result, err
	}

	if message.IsHTML {
		m.SetBody("text/html", content)
	} else {
		m.SetBody("text/plain", content)
	}

	// 添加附件
	for _, attachment := range message.Attachments {
		m.Attach(attachment.Name, gomail.SetCopyFunc(func(w gomail.WriterTo) error {
			_, err := w.Write(attachment.Content)
			return err
		}))
	}

	// 发送邮件
	if err := s.dialer.DialAndSend(m); err != nil {
		result.Error = err.Error()
		return result, err
	}

	result.Success = true
	result.MessageID = fmt.Sprintf("%d@%s", time.Now().UnixNano(), s.config.SMTP.Host)

	return result, nil
}

// SendBulkEmail 批量发送邮件
func (s *Service) SendBulkEmail(messages []*EmailMessage) ([]*SendResult, error) {
	results := make([]*SendResult, len(messages))

	for i, message := range messages {
		result, err := s.SendEmail(message)
		results[i] = result
		
		if err != nil {
			// 记录错误但继续处理其他邮件
			continue
		}

		// 添加发送间隔以避免被标记为垃圾邮件
		time.Sleep(100 * time.Millisecond)
	}

	return results, nil
}

// TestConnection 测试SMTP连接
func (s *Service) TestConnection() error {
	// 创建测试邮件
	m := gomail.NewMessage()
	m.SetHeader("From", s.config.Sender.Email)
	m.SetHeader("To", s.config.Sender.Email)
	m.SetHeader("Subject", "SMTP Connection Test")
	m.SetBody("text/plain", "This is a test email to verify SMTP connection.")

	// 尝试连接但不发送
	return s.dialer.DialAndSend(m)
}

// validateMessage 验证邮件消息
func (s *Service) validateMessage(message *EmailMessage) error {
	if len(message.To) == 0 {
		return fmt.Errorf("no recipients specified")
	}

	if message.Subject == "" {
		return fmt.Errorf("subject is required")
	}

	if message.Content == "" && message.Template == "" {
		return fmt.Errorf("either content or template must be specified")
	}

	// 验证邮箱地址格式
	for _, email := range message.To {
		if !isValidEmail(email) {
			return fmt.Errorf("invalid email address: %s", email)
		}
	}

	return nil
}

// renderContent 渲染邮件内容
func (s *Service) renderContent(message *EmailMessage) (string, error) {
	// 如果直接提供了内容，直接返回
	if message.Content != "" {
		return message.Content, nil
	}

	// 使用模板渲染
	if message.Template != "" {
		tmpl, exists := s.templates[message.Template]
		if !exists {
			return "", fmt.Errorf("template not found: %s", message.Template)
		}

		var buf bytes.Buffer
		if err := tmpl.Execute(&buf, message.Variables); err != nil {
			return "", fmt.Errorf("failed to render template: %w", err)
		}

		return buf.String(), nil
	}

	return "", fmt.Errorf("no content or template specified")
}

// isValidEmail 验证邮箱地址格式
func isValidEmail(email string) bool {
	// 简单的邮箱格式验证
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}

	if len(parts[0]) == 0 || len(parts[1]) == 0 {
		return false
	}

	if !strings.Contains(parts[1], ".") {
		return false
	}

	return true
}

// GetTemplates 获取可用模板列表
func (s *Service) GetTemplates() []string {
	templates := make([]string, 0, len(s.templates))
	for name := range s.templates {
		templates = append(templates, name)
	}
	return templates
}

// ReloadTemplates 重新加载模板
func (s *Service) ReloadTemplates() error {
	s.templates = make(map[string]*template.Template)
	return s.loadTemplates()
}
