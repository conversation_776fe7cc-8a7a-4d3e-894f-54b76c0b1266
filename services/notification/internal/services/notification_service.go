package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"github.com/godeye/notification/internal/config"
	"github.com/godeye/notification/internal/email"
	"github.com/godeye/notification/internal/models"
	"github.com/godeye/notification/internal/rules"
	"github.com/godeye/notification/internal/webhook"
)

// NotificationService 通知服务
type NotificationService struct {
	db            *gorm.DB
	redis         *redis.Client
	config        *config.Config
	emailService  *email.Service
	webhookService *webhook.Service
	rulesEngine   *rules.Engine
}

// NewNotificationService 创建通知服务
func NewNotificationService(
	db *gorm.DB,
	redis *redis.Client,
	config *config.Config,
) (*NotificationService, error) {
	// 初始化邮件服务
	emailService, err := email.NewService(&config.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to create email service: %w", err)
	}

	// 初始化Webhook服务
	webhookService := webhook.NewService(&config.Webhook)

	// 初始化规则引擎
	rulesEngine := rules.NewEngine(db, redis)

	return &NotificationService{
		db:             db,
		redis:          redis,
		config:         config,
		emailService:   emailService,
		webhookService: webhookService,
		rulesEngine:    rulesEngine,
	}, nil
}

// ProcessEvent 处理事件并发送通知
func (s *NotificationService) ProcessEvent(ctx context.Context, event *rules.Event) error {
	// 评估规则
	results, err := s.rulesEngine.EvaluateEvent(ctx, event)
	if err != nil {
		return fmt.Errorf("failed to evaluate rules: %w", err)
	}

	// 处理匹配的规则
	for _, result := range results {
		if result.Matched && !result.Throttled && result.Error == "" {
			if err := s.processMatchedRule(ctx, result.RuleID, event); err != nil {
				// 记录错误但继续处理其他规则
				fmt.Printf("Failed to process rule %s: %v\n", result.RuleID, err)
			}
		}
	}

	return nil
}

// processMatchedRule 处理匹配的规则
func (s *NotificationService) processMatchedRule(ctx context.Context, ruleID uuid.UUID, event *rules.Event) error {
	// 获取规则详情
	var rule models.NotificationRule
	err := s.db.Where("id = ?", ruleID).First(&rule).Error
	if err != nil {
		return fmt.Errorf("failed to get rule: %w", err)
	}

	// 获取通知渠道
	var channels []models.NotificationChannel
	if len(rule.Channels) > 0 {
		err = s.db.Where("id IN ? AND is_active = ?", rule.Channels, true).Find(&channels).Error
		if err != nil {
			return fmt.Errorf("failed to get channels: %w", err)
		}
	}

	// 为每个渠道创建通知
	for _, channel := range channels {
		notification := &models.Notification{
			UserID:    event.UserID,
			RuleID:    &rule.ID,
			ChannelID: channel.ID,
			Type:      channel.Type,
			Priority:  s.determinePriority(event),
			Metadata:  models.JSONB(event.Data),
		}

		// 渲染通知内容
		if err := s.renderNotification(notification, &rule, &channel, event); err != nil {
			return fmt.Errorf("failed to render notification: %w", err)
		}

		// 保存通知记录
		if err := s.db.Create(notification).Error; err != nil {
			return fmt.Errorf("failed to create notification: %w", err)
		}

		// 发送通知
		go s.sendNotificationAsync(context.Background(), notification, &channel)
	}

	return nil
}

// renderNotification 渲染通知内容
func (s *NotificationService) renderNotification(
	notification *models.Notification,
	rule *models.NotificationRule,
	channel *models.NotificationChannel,
	event *rules.Event,
) error {
	// 根据事件类型和模板生成内容
	switch event.Type {
	case "scan_result":
		return s.renderScanResultNotification(notification, rule, channel, event)
	case "task_status":
		return s.renderTaskStatusNotification(notification, rule, channel, event)
	case "system_alert":
		return s.renderSystemAlertNotification(notification, rule, channel, event)
	default:
		return s.renderGenericNotification(notification, rule, channel, event)
	}
}

// renderScanResultNotification 渲染扫描结果通知
func (s *NotificationService) renderScanResultNotification(
	notification *models.Notification,
	rule *models.NotificationRule,
	channel *models.NotificationChannel,
	event *rules.Event,
) error {
	data := event.Data

	// 设置主题
	riskLevel := getStringFromData(data, "risk_level", "medium")
	matchCount := getIntFromData(data, "match_count", 0)
	taskName := getStringFromData(data, "task_name", "未知任务")

	notification.Subject = fmt.Sprintf("🔍 GodEye 安全告警 - %s (发现 %d 个风险)", taskName, matchCount)

	// 设置收件人
	if channel.Type == "email" {
		config := channel.GetChannelConfig()
		if recipients, ok := config["recipients"].([]interface{}); ok {
			notification.Recipients = make([]string, len(recipients))
			for i, r := range recipients {
				notification.Recipients[i] = fmt.Sprintf("%v", r)
			}
		}
	}

	// 根据渠道类型设置内容
	switch channel.Type {
	case "email":
		notification.Content = s.generateEmailContent("scan_result_alert", data)
	case "webhook":
		content, _ := json.Marshal(data)
		notification.Content = string(content)
	default:
		notification.Content = fmt.Sprintf("在监控任务 %s 中发现了 %d 个潜在的安全风险", taskName, matchCount)
	}

	return nil
}

// renderTaskStatusNotification 渲染任务状态通知
func (s *NotificationService) renderTaskStatusNotification(
	notification *models.Notification,
	rule *models.NotificationRule,
	channel *models.NotificationChannel,
	event *rules.Event,
) error {
	data := event.Data

	taskName := getStringFromData(data, "task_name", "未知任务")
	status := getStringFromData(data, "status", "unknown")

	notification.Subject = fmt.Sprintf("📊 任务状态更新 - %s (%s)", taskName, status)

	// 设置收件人
	if channel.Type == "email" {
		config := channel.GetChannelConfig()
		if recipients, ok := config["recipients"].([]interface{}); ok {
			notification.Recipients = make([]string, len(recipients))
			for i, r := range recipients {
				notification.Recipients[i] = fmt.Sprintf("%v", r)
			}
		}
	}

	// 根据渠道类型设置内容
	switch channel.Type {
	case "email":
		notification.Content = s.generateEmailContent("task_status_update", data)
	case "webhook":
		content, _ := json.Marshal(data)
		notification.Content = string(content)
	default:
		notification.Content = fmt.Sprintf("任务 %s 状态已更新为: %s", taskName, status)
	}

	return nil
}

// renderSystemAlertNotification 渲染系统告警通知
func (s *NotificationService) renderSystemAlertNotification(
	notification *models.Notification,
	rule *models.NotificationRule,
	channel *models.NotificationChannel,
	event *rules.Event,
) error {
	data := event.Data

	alertType := getStringFromData(data, "alert_type", "system")
	message := getStringFromData(data, "message", "系统告警")

	notification.Subject = fmt.Sprintf("⚠️ 系统告警 - %s", alertType)

	// 设置收件人
	if channel.Type == "email" {
		config := channel.GetChannelConfig()
		if recipients, ok := config["recipients"].([]interface{}); ok {
			notification.Recipients = make([]string, len(recipients))
			for i, r := range recipients {
				notification.Recipients[i] = fmt.Sprintf("%v", r)
			}
		}
	}

	// 根据渠道类型设置内容
	switch channel.Type {
	case "email":
		notification.Content = s.generateEmailContent("system_notification", data)
	case "webhook":
		content, _ := json.Marshal(data)
		notification.Content = string(content)
	default:
		notification.Content = message
	}

	return nil
}

// renderGenericNotification 渲染通用通知
func (s *NotificationService) renderGenericNotification(
	notification *models.Notification,
	rule *models.NotificationRule,
	channel *models.NotificationChannel,
	event *rules.Event,
) error {
	notification.Subject = fmt.Sprintf("GodEye 通知 - %s", event.Type)
	
	content, _ := json.Marshal(event.Data)
	notification.Content = string(content)

	return nil
}

// sendNotificationAsync 异步发送通知
func (s *NotificationService) sendNotificationAsync(ctx context.Context, notification *models.Notification, channel *models.NotificationChannel) {
	var err error
	
	switch channel.Type {
	case "email":
		err = s.sendEmailNotification(ctx, notification, channel)
	case "webhook":
		err = s.sendWebhookNotification(ctx, notification, channel)
	default:
		err = fmt.Errorf("unsupported channel type: %s", channel.Type)
	}

	// 更新通知状态
	if err != nil {
		notification.MarkAsFailed(err.Error())
	} else {
		notification.MarkAsSent()
	}

	// 保存状态更新
	s.db.Save(notification)

	// 记录日志
	s.logNotification(notification, err)
}

// sendEmailNotification 发送邮件通知
func (s *NotificationService) sendEmailNotification(ctx context.Context, notification *models.Notification, channel *models.NotificationChannel) error {
	message := &email.EmailMessage{
		To:      notification.Recipients,
		Subject: notification.Subject,
		Content: notification.Content,
		IsHTML:  true,
	}

	result, err := s.emailService.SendEmail(message)
	if err != nil {
		return err
	}

	// 保存发送结果
	if result.Success {
		notification.Response = models.JSONB{
			"message_id": result.MessageID,
			"sent_at":    result.SentAt,
		}
	}

	return nil
}

// sendWebhookNotification 发送Webhook通知
func (s *NotificationService) sendWebhookNotification(ctx context.Context, notification *models.Notification, channel *models.NotificationChannel) error {
	config := channel.GetChannelConfig()
	webhookURL, ok := config["url"].(string)
	if !ok {
		return fmt.Errorf("webhook URL not configured")
	}

	// 解析payload
	var payload map[string]interface{}
	if err := json.Unmarshal([]byte(notification.Content), &payload); err != nil {
		return fmt.Errorf("failed to parse payload: %w", err)
	}

	// 根据Webhook类型发送
	webhookType := s.webhookService.ParseWebhookType(webhookURL)
	var result *webhook.WebhookResponse
	var err error

	switch webhookType {
	case "slack":
		slackMessage := s.webhookService.CreateSlackScanAlert(payload)
		result, err = s.webhookService.SendSlackMessage(ctx, webhookURL, slackMessage)
	case "dingtalk":
		dingMessage := s.webhookService.CreateDingTalkScanAlert(payload)
		result, err = s.webhookService.SendDingTalkMessage(ctx, webhookURL, dingMessage)
	case "wechatwork":
		wechatMessage := s.webhookService.CreateWeChatWorkScanAlert(payload)
		result, err = s.webhookService.SendWeChatWorkMessage(ctx, webhookURL, wechatMessage)
	case "feishu":
		feishuMessage := s.webhookService.CreateFeishuScanAlert(payload)
		result, err = s.webhookService.SendFeishuMessage(ctx, webhookURL, feishuMessage)
	default:
		result, err = s.webhookService.SendGenericWebhook(ctx, webhookURL, payload)
	}

	if err != nil {
		return err
	}

	// 保存发送结果
	if result.Success {
		notification.Response = models.JSONB{
			"status_code": result.StatusCode,
			"duration":    result.Duration.String(),
			"sent_at":     result.SentAt,
		}
	}

	return nil
}

// logNotification 记录通知日志
func (s *NotificationService) logNotification(notification *models.Notification, err error) {
	log := &models.NotificationLog{
		NotificationID: notification.ID,
		Action:         "sent",
		Status:         notification.Status,
	}

	if err != nil {
		log.Action = "failed"
		log.Message = err.Error()
	} else {
		log.Message = "Notification sent successfully"
	}

	s.db.Create(log)
}

// determinePriority 确定优先级
func (s *NotificationService) determinePriority(event *rules.Event) string {
	if riskLevel, ok := event.Data["risk_level"].(string); ok {
		switch riskLevel {
		case "critical":
			return "critical"
		case "high":
			return "high"
		case "medium":
			return "normal"
		case "low":
			return "low"
		}
	}
	return "normal"
}

// generateEmailContent 生成邮件内容
func (s *NotificationService) generateEmailContent(template string, data map[string]interface{}) string {
	// 这里可以使用模板引擎生成HTML内容
	// 简化实现，直接返回JSON格式
	content, _ := json.MarshalIndent(data, "", "  ")
	return string(content)
}

// SendManualNotification 发送手动通知
func (s *NotificationService) SendManualNotification(ctx context.Context, userID uuid.UUID, channelID uuid.UUID, subject, content string, recipients []string) error {
	// 获取通知渠道
	var channel models.NotificationChannel
	err := s.db.Where("id = ? AND user_id = ? AND is_active = ?", channelID, userID, true).First(&channel).Error
	if err != nil {
		return fmt.Errorf("channel not found or not accessible: %w", err)
	}

	// 创建通知记录
	notification := &models.Notification{
		UserID:     userID,
		ChannelID:  channel.ID,
		Type:       channel.Type,
		Priority:   "normal",
		Subject:    subject,
		Content:    content,
		Recipients: recipients,
	}

	// 保存通知记录
	if err := s.db.Create(notification).Error; err != nil {
		return fmt.Errorf("failed to create notification: %w", err)
	}

	// 发送通知
	go s.sendNotificationAsync(context.Background(), notification, &channel)

	return nil
}

// GetNotificationHistory 获取通知历史
func (s *NotificationService) GetNotificationHistory(ctx context.Context, userID uuid.UUID, limit, offset int) ([]models.Notification, int64, error) {
	var notifications []models.Notification
	var total int64

	// 获取总数
	err := s.db.Model(&models.Notification{}).Where("user_id = ?", userID).Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count notifications: %w", err)
	}

	// 获取通知列表
	err = s.db.Where("user_id = ?", userID).
		Preload("Rule").
		Preload("Channel").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get notifications: %w", err)
	}

	return notifications, total, nil
}

// GetNotificationStats 获取通知统计
func (s *NotificationService) GetNotificationStats(ctx context.Context, userID uuid.UUID, days int) (map[string]interface{}, error) {
	startDate := time.Now().AddDate(0, 0, -days)

	// 获取基本统计
	var totalSent, totalFailed int64
	s.db.Model(&models.Notification{}).
		Where("user_id = ? AND created_at >= ? AND status = ?", userID, startDate, "sent").
		Count(&totalSent)

	s.db.Model(&models.Notification{}).
		Where("user_id = ? AND created_at >= ? AND status = ?", userID, startDate, "failed").
		Count(&totalFailed)

	// 按渠道类型统计
	var channelStats []struct {
		Type  string `json:"type"`
		Count int64  `json:"count"`
	}
	s.db.Model(&models.Notification{}).
		Select("type, count(*) as count").
		Where("user_id = ? AND created_at >= ?", userID, startDate).
		Group("type").
		Scan(&channelStats)

	// 按日期统计
	var dailyStats []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}
	s.db.Model(&models.Notification{}).
		Select("DATE(created_at) as date, count(*) as count").
		Where("user_id = ? AND created_at >= ?", userID, startDate).
		Group("DATE(created_at)").
		Order("date").
		Scan(&dailyStats)

	return map[string]interface{}{
		"total_sent":    totalSent,
		"total_failed":  totalFailed,
		"channel_stats": channelStats,
		"daily_stats":   dailyStats,
		"success_rate":  float64(totalSent) / float64(totalSent+totalFailed) * 100,
	}, nil
}

// 辅助函数
func getStringFromData(data map[string]interface{}, key, defaultValue string) string {
	if val, ok := data[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return defaultValue
}

func getIntFromData(data map[string]interface{}, key string, defaultValue int) int {
	if val, ok := data[key]; ok {
		if i, ok := val.(int); ok {
			return i
		}
		if f, ok := val.(float64); ok {
			return int(f)
		}
	}
	return defaultValue
}
