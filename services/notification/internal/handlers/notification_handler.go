package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/godeye/notification/internal/models"
	"github.com/godeye/notification/internal/rules"
	"github.com/godeye/notification/internal/services"
	"github.com/godeye/notification/pkg/errors"
	"github.com/godeye/notification/pkg/validator"
)

// NotificationHandler 通知处理器
type NotificationHandler struct {
	notificationService *services.NotificationService
	validator          *validator.Validator
}

// NewNotificationHandler 创建通知处理器
func NewNotificationHandler(notificationService *services.NotificationService) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
		validator:          validator.New(),
	}
}

// CreateChannel 创建通知渠道
func (h *NotificationHandler) CreateChannel(c *gin.Context) {
	var req struct {
		Name        string                 `json:"name" validate:"required,max=100"`
		Type        string                 `json:"type" validate:"required,oneof=email webhook slack dingtalk"`
		Config      map[string]interface{} `json:"config" validate:"required"`
		IsDefault   bool                   `json:"is_default"`
		Description string                 `json:"description" validate:"max=500"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid request body", err.Error()))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	// 获取用户ID（从JWT中间件设置）
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	channel := &models.NotificationChannel{
		UserID:      userID.(uuid.UUID),
		Name:        req.Name,
		Type:        req.Type,
		Config:      models.JSONB(req.Config),
		IsDefault:   req.IsDefault,
		Description: req.Description,
		IsActive:    true,
	}

	// TODO: 验证配置的有效性
	if err := h.validateChannelConfig(channel); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid channel configuration", err.Error()))
		return
	}

	// 保存到数据库
	// 这里需要数据库操作，暂时省略具体实现

	c.JSON(http.StatusCreated, gin.H{
		"message": "Channel created successfully",
		"data":    channel,
	})
}

// ListChannels 获取通知渠道列表
func (h *NotificationHandler) ListChannels(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	channelType := c.Query("type")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// TODO: 从数据库获取渠道列表
	// channels, total, err := h.channelService.GetChannels(userID.(uuid.UUID), channelType, limit, offset)

	c.JSON(http.StatusOK, gin.H{
		"data": gin.H{
			"channels": []interface{}{}, // channels
			"total":    0,               // total
			"page":     page,
			"limit":    limit,
		},
	})
}

// GetChannel 获取通知渠道详情
func (h *NotificationHandler) GetChannel(c *gin.Context) {
	channelID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid channel ID", err.Error()))
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	// TODO: 从数据库获取渠道详情
	// channel, err := h.channelService.GetChannel(userID.(uuid.UUID), channelID)

	c.JSON(http.StatusOK, gin.H{
		"data": gin.H{
			"id":   channelID,
			"name": "Sample Channel",
		},
	})
}

// UpdateChannel 更新通知渠道
func (h *NotificationHandler) UpdateChannel(c *gin.Context) {
	channelID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid channel ID", err.Error()))
		return
	}

	var req struct {
		Name        string                 `json:"name" validate:"max=100"`
		Config      map[string]interface{} `json:"config"`
		IsActive    *bool                  `json:"is_active"`
		IsDefault   *bool                  `json:"is_default"`
		Description string                 `json:"description" validate:"max=500"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid request body", err.Error()))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	// TODO: 更新渠道
	// err = h.channelService.UpdateChannel(userID.(uuid.UUID), channelID, &req)

	c.JSON(http.StatusOK, gin.H{
		"message": "Channel updated successfully",
	})
}

// DeleteChannel 删除通知渠道
func (h *NotificationHandler) DeleteChannel(c *gin.Context) {
	channelID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid channel ID", err.Error()))
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	// TODO: 删除渠道
	// err = h.channelService.DeleteChannel(userID.(uuid.UUID), channelID)

	c.JSON(http.StatusOK, gin.H{
		"message": "Channel deleted successfully",
	})
}

// TestChannel 测试通知渠道
func (h *NotificationHandler) TestChannel(c *gin.Context) {
	channelID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid channel ID", err.Error()))
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	// TODO: 测试渠道连接
	// result, err := h.channelService.TestChannel(userID.(uuid.UUID), channelID)

	c.JSON(http.StatusOK, gin.H{
		"message": "Channel test completed",
		"data": gin.H{
			"success": true,
			"details": "Test notification sent successfully",
		},
	})
}

// SendManualNotification 发送手动通知
func (h *NotificationHandler) SendManualNotification(c *gin.Context) {
	var req struct {
		ChannelID  uuid.UUID `json:"channel_id" validate:"required"`
		Subject    string    `json:"subject" validate:"required,max=200"`
		Content    string    `json:"content" validate:"required"`
		Recipients []string  `json:"recipients" validate:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid request body", err.Error()))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	// 发送手动通知
	err := h.notificationService.SendManualNotification(
		c.Request.Context(),
		userID.(uuid.UUID),
		req.ChannelID,
		req.Subject,
		req.Content,
		req.Recipients,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to send notification", err.Error()))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Notification sent successfully",
	})
}

// ProcessEvent 处理事件通知
func (h *NotificationHandler) ProcessEvent(c *gin.Context) {
	var req struct {
		Type      string                 `json:"type" validate:"required"`
		UserID    uuid.UUID              `json:"user_id" validate:"required"`
		Data      map[string]interface{} `json:"data" validate:"required"`
		Source    string                 `json:"source" validate:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid request body", err.Error()))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	// 创建事件
	event := &rules.Event{
		Type:      req.Type,
		UserID:    req.UserID,
		Data:      req.Data,
		Source:    req.Source,
		Timestamp: time.Now(),
	}

	// 处理事件
	err := h.notificationService.ProcessEvent(c.Request.Context(), event)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to process event", err.Error()))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Event processed successfully",
	})
}

// GetNotificationHistory 获取通知历史
func (h *NotificationHandler) GetNotificationHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 获取通知历史
	notifications, total, err := h.notificationService.GetNotificationHistory(
		c.Request.Context(),
		userID.(uuid.UUID),
		limit,
		offset,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to get notification history", err.Error()))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": gin.H{
			"notifications": notifications,
			"total":         total,
			"page":          page,
			"limit":         limit,
		},
	})
}

// GetNotificationStats 获取通知统计
func (h *NotificationHandler) GetNotificationStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewAuthorizationError("User not authenticated"))
		return
	}

	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
	if days < 1 || days > 365 {
		days = 30
	}

	// 获取统计数据
	stats, err := h.notificationService.GetNotificationStats(
		c.Request.Context(),
		userID.(uuid.UUID),
		days,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to get notification stats", err.Error()))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}

// validateChannelConfig 验证渠道配置
func (h *NotificationHandler) validateChannelConfig(channel *models.NotificationChannel) error {
	config := channel.GetChannelConfig()

	switch channel.Type {
	case "email":
		if _, ok := config["recipients"]; !ok {
			return fmt.Errorf("email recipients are required")
		}
	case "webhook":
		if _, ok := config["url"]; !ok {
			return fmt.Errorf("webhook URL is required")
		}
	case "slack":
		if _, ok := config["webhook_url"]; !ok {
			return fmt.Errorf("slack webhook URL is required")
		}
	case "dingtalk":
		if _, ok := config["webhook_url"]; !ok {
			return fmt.Errorf("dingtalk webhook URL is required")
		}
	}

	return nil
}
