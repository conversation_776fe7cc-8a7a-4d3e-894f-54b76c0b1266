package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Environment string         `mapstructure:"environment"`
	Server      ServerConfig   `mapstructure:"server"`
	Database    DatabaseConfig `mapstructure:"database"`
	Redis       RedisConfig    `mapstructure:"redis"`
	Email       EmailConfig    `mapstructure:"email"`
	Webhook     WebhookConfig  `mapstructure:"webhook"`
	Queue       QueueConfig    `mapstructure:"queue"`
	Logging     LoggingConfig  `mapstructure:"logging"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int           `mapstructure:"port"`
	Host         string        `mapstructure:"host"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	User         string        `mapstructure:"user"`
	Password     string        `mapstructure:"password"`
	Database     string        `mapstructure:"database"`
	SSLMode      string        `mapstructure:"ssl_mode"`
	MaxOpenConns int           `mapstructure:"max_open_conns"`
	MaxIdleConns int           `mapstructure:"max_idle_conns"`
	MaxLifetime  time.Duration `mapstructure:"max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Password     string        `mapstructure:"password"`
	Database     int           `mapstructure:"database"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	SMTP     SMTPConfig     `mapstructure:"smtp"`
	Templates TemplateConfig `mapstructure:"templates"`
	Sender   SenderConfig   `mapstructure:"sender"`
	Limits   LimitsConfig   `mapstructure:"limits"`
}

// SMTPConfig SMTP配置
type SMTPConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	UseTLS   bool   `mapstructure:"use_tls"`
	UseSSL   bool   `mapstructure:"use_ssl"`
}

// TemplateConfig 模板配置
type TemplateConfig struct {
	Path     string `mapstructure:"path"`
	Language string `mapstructure:"language"`
}

// SenderConfig 发送者配置
type SenderConfig struct {
	Name  string `mapstructure:"name"`
	Email string `mapstructure:"email"`
}

// LimitsConfig 限制配置
type LimitsConfig struct {
	RateLimit    int           `mapstructure:"rate_limit"`
	BurstLimit   int           `mapstructure:"burst_limit"`
	RetryAttempts int          `mapstructure:"retry_attempts"`
	RetryDelay   time.Duration `mapstructure:"retry_delay"`
}

// WebhookConfig Webhook配置
type WebhookConfig struct {
	Timeout       time.Duration `mapstructure:"timeout"`
	RetryAttempts int           `mapstructure:"retry_attempts"`
	RetryDelay    time.Duration `mapstructure:"retry_delay"`
	MaxBodySize   int64         `mapstructure:"max_body_size"`
	UserAgent     string        `mapstructure:"user_agent"`
}

// QueueConfig 队列配置
type QueueConfig struct {
	URL          string        `mapstructure:"url"`
	Exchange     string        `mapstructure:"exchange"`
	QueueName    string        `mapstructure:"queue_name"`
	RoutingKey   string        `mapstructure:"routing_key"`
	Durable      bool          `mapstructure:"durable"`
	AutoDelete   bool          `mapstructure:"auto_delete"`
	Exclusive    bool          `mapstructure:"exclusive"`
	NoWait       bool          `mapstructure:"no_wait"`
	PrefetchCount int          `mapstructure:"prefetch_count"`
	ReconnectDelay time.Duration `mapstructure:"reconnect_delay"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
	Output string `mapstructure:"output"`
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置环境变量前缀
	viper.SetEnvPrefix("NOTIFICATION")
	viper.AutomaticEnv()

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认值
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("environment", "development")
	viper.SetDefault("server.port", 8083)
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "60s")

	// 数据库默认配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "godeye")
	viper.SetDefault("database.password", "godeye123")
	viper.SetDefault("database.database", "godeye_notification")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.max_lifetime", "5m")

	// Redis默认配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.database", 2)
	viper.SetDefault("redis.pool_size", 10)
	viper.SetDefault("redis.min_idle_conns", 2)
	viper.SetDefault("redis.dial_timeout", "5s")
	viper.SetDefault("redis.read_timeout", "3s")
	viper.SetDefault("redis.write_timeout", "3s")

	// 邮件默认配置
	viper.SetDefault("email.smtp.host", "smtp.gmail.com")
	viper.SetDefault("email.smtp.port", 587)
	viper.SetDefault("email.smtp.use_tls", true)
	viper.SetDefault("email.smtp.use_ssl", false)
	viper.SetDefault("email.templates.path", "./templates")
	viper.SetDefault("email.templates.language", "zh-CN")
	viper.SetDefault("email.sender.name", "GodEye Security")
	viper.SetDefault("email.limits.rate_limit", 100)
	viper.SetDefault("email.limits.burst_limit", 10)
	viper.SetDefault("email.limits.retry_attempts", 3)
	viper.SetDefault("email.limits.retry_delay", "5s")

	// Webhook默认配置
	viper.SetDefault("webhook.timeout", "30s")
	viper.SetDefault("webhook.retry_attempts", 3)
	viper.SetDefault("webhook.retry_delay", "5s")
	viper.SetDefault("webhook.max_body_size", 1048576) // 1MB
	viper.SetDefault("webhook.user_agent", "GodEye-Notification/1.0")

	// 队列默认配置
	viper.SetDefault("queue.url", "amqp://guest:guest@localhost:5672/")
	viper.SetDefault("queue.exchange", "godeye.notifications")
	viper.SetDefault("queue.queue_name", "notification.queue")
	viper.SetDefault("queue.routing_key", "notification")
	viper.SetDefault("queue.durable", true)
	viper.SetDefault("queue.auto_delete", false)
	viper.SetDefault("queue.exclusive", false)
	viper.SetDefault("queue.no_wait", false)
	viper.SetDefault("queue.prefetch_count", 10)
	viper.SetDefault("queue.reconnect_delay", "5s")

	// 日志默认配置
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.User, c.Password, c.Database, c.SSLMode)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetSMTPAddr 获取SMTP地址
func (c *SMTPConfig) GetSMTPAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}
