package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// JSONB 自定义JSONB类型
type JSONB map[string]interface{}

// NotificationChannel 通知渠道
type NotificationChannel struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID      `json:"user_id" gorm:"type:uuid;not null;index"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Type        string         `json:"type" gorm:"size:20;not null"` // email, webhook, slack, dingtalk, wechat
	Config      JSONB          `json:"config" gorm:"type:jsonb"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	IsDefault   bool           `json:"is_default" gorm:"default:false"`
	Description string         `json:"description" gorm:"size:500"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// NotificationRule 通知规则
type NotificationRule struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID      `json:"user_id" gorm:"type:uuid;not null;index"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Description string         `json:"description" gorm:"size:500"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	Priority    int            `json:"priority" gorm:"default:0"` // 优先级，数字越大优先级越高
	Conditions  JSONB          `json:"conditions" gorm:"type:jsonb"` // 触发条件
	Channels    []uuid.UUID    `json:"channels" gorm:"type:uuid[]"` // 通知渠道ID列表
	Template    string         `json:"template" gorm:"size:50"` // 模板名称
	Throttle    JSONB          `json:"throttle" gorm:"type:jsonb"` // 限流配置
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string         `json:"name" gorm:"size:50;not null;uniqueIndex"`
	Type        string         `json:"type" gorm:"size:20;not null"` // email, webhook, text
	Language    string         `json:"language" gorm:"size:10;default:'zh-CN'"`
	Subject     string         `json:"subject" gorm:"size:200"`
	Content     string         `json:"content" gorm:"type:text;not null"`
	Variables   []string       `json:"variables" gorm:"type:text[]"` // 可用变量列表
	IsSystem    bool           `json:"is_system" gorm:"default:false"` // 是否为系统模板
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	Description string         `json:"description" gorm:"size:500"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// Notification 通知记录
type Notification struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID      `json:"user_id" gorm:"type:uuid;not null;index"`
	RuleID      *uuid.UUID     `json:"rule_id" gorm:"type:uuid;index"` // 触发的规则ID，可为空（手动发送）
	ChannelID   uuid.UUID      `json:"channel_id" gorm:"type:uuid;not null;index"`
	Type        string         `json:"type" gorm:"size:20;not null"` // email, webhook, etc.
	Priority    string         `json:"priority" gorm:"size:10;default:'normal'"` // low, normal, high, critical
	Subject     string         `json:"subject" gorm:"size:200"`
	Content     string         `json:"content" gorm:"type:text;not null"`
	Recipients  []string       `json:"recipients" gorm:"type:text[]"` // 接收者列表
	Metadata    JSONB          `json:"metadata" gorm:"type:jsonb"` // 额外元数据
	Status      string         `json:"status" gorm:"size:20;default:'pending'"` // pending, sent, failed, cancelled
	SentAt      *time.Time     `json:"sent_at"`
	FailedAt    *time.Time     `json:"failed_at"`
	RetryCount  int            `json:"retry_count" gorm:"default:0"`
	MaxRetries  int            `json:"max_retries" gorm:"default:3"`
	ErrorMessage string        `json:"error_message" gorm:"size:1000"`
	Response    JSONB          `json:"response" gorm:"type:jsonb"` // 发送响应
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`

	// 关联
	Rule    *NotificationRule    `json:"rule,omitempty" gorm:"foreignKey:RuleID"`
	Channel *NotificationChannel `json:"channel,omitempty" gorm:"foreignKey:ChannelID"`
}

// NotificationLog 通知日志
type NotificationLog struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	NotificationID uuid.UUID `json:"notification_id" gorm:"type:uuid;not null;index"`
	Action         string    `json:"action" gorm:"size:20;not null"` // created, sent, failed, retried
	Status         string    `json:"status" gorm:"size:20;not null"`
	Message        string    `json:"message" gorm:"size:1000"`
	Details        JSONB     `json:"details" gorm:"type:jsonb"`
	CreatedAt      time.Time `json:"created_at"`

	// 关联
	Notification *Notification `json:"notification,omitempty" gorm:"foreignKey:NotificationID"`
}

// NotificationStats 通知统计
type NotificationStats struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Date        time.Time `json:"date" gorm:"type:date;not null;index"`
	ChannelType string    `json:"channel_type" gorm:"size:20;not null"`
	TotalSent   int       `json:"total_sent" gorm:"default:0"`
	TotalFailed int       `json:"total_failed" gorm:"default:0"`
	TotalRetries int      `json:"total_retries" gorm:"default:0"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 设置表名
func (NotificationChannel) TableName() string {
	return "notification_channels"
}

func (NotificationRule) TableName() string {
	return "notification_rules"
}

func (NotificationTemplate) TableName() string {
	return "notification_templates"
}

func (Notification) TableName() string {
	return "notifications"
}

func (NotificationLog) TableName() string {
	return "notification_logs"
}

func (NotificationStats) TableName() string {
	return "notification_stats"
}

// BeforeCreate 创建前钩子
func (n *NotificationChannel) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

func (n *NotificationRule) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

func (n *NotificationTemplate) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

func (n *Notification) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

func (n *NotificationLog) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

func (n *NotificationStats) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

// IsRetryable 检查是否可以重试
func (n *Notification) IsRetryable() bool {
	return n.Status == "failed" && n.RetryCount < n.MaxRetries
}

// CanSend 检查是否可以发送
func (n *Notification) CanSend() bool {
	return n.Status == "pending" || n.IsRetryable()
}

// MarkAsSent 标记为已发送
func (n *Notification) MarkAsSent() {
	now := time.Now()
	n.Status = "sent"
	n.SentAt = &now
}

// MarkAsFailed 标记为失败
func (n *Notification) MarkAsFailed(errorMsg string) {
	now := time.Now()
	n.Status = "failed"
	n.FailedAt = &now
	n.ErrorMessage = errorMsg
	n.RetryCount++
}

// GetChannelConfig 获取渠道配置
func (c *NotificationChannel) GetChannelConfig() map[string]interface{} {
	if c.Config == nil {
		return make(map[string]interface{})
	}
	return c.Config
}

// GetRuleConditions 获取规则条件
func (r *NotificationRule) GetRuleConditions() map[string]interface{} {
	if r.Conditions == nil {
		return make(map[string]interface{})
	}
	return r.Conditions
}

// GetThrottleConfig 获取限流配置
func (r *NotificationRule) GetThrottleConfig() map[string]interface{} {
	if r.Throttle == nil {
		return make(map[string]interface{})
	}
	return r.Throttle
}
