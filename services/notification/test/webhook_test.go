package test

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/godeye/notification/internal/webhook"
	"github.com/stretchr/testify/assert"
)

func TestWebhookService_ParseWebhookType(t *testing.T) {
	config := &webhook.Config{
		UserAgent: "GodEye-Webhook/1.0",
		Timeout:   30 * time.Second,
	}
	service := webhook.NewService(config)

	tests := []struct {
		name        string
		webhookURL  string
		expectedType string
	}{
		{
			name:        "Slack webhook",
			webhookURL:  "*****************************************************************************",
			expectedType: "slack",
		},
		{
			name:        "DingTalk webhook",
			webhookURL:  "https://oapi.dingtalk.com/robot/send?access_token=xxx",
			expectedType: "dingtalk",
		},
		{
			name:        "WeChat Work webhook",
			webhookURL:  "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx",
			expectedType: "wechatwork",
		},
		{
			name:        "<PERSON>ish<PERSON> webhook",
			webhookURL:  "https://open.feishu.cn/open-apis/bot/v2/hook/xxx",
			expectedType: "feishu",
		},
		{
			name:        "Lark webhook",
			webhookURL:  "https://open.larksuite.com/open-apis/bot/v2/hook/xxx",
			expectedType: "feishu",
		},
		{
			name:        "Generic webhook",
			webhookURL:  "https://example.com/webhook",
			expectedType: "generic",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.ParseWebhookType(tt.webhookURL)
			assert.Equal(t, tt.expectedType, result)
		})
	}
}

func TestWebhookService_CreateScanAlerts(t *testing.T) {
	config := &webhook.Config{
		UserAgent: "GodEye-Webhook/1.0",
		Timeout:   30 * time.Second,
	}
	service := webhook.NewService(config)

	testData := map[string]interface{}{
		"task_name":     "测试监控任务",
		"risk_level":    "high",
		"match_count":   5,
		"scan_time":     "2024-01-01 12:00:00",
		"dashboard_url": "https://godeye.example.com/dashboard",
	}

	t.Run("Create Slack alert", func(t *testing.T) {
		message := service.CreateSlackScanAlert(testData)
		assert.NotNil(t, message)
		assert.Equal(t, "GodEye", message.Username)
		assert.Equal(t, ":shield:", message.IconEmoji)
		assert.NotEmpty(t, message.Attachments)
		
		attachment := message.Attachments[0]
		assert.Equal(t, "danger", attachment.Color) // high risk = danger
		assert.Equal(t, "🔍 GodEye 安全告警", attachment.Title)
		assert.NotEmpty(t, attachment.Fields)
	})

	t.Run("Create DingTalk alert", func(t *testing.T) {
		message := service.CreateDingTalkScanAlert(testData)
		assert.NotNil(t, message)
		assert.Equal(t, "markdown", message.MsgType)
		assert.NotNil(t, message.Markdown)
		assert.Contains(t, message.Markdown.Text, "🔍 GodEye 安全告警")
		assert.Contains(t, message.Markdown.Text, "测试监控任务")
		assert.Contains(t, message.Markdown.Text, "🟠") // high risk emoji
	})

	t.Run("Create WeChat Work alert", func(t *testing.T) {
		message := service.CreateWeChatWorkScanAlert(testData)
		assert.NotNil(t, message)
		assert.Equal(t, "markdown", message.MsgType)
		assert.NotNil(t, message.Markdown)
		assert.Contains(t, message.Markdown.Content, "🔍 GodEye 安全告警")
		assert.Contains(t, message.Markdown.Content, "测试监控任务")
		assert.Contains(t, message.Markdown.Content, "🟠") // high risk emoji
	})

	t.Run("Create Feishu alert", func(t *testing.T) {
		message := service.CreateFeishuScanAlert(testData)
		assert.NotNil(t, message)
		assert.Equal(t, "interactive", message.MsgType)
		assert.NotNil(t, message.Content)
		
		// 验证卡片内容
		card, ok := message.Content["card"]
		assert.True(t, ok)
		
		cardData, _ := json.Marshal(card)
		var cardContent webhook.FeishuCardContent
		err := json.Unmarshal(cardData, &cardContent)
		assert.NoError(t, err)
		
		// 验证header
		assert.NotNil(t, cardContent.Header)
		title, ok := cardContent.Header["title"].(map[string]interface{})
		assert.True(t, ok)
		assert.Contains(t, title["content"], "🔍 GodEye 安全告警")
		assert.Equal(t, "orange", cardContent.Header["template"]) // high risk = orange
		
		// 验证elements
		assert.NotEmpty(t, cardContent.Elements)
	})
}

func TestWebhookService_RiskLevelMapping(t *testing.T) {
	config := &webhook.Config{
		UserAgent: "GodEye-Webhook/1.0",
		Timeout:   30 * time.Second,
	}
	service := webhook.NewService(config)

	riskLevels := []struct {
		level         string
		expectedColor string
		expectedEmoji string
	}{
		{"critical", "red", "🔴"},
		{"high", "orange", "🟠"},
		{"medium", "yellow", "🟡"},
		{"low", "green", "🟢"},
		{"unknown", "yellow", "🟡"}, // default
	}

	for _, risk := range riskLevels {
		t.Run("Risk level: "+risk.level, func(t *testing.T) {
			testData := map[string]interface{}{
				"task_name":     "测试任务",
				"risk_level":    risk.level,
				"match_count":   1,
				"scan_time":     "2024-01-01 12:00:00",
				"dashboard_url": "https://example.com",
			}

			// Test Slack color mapping
			slackMessage := service.CreateSlackScanAlert(testData)
			expectedSlackColor := risk.expectedColor
			if risk.level == "critical" || risk.level == "high" {
				expectedSlackColor = "danger"
			} else if risk.level == "medium" {
				expectedSlackColor = "warning"
			} else {
				expectedSlackColor = "good"
			}
			assert.Equal(t, expectedSlackColor, slackMessage.Attachments[0].Color)

			// Test DingTalk emoji
			dingMessage := service.CreateDingTalkScanAlert(testData)
			assert.Contains(t, dingMessage.Markdown.Text, risk.expectedEmoji)

			// Test WeChat Work emoji
			wechatMessage := service.CreateWeChatWorkScanAlert(testData)
			assert.Contains(t, wechatMessage.Markdown.Content, risk.expectedEmoji)

			// Test Feishu color and emoji
			feishuMessage := service.CreateFeishuScanAlert(testData)
			card, _ := feishuMessage.Content["card"]
			cardData, _ := json.Marshal(card)
			var cardContent webhook.FeishuCardContent
			json.Unmarshal(cardData, &cardContent)
			
			assert.Equal(t, risk.expectedColor, cardContent.Header["template"])
			title, _ := cardContent.Header["title"].(map[string]interface{})
			assert.Contains(t, title["content"], risk.expectedEmoji)
		})
	}
}

func TestWebhookService_MessageSerialization(t *testing.T) {
	config := &webhook.Config{
		UserAgent: "GodEye-Webhook/1.0",
		Timeout:   30 * time.Second,
	}
	service := webhook.NewService(config)

	testData := map[string]interface{}{
		"task_name":     "序列化测试",
		"risk_level":    "medium",
		"match_count":   3,
		"scan_time":     "2024-01-01 12:00:00",
		"dashboard_url": "https://example.com",
	}

	t.Run("Serialize DingTalk message", func(t *testing.T) {
		message := service.CreateDingTalkScanAlert(testData)
		data, err := json.Marshal(message)
		assert.NoError(t, err)
		assert.NotEmpty(t, data)

		var unmarshaled webhook.DingTalkMessage
		err = json.Unmarshal(data, &unmarshaled)
		assert.NoError(t, err)
		assert.Equal(t, message.MsgType, unmarshaled.MsgType)
	})

	t.Run("Serialize WeChat Work message", func(t *testing.T) {
		message := service.CreateWeChatWorkScanAlert(testData)
		data, err := json.Marshal(message)
		assert.NoError(t, err)
		assert.NotEmpty(t, data)

		var unmarshaled webhook.WeChatWorkMessage
		err = json.Unmarshal(data, &unmarshaled)
		assert.NoError(t, err)
		assert.Equal(t, message.MsgType, unmarshaled.MsgType)
	})

	t.Run("Serialize Feishu message", func(t *testing.T) {
		message := service.CreateFeishuScanAlert(testData)
		data, err := json.Marshal(message)
		assert.NoError(t, err)
		assert.NotEmpty(t, data)

		var unmarshaled webhook.FeishuMessage
		err = json.Unmarshal(data, &unmarshaled)
		assert.NoError(t, err)
		assert.Equal(t, message.MsgType, unmarshaled.MsgType)
	})
}
