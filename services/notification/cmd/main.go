package main

import (
	"log"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/godeye/notification/internal/models"
)

func main() {
	// 数据库连接
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = "host=postgres-dev user=godeye_dev password=dev123 dbname=godeye_dev port=5432 sslmode=disable TimeZone=Asia/Shanghai"
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 自动迁移
	err = db.AutoMigrate(
		&models.NotificationChannel{},
		&models.NotificationRule{},
		&models.NotificationTemplate{},
		&models.NotificationLog{},
	)
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 设置路由
	r := gin.Default()

	// 中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		c.Next()
	})

	// API 路由
	api := r.Group("/api/v1")
	{
		// 基础ping路由
		api.GET("/notification/ping", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"message": "pong",
				"service": "notification",
				"timestamp": time.Now().Unix(),
			})
		})

		// 通知渠道管理
		channels := api.Group("/notification/channels")
		{
			channels.GET("", func(c *gin.Context) {
				c.JSON(200, gin.H{"items": []interface{}{}, "total": 0})
			})
			channels.POST("", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Channel created"})
			})
		}

		// 通知发送
		notifications := api.Group("/notification")
		{
			notifications.POST("/send", func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "Notification sent"})
			})
			notifications.GET("/logs", func(c *gin.Context) {
				c.JSON(200, gin.H{"items": []interface{}{}, "total": 0})
			})
		}
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	port := os.Getenv("PORT")
	if port == "" {
		port = "8083"
	}

	log.Printf("Notification service starting on port %s", port)
	log.Fatal(r.Run(":" + port))
}
