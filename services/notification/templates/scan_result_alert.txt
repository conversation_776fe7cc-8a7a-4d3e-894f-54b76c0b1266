===============================================
🔍 GodEye 安全告警通知
===============================================

⚠️ 检测到潜在的代码泄露风险

在您的监控任务中发现了 {{.MatchCount}} 个潜在的安全风险项目。

风险级别: {{upper .RiskLevel}}

===============================================
📋 扫描详情
===============================================

任务名称: {{.TaskName}}
扫描目标: {{.TargetType}}: {{.TargetValue}}
扫描时间: {{formatTime .ScanTime}}
发现问题: {{.MatchCount}} 个匹配项

{{if .Results}}
===============================================
🔍 发现的问题
===============================================

{{range .Results}}
-------------------------------------------
仓库: {{.RepoFullName}}
文件: {{.FilePath}}
匹配类型: {{.MatchType}}
风险级别: {{upper .RiskLevel}}
{{if .MatchContent}}
匹配内容:
{{.MatchContent}}
{{end}}
文件链接: {{.FileURL}}
-------------------------------------------

{{end}}
{{end}}

===============================================
📝 建议措施
===============================================

1. 立即检查标记的文件，确认是否包含敏感信息
2. 如果确认存在泄露，请立即删除或移动敏感文件
3. 更新 .gitignore 文件，防止类似文件被提交
4. 考虑使用环境变量或配置文件管理敏感信息
5. 定期审查代码仓库的安全性

===============================================
查看完整报告: {{.DashboardURL}}
===============================================

此邮件由 GodEye 安全监控系统自动发送
如有疑问，请联系技术支持: <EMAIL>

© 2024 GodEye Security. All rights reserved.
