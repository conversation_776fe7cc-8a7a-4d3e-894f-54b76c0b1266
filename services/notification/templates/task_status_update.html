<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> 任务状态更新</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header.failed {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }
        .header.running {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        .content {
            padding: 30px;
        }
        .status-box {
            background-color: #e8f5e8;
            border: 1px solid #4CAF50;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .status-box.failed {
            background-color: #ffebee;
            border-color: #f44336;
        }
        .status-box.running {
            background-color: #e3f2fd;
            border-color: #2196F3;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-completed { background-color: #4CAF50; color: white; }
        .status-failed { background-color: #f44336; color: white; }
        .status-running { background-color: #2196F3; color: white; }
        .status-pending { background-color: #ff9800; color: white; }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .details-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
            transition: width 0.3s ease;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header {{.Status}}">
            <h1>📊 任务状态更新</h1>
            <p>{{.TaskName}} 状态已更新</p>
        </div>
        
        <div class="content">
            <div class="status-box {{.Status}}">
                <h2>任务状态: <span class="status-badge status-{{.Status}}">{{.Status}}</span></h2>
                {{if .Progress}}
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{.Progress}}%"></div>
                </div>
                <p>进度: {{.Progress}}%</p>
                {{end}}
            </div>

            <h3>📋 任务详情</h3>
            <table class="details-table">
                <tr>
                    <th>任务名称</th>
                    <td>{{.TaskName}}</td>
                </tr>
                <tr>
                    <th>任务类型</th>
                    <td>{{.TaskType}}</td>
                </tr>
                <tr>
                    <th>扫描目标</th>
                    <td>{{.TargetType}}: {{.TargetValue}}</td>
                </tr>
                <tr>
                    <th>当前状态</th>
                    <td><span class="status-badge status-{{.Status}}">{{.Status}}</span></td>
                </tr>
                <tr>
                    <th>开始时间</th>
                    <td>{{formatTime .StartTime}}</td>
                </tr>
                {{if .EndTime}}
                <tr>
                    <th>结束时间</th>
                    <td>{{formatTime .EndTime}}</td>
                </tr>
                <tr>
                    <th>运行时长</th>
                    <td>{{.Duration}}</td>
                </tr>
                {{end}}
                {{if .Results}}
                <tr>
                    <th>扫描结果</th>
                    <td>发现 {{.Results.TotalMatches}} 个匹配项</td>
                </tr>
                <tr>
                    <th>扫描统计</th>
                    <td>
                        扫描仓库: {{.Results.ScannedRepos}}/{{.Results.TotalRepos}}<br>
                        扫描文件: {{.Results.ScannedFiles}}/{{.Results.TotalFiles}}
                    </td>
                </tr>
                {{end}}
            </table>

            {{if .ErrorMessage}}
            <h3>❌ 错误信息</h3>
            <div style="background-color: #ffebee; border: 1px solid #f44336; border-radius: 6px; padding: 15px; margin: 15px 0;">
                <p style="color: #d32f2f; margin: 0;">{{.ErrorMessage}}</p>
            </div>
            {{end}}

            {{if eq .Status "completed"}}
            <h3>✅ 任务完成</h3>
            <p>您的监控任务已成功完成。{{if .Results.TotalMatches}}发现了 {{.Results.TotalMatches}} 个潜在的安全风险项目，请及时查看和处理。{{else}}未发现安全风险。{{end}}</p>
            {{else if eq .Status "failed"}}
            <h3>❌ 任务失败</h3>
            <p>您的监控任务执行失败，请检查配置或联系技术支持。</p>
            {{else if eq .Status "running"}}
            <h3>🔄 任务运行中</h3>
            <p>您的监控任务正在执行中，请耐心等待完成。</p>
            {{end}}

            <div style="text-align: center;">
                <a href="{{.DashboardURL}}" class="button">查看任务详情</a>
            </div>
        </div>

        <div class="footer">
            <p>此邮件由 GodEye 安全监控系统自动发送</p>
            <p>如有疑问，请联系 <a href="mailto:<EMAIL>">技术支持</a></p>
            <p>© 2024 GodEye Security. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
