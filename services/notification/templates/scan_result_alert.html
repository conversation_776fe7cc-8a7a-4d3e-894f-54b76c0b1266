<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>Eye 扫描结果告警</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        .content {
            padding: 30px;
        }
        .alert-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .alert-box.high {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .alert-box.critical {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            border-left: 4px solid #dc3545;
        }
        .risk-level {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .risk-low { background-color: #d4edda; color: #155724; }
        .risk-medium { background-color: #fff3cd; color: #856404; }
        .risk-high { background-color: #f8d7da; color: #721c24; }
        .risk-critical { background-color: #f8d7da; color: #721c24; border: 2px solid #dc3545; }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th,
        .details-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .details-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
        }
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 GodEye 安全告警</h1>
            <p>检测到潜在的代码泄露风险</p>
        </div>
        
        <div class="content">
            <div class="alert-box {{if eq .RiskLevel "critical"}}critical{{else if eq .RiskLevel "high"}}high{{end}}">
                <h2>⚠️ 安全告警通知</h2>
                <p>在您的监控任务中发现了 <strong>{{.MatchCount}}</strong> 个潜在的安全风险项目。</p>
                <p><strong>风险级别：</strong> <span class="risk-level risk-{{.RiskLevel}}">{{.RiskLevel}}</span></p>
            </div>

            <h3>📋 扫描详情</h3>
            <table class="details-table">
                <tr>
                    <th>任务名称</th>
                    <td>{{.TaskName}}</td>
                </tr>
                <tr>
                    <th>扫描目标</th>
                    <td>{{.TargetType}}: {{.TargetValue}}</td>
                </tr>
                <tr>
                    <th>扫描时间</th>
                    <td>{{formatTime .ScanTime}}</td>
                </tr>
                <tr>
                    <th>发现问题</th>
                    <td>{{.MatchCount}} 个匹配项</td>
                </tr>
            </table>

            {{if .Results}}
            <h3>🔍 发现的问题</h3>
            {{range .Results}}
            <div class="alert-box">
                <h4>{{.RepoFullName}}</h4>
                <p><strong>文件：</strong> {{.FilePath}}</p>
                <p><strong>匹配类型：</strong> {{.MatchType}}</p>
                <p><strong>风险级别：</strong> <span class="risk-level risk-{{.RiskLevel}}">{{.RiskLevel}}</span></p>
                {{if .MatchContent}}
                <p><strong>匹配内容：</strong></p>
                <div class="code-block">{{.MatchContent}}</div>
                {{end}}
                <p><strong>文件链接：</strong> <a href="{{.FileURL}}" target="_blank">查看文件</a></p>
            </div>
            {{end}}
            {{end}}

            <div style="text-align: center;">
                <a href="{{.DashboardURL}}" class="button">查看完整报告</a>
            </div>

            <h3>📝 建议措施</h3>
            <ul>
                <li>立即检查标记的文件，确认是否包含敏感信息</li>
                <li>如果确认存在泄露，请立即删除或移动敏感文件</li>
                <li>更新 .gitignore 文件，防止类似文件被提交</li>
                <li>考虑使用环境变量或配置文件管理敏感信息</li>
                <li>定期审查代码仓库的安全性</li>
            </ul>
        </div>

        <div class="footer">
            <p>此邮件由 GodEye 安全监控系统自动发送</p>
            <p>如有疑问，请联系 <a href="mailto:<EMAIL>">技术支持</a></p>
            <p>© 2024 GodEye Security. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
