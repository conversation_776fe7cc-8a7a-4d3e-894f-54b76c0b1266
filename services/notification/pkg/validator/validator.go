package validator

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"

	"github.com/godeye/notification/pkg/errors"
)

// Validator 验证器
type Validator struct {
	validator *validator.Validate
}

// New 创建新的验证器
func New() *Validator {
	v := validator.New()
	
	// 注册自定义验证规则
	v.RegisterValidation("email_list", validateEmailList)
	v.RegisterValidation("webhook_url", validateWebhookURL)
	v.RegisterValidation("cron_expression", validateCronExpression)
	
	// 注册字段名转换函数
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	return &Validator{
		validator: v,
	}
}

// Validate 验证结构体
func (v *Validator) Validate(s interface{}) *errors.ValidationError {
	err := v.validator.Struct(s)
	if err == nil {
		return nil
	}

	var errorMessages []string
	
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, fieldError := range validationErrors {
			errorMessages = append(errorMessages, v.formatFieldError(fieldError))
		}
	} else {
		errorMessages = append(errorMessages, err.Error())
	}

	return errors.NewValidationError(
		"Validation failed",
		strings.Join(errorMessages, "; "),
	)
}

// ValidateVar 验证单个变量
func (v *Validator) ValidateVar(field interface{}, tag string) error {
	return v.validator.Var(field, tag)
}

// formatFieldError 格式化字段错误信息
func (v *Validator) formatFieldError(fe validator.FieldError) string {
	field := fe.Field()
	tag := fe.Tag()
	param := fe.Param()

	switch tag {
	case "required":
		return fmt.Sprintf("%s 是必填字段", field)
	case "email":
		return fmt.Sprintf("%s 必须是有效的邮箱地址", field)
	case "min":
		return fmt.Sprintf("%s 最小长度为 %s", field, param)
	case "max":
		return fmt.Sprintf("%s 最大长度为 %s", field, param)
	case "len":
		return fmt.Sprintf("%s 长度必须为 %s", field, param)
	case "gte":
		return fmt.Sprintf("%s 必须大于等于 %s", field, param)
	case "lte":
		return fmt.Sprintf("%s 必须小于等于 %s", field, param)
	case "gt":
		return fmt.Sprintf("%s 必须大于 %s", field, param)
	case "lt":
		return fmt.Sprintf("%s 必须小于 %s", field, param)
	case "oneof":
		return fmt.Sprintf("%s 必须是以下值之一: %s", field, param)
	case "uuid":
		return fmt.Sprintf("%s 必须是有效的UUID格式", field)
	case "url":
		return fmt.Sprintf("%s 必须是有效的URL", field)
	case "uri":
		return fmt.Sprintf("%s 必须是有效的URI", field)
	case "alpha":
		return fmt.Sprintf("%s 只能包含字母", field)
	case "alphanum":
		return fmt.Sprintf("%s 只能包含字母和数字", field)
	case "numeric":
		return fmt.Sprintf("%s 只能包含数字", field)
	case "email_list":
		return fmt.Sprintf("%s 必须是有效的邮箱地址列表", field)
	case "webhook_url":
		return fmt.Sprintf("%s 必须是有效的Webhook URL", field)
	case "cron_expression":
		return fmt.Sprintf("%s 必须是有效的Cron表达式", field)
	default:
		return fmt.Sprintf("%s 验证失败: %s", field, tag)
	}
}

// validateEmailList 验证邮箱列表
func validateEmailList(fl validator.FieldLevel) bool {
	emails, ok := fl.Field().Interface().([]string)
	if !ok {
		return false
	}

	emailValidator := validator.New()
	for _, email := range emails {
		if err := emailValidator.Var(email, "email"); err != nil {
			return false
		}
	}

	return true
}

// validateWebhookURL 验证Webhook URL
func validateWebhookURL(fl validator.FieldLevel) bool {
	url, ok := fl.Field().Interface().(string)
	if !ok {
		return false
	}

	// 检查是否为有效的HTTP/HTTPS URL
	urlValidator := validator.New()
	if err := urlValidator.Var(url, "url"); err != nil {
		return false
	}

	// 检查协议
	return strings.HasPrefix(strings.ToLower(url), "http://") || 
		   strings.HasPrefix(strings.ToLower(url), "https://")
}

// validateCronExpression 验证Cron表达式
func validateCronExpression(fl validator.FieldLevel) bool {
	cronExpr, ok := fl.Field().Interface().(string)
	if !ok {
		return false
	}

	// 简单的Cron表达式验证
	// 标准格式: 秒 分 时 日 月 周
	parts := strings.Fields(cronExpr)
	
	// 支持5字段或6字段格式
	if len(parts) != 5 && len(parts) != 6 {
		return false
	}

	// 这里可以添加更详细的Cron表达式验证逻辑
	// 为了简化，这里只检查字段数量
	return true
}

// ValidateChannelConfig 验证通知渠道配置
func (v *Validator) ValidateChannelConfig(channelType string, config map[string]interface{}) *errors.ValidationError {
	switch channelType {
	case "email":
		return v.validateEmailConfig(config)
	case "webhook":
		return v.validateWebhookConfig(config)
	case "slack":
		return v.validateSlackConfig(config)
	case "dingtalk":
		return v.validateDingTalkConfig(config)
	default:
		return errors.NewValidationError("Invalid channel type", fmt.Sprintf("Unsupported channel type: %s", channelType))
	}
}

// validateEmailConfig 验证邮件配置
func (v *Validator) validateEmailConfig(config map[string]interface{}) *errors.ValidationError {
	recipients, ok := config["recipients"]
	if !ok {
		return errors.NewValidationError("Missing recipients", "Email recipients are required")
	}

	// 验证收件人列表
	recipientList, ok := recipients.([]interface{})
	if !ok {
		return errors.NewValidationError("Invalid recipients format", "Recipients must be an array")
	}

	if len(recipientList) == 0 {
		return errors.NewValidationError("Empty recipients", "At least one recipient is required")
	}

	// 验证每个邮箱地址
	emailValidator := validator.New()
	for i, recipient := range recipientList {
		email, ok := recipient.(string)
		if !ok {
			return errors.NewValidationError("Invalid recipient format", fmt.Sprintf("Recipient at index %d must be a string", i))
		}

		if err := emailValidator.Var(email, "email"); err != nil {
			return errors.NewValidationError("Invalid email address", fmt.Sprintf("Invalid email at index %d: %s", i, email))
		}
	}

	return nil
}

// validateWebhookConfig 验证Webhook配置
func (v *Validator) validateWebhookConfig(config map[string]interface{}) *errors.ValidationError {
	url, ok := config["url"]
	if !ok {
		return errors.NewValidationError("Missing webhook URL", "Webhook URL is required")
	}

	urlStr, ok := url.(string)
	if !ok {
		return errors.NewValidationError("Invalid URL format", "Webhook URL must be a string")
	}

	if err := v.ValidateVar(urlStr, "webhook_url"); err != nil {
		return errors.NewValidationError("Invalid webhook URL", err.Error())
	}

	return nil
}

// validateSlackConfig 验证Slack配置
func (v *Validator) validateSlackConfig(config map[string]interface{}) *errors.ValidationError {
	webhookURL, ok := config["webhook_url"]
	if !ok {
		return errors.NewValidationError("Missing Slack webhook URL", "Slack webhook URL is required")
	}

	urlStr, ok := webhookURL.(string)
	if !ok {
		return errors.NewValidationError("Invalid URL format", "Slack webhook URL must be a string")
	}

	// 验证是否为Slack webhook URL
	if !strings.Contains(strings.ToLower(urlStr), "slack.com") {
		return errors.NewValidationError("Invalid Slack URL", "URL must be a valid Slack webhook URL")
	}

	if err := v.ValidateVar(urlStr, "url"); err != nil {
		return errors.NewValidationError("Invalid Slack webhook URL", err.Error())
	}

	return nil
}

// validateDingTalkConfig 验证钉钉配置
func (v *Validator) validateDingTalkConfig(config map[string]interface{}) *errors.ValidationError {
	webhookURL, ok := config["webhook_url"]
	if !ok {
		return errors.NewValidationError("Missing DingTalk webhook URL", "DingTalk webhook URL is required")
	}

	urlStr, ok := webhookURL.(string)
	if !ok {
		return errors.NewValidationError("Invalid URL format", "DingTalk webhook URL must be a string")
	}

	// 验证是否为钉钉webhook URL
	if !strings.Contains(strings.ToLower(urlStr), "dingtalk.com") && 
	   !strings.Contains(strings.ToLower(urlStr), "oapi.dingtalk.com") {
		return errors.NewValidationError("Invalid DingTalk URL", "URL must be a valid DingTalk webhook URL")
	}

	if err := v.ValidateVar(urlStr, "url"); err != nil {
		return errors.NewValidationError("Invalid DingTalk webhook URL", err.Error())
	}

	return nil
}
