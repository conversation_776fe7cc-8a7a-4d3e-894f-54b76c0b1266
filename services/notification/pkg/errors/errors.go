package errors

import (
	"fmt"
	"net/http"
)

// AppError 应用错误接口
type AppError interface {
	Error() string
	Code() int
	Type() string
	Details() string
}

// BaseError 基础错误结构
type BaseError struct {
	ErrorCode    int    `json:"code"`
	ErrorType    string `json:"type"`
	ErrorMessage string `json:"message"`
	ErrorDetails string `json:"details,omitempty"`
}

// Error 实现error接口
func (e *BaseError) Error() string {
	return e.ErrorMessage
}

// Code 返回HTTP状态码
func (e *BaseError) Code() int {
	return e.ErrorCode
}

// Type 返回错误类型
func (e *BaseError) Type() string {
	return e.ErrorType
}

// Details 返回错误详情
func (e *BaseError) Details() string {
	return e.ErrorDetails
}

// ValidationError 验证错误
type ValidationError struct {
	*BaseError
}

// NewValidationError 创建验证错误
func NewValidationError(message, details string) *ValidationError {
	return &ValidationError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusBadRequest,
			ErrorType:    "VALIDATION_ERROR",
			ErrorMessage: message,
			ErrorDetails: details,
		},
	}
}

// AuthorizationError 授权错误
type AuthorizationError struct {
	*BaseError
}

// NewAuthorizationError 创建授权错误
func NewAuthorizationError(message string) *AuthorizationError {
	return &AuthorizationError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusUnauthorized,
			ErrorType:    "AUTHORIZATION_ERROR",
			ErrorMessage: message,
		},
	}
}

// ForbiddenError 禁止访问错误
type ForbiddenError struct {
	*BaseError
}

// NewForbiddenError 创建禁止访问错误
func NewForbiddenError(message string) *ForbiddenError {
	return &ForbiddenError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusForbidden,
			ErrorType:    "FORBIDDEN_ERROR",
			ErrorMessage: message,
		},
	}
}

// NotFoundError 资源未找到错误
type NotFoundError struct {
	*BaseError
}

// NewNotFoundError 创建资源未找到错误
func NewNotFoundError(resource string) *NotFoundError {
	return &NotFoundError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusNotFound,
			ErrorType:    "NOT_FOUND_ERROR",
			ErrorMessage: fmt.Sprintf("%s not found", resource),
		},
	}
}

// ConflictError 冲突错误
type ConflictError struct {
	*BaseError
}

// NewConflictError 创建冲突错误
func NewConflictError(message string) *ConflictError {
	return &ConflictError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusConflict,
			ErrorType:    "CONFLICT_ERROR",
			ErrorMessage: message,
		},
	}
}

// InternalError 内部服务器错误
type InternalError struct {
	*BaseError
}

// NewInternalError 创建内部服务器错误
func NewInternalError(message, details string) *InternalError {
	return &InternalError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusInternalServerError,
			ErrorType:    "INTERNAL_ERROR",
			ErrorMessage: message,
			ErrorDetails: details,
		},
	}
}

// ServiceUnavailableError 服务不可用错误
type ServiceUnavailableError struct {
	*BaseError
}

// NewServiceUnavailableError 创建服务不可用错误
func NewServiceUnavailableError(service string) *ServiceUnavailableError {
	return &ServiceUnavailableError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusServiceUnavailable,
			ErrorType:    "SERVICE_UNAVAILABLE_ERROR",
			ErrorMessage: fmt.Sprintf("%s service is currently unavailable", service),
		},
	}
}

// RateLimitError 限流错误
type RateLimitError struct {
	*BaseError
}

// NewRateLimitError 创建限流错误
func NewRateLimitError(message string) *RateLimitError {
	return &RateLimitError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusTooManyRequests,
			ErrorType:    "RATE_LIMIT_ERROR",
			ErrorMessage: message,
		},
	}
}

// BadRequestError 错误请求错误
type BadRequestError struct {
	*BaseError
}

// NewBadRequestError 创建错误请求错误
func NewBadRequestError(message string) *BadRequestError {
	return &BadRequestError{
		BaseError: &BaseError{
			ErrorCode:    http.StatusBadRequest,
			ErrorType:    "BAD_REQUEST_ERROR",
			ErrorMessage: message,
		},
	}
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Success bool   `json:"success"`
	Error   *BaseError `json:"error"`
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(err AppError) *ErrorResponse {
	baseErr := &BaseError{
		ErrorCode:    err.Code(),
		ErrorType:    err.Type(),
		ErrorMessage: err.Error(),
		ErrorDetails: err.Details(),
	}

	return &ErrorResponse{
		Success: false,
		Error:   baseErr,
	}
}

// WrapError 包装标准错误为应用错误
func WrapError(err error, message string) *InternalError {
	details := ""
	if err != nil {
		details = err.Error()
	}
	return NewInternalError(message, details)
}

// IsAppError 检查是否为应用错误
func IsAppError(err error) bool {
	_, ok := err.(AppError)
	return ok
}

// GetAppError 获取应用错误
func GetAppError(err error) AppError {
	if appErr, ok := err.(AppError); ok {
		return appErr
	}
	return NewInternalError("Internal server error", err.Error())
}
