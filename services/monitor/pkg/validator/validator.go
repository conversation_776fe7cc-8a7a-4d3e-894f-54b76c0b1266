package validator

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/godeye/monitor/pkg/errors"
)

// Validator 验证器
type Validator struct {
	validate *validator.Validate
}

// ValidationError 验证错误详情
type ValidationError struct {
	Field   string `json:"field"`
	Tag     string `json:"tag"`
	Value   string `json:"value"`
	Message string `json:"message"`
}

// New 创建新的验证器
func New() *Validator {
	validate := validator.New()
	
	// 注册自定义标签名
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	// 注册自定义验证规则
	registerCustomValidations(validate)

	return &Validator{
		validate: validate,
	}
}

// Validate 验证结构体
func (v *Validator) Validate(s interface{}) error {
	err := v.validate.Struct(s)
	if err == nil {
		return nil
	}

	var validationErrors []ValidationError
	
	if validationErrs, ok := err.(validator.ValidationErrors); ok {
		for _, validationErr := range validationErrs {
			validationErrors = append(validationErrors, ValidationError{
				Field:   validationErr.Field(),
				Tag:     validationErr.Tag(),
				Value:   fmt.Sprintf("%v", validationErr.Value()),
				Message: getErrorMessage(validationErr),
			})
		}
	}

	return errors.NewValidationError("Validation failed", validationErrors)
}

// registerCustomValidations 注册自定义验证规则
func registerCustomValidations(validate *validator.Validate) {
	// 注册目标类型验证
	validate.RegisterValidation("target_type", validateTargetType)
	
	// 注册调度类型验证
	validate.RegisterValidation("schedule_type", validateScheduleType)
	
	// 注册风险级别验证
	validate.RegisterValidation("risk_level", validateRiskLevel)
	
	// 注册状态验证
	validate.RegisterValidation("status", validateStatus)
}

// validateTargetType 验证目标类型
func validateTargetType(fl validator.FieldLevel) bool {
	targetType := fl.Field().String()
	validTypes := []string{"repository", "organization", "user", "platform"}

	for _, validType := range validTypes {
		if targetType == validType {
			return true
		}
	}
	return false
}

// validateScheduleType 验证调度类型
func validateScheduleType(fl validator.FieldLevel) bool {
	scheduleType := fl.Field().String()
	validTypes := []string{"manual", "interval", "cron"}
	
	for _, validType := range validTypes {
		if scheduleType == validType {
			return true
		}
	}
	return false
}

// validateRiskLevel 验证风险级别
func validateRiskLevel(fl validator.FieldLevel) bool {
	riskLevel := fl.Field().String()
	validLevels := []string{"low", "medium", "high", "critical"}
	
	for _, validLevel := range validLevels {
		if riskLevel == validLevel {
			return true
		}
	}
	return false
}

// validateStatus 验证状态
func validateStatus(fl validator.FieldLevel) bool {
	status := fl.Field().String()
	validStatuses := []string{"pending", "running", "completed", "failed", "paused", "cancelled"}
	
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// getErrorMessage 获取错误消息
func getErrorMessage(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s 是必填字段", fe.Field())
	case "min":
		return fmt.Sprintf("%s 最小值为 %s", fe.Field(), fe.Param())
	case "max":
		return fmt.Sprintf("%s 最大值为 %s", fe.Field(), fe.Param())
	case "email":
		return fmt.Sprintf("%s 必须是有效的邮箱地址", fe.Field())
	case "len":
		return fmt.Sprintf("%s 长度必须为 %s", fe.Field(), fe.Param())
	case "oneof":
		return fmt.Sprintf("%s 必须是以下值之一: %s", fe.Field(), fe.Param())
	case "target_type":
		return fmt.Sprintf("%s 必须是 repository、organization 或 user", fe.Field())
	case "schedule_type":
		return fmt.Sprintf("%s 必须是 manual、interval 或 cron", fe.Field())
	case "risk_level":
		return fmt.Sprintf("%s 必须是 low、medium、high 或 critical", fe.Field())
	case "status":
		return fmt.Sprintf("%s 必须是有效的状态值", fe.Field())
	case "uuid":
		return fmt.Sprintf("%s 必须是有效的UUID格式", fe.Field())
	case "url":
		return fmt.Sprintf("%s 必须是有效的URL", fe.Field())
	case "alphanum":
		return fmt.Sprintf("%s 只能包含字母和数字", fe.Field())
	case "alpha":
		return fmt.Sprintf("%s 只能包含字母", fe.Field())
	case "numeric":
		return fmt.Sprintf("%s 只能包含数字", fe.Field())
	case "gte":
		return fmt.Sprintf("%s 必须大于等于 %s", fe.Field(), fe.Param())
	case "lte":
		return fmt.Sprintf("%s 必须小于等于 %s", fe.Field(), fe.Param())
	case "gt":
		return fmt.Sprintf("%s 必须大于 %s", fe.Field(), fe.Param())
	case "lt":
		return fmt.Sprintf("%s 必须小于 %s", fe.Field(), fe.Param())
	default:
		return fmt.Sprintf("%s 验证失败", fe.Field())
	}
}
