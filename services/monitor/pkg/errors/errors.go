package errors

import (
	"fmt"
	"net/http"
)

// AppError 应用错误接口
type AppError interface {
	Error() string
	Code() int
	Type() string
	Details() interface{}
}

// BaseError 基础错误结构
type BaseError struct {
	Message    string      `json:"message"`
	StatusCode int         `json:"status_code"`
	ErrorType  string      `json:"type"`
	ErrorDetails interface{} `json:"details,omitempty"`
}

func (e *BaseError) Error() string {
	return e.Message
}

func (e *BaseError) Code() int {
	return e.StatusCode
}

func (e *BaseError) Type() string {
	return e.ErrorType
}

func (e *BaseError) Details() interface{} {
	return e.ErrorDetails
}

// ValidationError 验证错误
type ValidationError struct {
	*BaseError
}

// NewValidationError 创建验证错误
func NewValidationError(message string, details interface{}) *ValidationError {
	return &ValidationError{
		BaseError: &BaseError{
			Message:      message,
			StatusCode:   http.StatusBadRequest,
			ErrorType:    "validation_error",
			ErrorDetails: details,
		},
	}
}

// UnauthorizedError 未授权错误
type UnauthorizedError struct {
	*BaseError
}

// NewUnauthorizedError 创建未授权错误
func NewUnauthorizedError(message string) *UnauthorizedError {
	return &UnauthorizedError{
		BaseError: &BaseError{
			Message:    message,
			StatusCode: http.StatusUnauthorized,
			ErrorType:  "unauthorized_error",
		},
	}
}

// ForbiddenError 禁止访问错误
type ForbiddenError struct {
	*BaseError
}

// NewForbiddenError 创建禁止访问错误
func NewForbiddenError(message string) *ForbiddenError {
	return &ForbiddenError{
		BaseError: &BaseError{
			Message:    message,
			StatusCode: http.StatusForbidden,
			ErrorType:  "forbidden_error",
		},
	}
}

// NotFoundError 未找到错误
type NotFoundError struct {
	*BaseError
}

// NewNotFoundError 创建未找到错误
func NewNotFoundError(message string) *NotFoundError {
	return &NotFoundError{
		BaseError: &BaseError{
			Message:    message,
			StatusCode: http.StatusNotFound,
			ErrorType:  "not_found_error",
		},
	}
}

// ConflictError 冲突错误
type ConflictError struct {
	*BaseError
}

// NewConflictError 创建冲突错误
func NewConflictError(message string, details interface{}) *ConflictError {
	return &ConflictError{
		BaseError: &BaseError{
			Message:      message,
			StatusCode:   http.StatusConflict,
			ErrorType:    "conflict_error",
			ErrorDetails: details,
		},
	}
}

// InternalError 内部错误
type InternalError struct {
	*BaseError
}

// NewInternalError 创建内部错误
func NewInternalError(message string, err error) *InternalError {
	details := ""
	if err != nil {
		details = err.Error()
	}
	
	return &InternalError{
		BaseError: &BaseError{
			Message:      message,
			StatusCode:   http.StatusInternalServerError,
			ErrorType:    "internal_error",
			ErrorDetails: details,
		},
	}
}

// RateLimitError 限流错误
type RateLimitError struct {
	*BaseError
}

// NewRateLimitError 创建限流错误
func NewRateLimitError(message string) *RateLimitError {
	return &RateLimitError{
		BaseError: &BaseError{
			Message:    message,
			StatusCode: http.StatusTooManyRequests,
			ErrorType:  "rate_limit_error",
		},
	}
}

// ServiceUnavailableError 服务不可用错误
type ServiceUnavailableError struct {
	*BaseError
}

// NewServiceUnavailableError 创建服务不可用错误
func NewServiceUnavailableError(message string) *ServiceUnavailableError {
	return &ServiceUnavailableError{
		BaseError: &BaseError{
			Message:    message,
			StatusCode: http.StatusServiceUnavailable,
			ErrorType:  "service_unavailable_error",
		},
	}
}

// WrapError 包装错误
func WrapError(err error, message string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", message, err)
}

// IsAppError 检查是否为应用错误
func IsAppError(err error) bool {
	_, ok := err.(AppError)
	return ok
}

// GetAppError 获取应用错误
func GetAppError(err error) AppError {
	if appErr, ok := err.(AppError); ok {
		return appErr
	}
	return NewInternalError("Internal server error", err)
}
