package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/godeye/monitor/internal/config"
	"github.com/godeye/monitor/internal/github"
	"github.com/godeye/monitor/internal/middleware"
	"github.com/godeye/monitor/internal/handlers"
	"github.com/godeye/monitor/internal/models"
	"github.com/godeye/monitor/internal/scanner"
	"github.com/godeye/monitor/internal/scheduler"
	"github.com/godeye/monitor/internal/services"
	"github.com/godeye/monitor/pkg/validator"
)

func main() {
	log.Println("Starting monitor service main function v3...")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	log.Println("Config loaded successfully")
	log.Printf("Database config: host=%s, port=%d, database=%s, user=%s",
		cfg.Database.Host, cfg.Database.Port, cfg.Database.Database, cfg.Database.User)

	// 初始化数据库
	db, err := initDatabase(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 初始化Redis
	redisClient, err := initRedis(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize Redis: %v", err)
	}

	// 初始化GitHub客户端
	githubClient, err := initGitHubClient(cfg)
	if err != nil {
		log.Printf("Failed to initialize GitHub client: %v", err)
		// 在没有GitHub token的情况下，创建一个空的客户端用于测试
		githubClient = nil
	}

	// 初始化扫描引擎
	scanEngine := initScanEngine(githubClient, cfg)

	// 初始化服务
	monitorService := services.NewMonitorService(db, redisClient, githubClient, scanEngine)
	scanService := services.NewScanService(db, redisClient, githubClient, scanEngine)
	resultService := services.NewResultService(db, redisClient)

	// 初始化调度器
	schedulerConfig := scheduler.Config{
		CheckInterval:     time.Minute,
		MaxConcurrentJobs: 10,
		JobTimeout:        time.Hour,
	}
	taskScheduler := scheduler.NewScheduler(db, redisClient, scanService, schedulerConfig)

	// 初始化验证器
	validatorInstance := validator.New()

	// 初始化处理器
	monitorHandler := handlers.NewMonitorHandler(monitorService, validatorInstance)
	resultHandler := handlers.NewResultHandler(resultService, validatorInstance)
	schedulerHandler := handlers.NewSchedulerHandler(taskScheduler)

	// 设置Gin模式
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// 启动调度器
	if err := taskScheduler.Start(); err != nil {
		log.Fatalf("Failed to start scheduler: %v", err)
	}

	// 创建路由
	router := setupRoutes(monitorHandler, resultHandler, schedulerHandler)

	// 打印路由数量
	routes := router.Routes()
	log.Printf("Registered %d routes", len(routes))
	log.Printf("Starting monitor service on port %d", cfg.Server.Port)
	log.Println("Service initialization complete")

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// 启动服务器
	go func() {
		log.Printf("Starting monitor service on port %d", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down monitor service...")

	// 停止调度器
	if err := taskScheduler.Stop(); err != nil {
		log.Printf("Failed to stop scheduler: %v", err)
	}

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Monitor service stopped")
}

// initDatabase 初始化数据库
func initDatabase(cfg *config.Config) (*gorm.DB, error) {
	// 配置日志级别
	logLevel := logger.Silent
	if cfg.Environment == "development" {
		logLevel = logger.Info
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(cfg.Database.GetDSN()), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 设置连接池
	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.Database.MaxLifetime)

	// 自动迁移
	if err := db.AutoMigrate(
		&models.MonitorTask{},
		&models.ScanRun{},
		&models.ScanResult{},
		&models.Repository{},
		&models.ScanKeyword{},
	); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	return db, nil
}

// initRedis 初始化Redis
func initRedis(cfg *config.Config) (*redis.Client, error) {
	client := redis.NewClient(&redis.Options{
		Addr:         cfg.Redis.GetRedisAddr(),
		Password:     cfg.Redis.Password,
		DB:           cfg.Redis.Database,
		PoolSize:     cfg.Redis.PoolSize,
		MinIdleConns: cfg.Redis.MinIdleConns,
		DialTimeout:  cfg.Redis.DialTimeout,
		ReadTimeout:  cfg.Redis.ReadTimeout,
		WriteTimeout: cfg.Redis.WriteTimeout,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return client, nil
}

// initGitHubClient 初始化GitHub客户端
func initGitHubClient(cfg *config.Config) (*github.Client, error) {
	githubConfig := github.Config{
		Token:          cfg.GitHub.Token,
		BaseURL:        cfg.GitHub.BaseURL,
		UploadURL:      cfg.GitHub.UploadURL,
		RateLimit:      cfg.GitHub.RateLimit,
		MaxRetries:     cfg.GitHub.RetryAttempts,
		RetryDelay:     cfg.GitHub.RetryDelay,
		RequestTimeout: cfg.GitHub.RequestTimeout,
	}

	return github.NewClient(githubConfig)
}

// initScanEngine 初始化扫描引擎
func initScanEngine(githubClient *github.Client, cfg *config.Config) *scanner.Engine {
	scanConfig := scanner.Config{
		MaxFileSize:     cfg.Scanner.MaxFileSize,
		MaxFiles:        cfg.Scanner.MaxFiles,
		ScanTimeout:     cfg.Scanner.ScanTimeout,
		WorkerCount:     cfg.Scanner.WorkerCount,
		BatchSize:       cfg.Scanner.BatchSize,
		ExcludePatterns: cfg.Scanner.ExcludePatterns,
		IncludeExts:     cfg.Scanner.IncludeExts,
	}

	return scanner.NewEngine(githubClient, scanConfig)
}

// setupRoutes 设置路由
func setupRoutes(monitorHandler *handlers.MonitorHandler, resultHandler *handlers.ResultHandler, schedulerHandler *handlers.SchedulerHandler) *gin.Engine {
	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	log.Println("Setting up routes...")
	log.Printf("Handlers: monitor=%v, result=%v, scheduler=%v", monitorHandler != nil, resultHandler != nil, schedulerHandler != nil)

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"service":   "monitor",
			"timestamp": time.Now().Unix(),
		})
	})

	// 简单ping路由测试
	log.Println("Registering /ping route")
	router.GET("/ping", func(c *gin.Context) {
		log.Println("Ping route called!")
		c.JSON(http.StatusOK, gin.H{
			"message": "pong",
			"service": "monitor",
			"timestamp": time.Now().Unix(),
		})
	})

	// API ping路由
	log.Println("Registering /api/v1/monitor/ping route")
	router.GET("/api/v1/monitor/ping", func(c *gin.Context) {
		log.Println("API ping route called!")
		c.JSON(http.StatusOK, gin.H{
			"message": "pong",
			"service": "monitor",
			"timestamp": time.Now().Unix(),
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	v1.Use(middleware.Auth()) // 重新启用认证中间件
	{
		// 监控任务路由
		tasks := v1.Group("/monitor/tasks")
		{
			tasks.POST("", monitorHandler.CreateTask)
			tasks.GET("", monitorHandler.ListTasks)
			tasks.GET("/:id", monitorHandler.GetTask)
			tasks.PUT("/:id", monitorHandler.UpdateTask)
			tasks.DELETE("/:id", monitorHandler.DeleteTask)
		}

		// 扫描结果路由
		results := v1.Group("/monitor/results")
		{
			results.GET("", resultHandler.ListResults)
			results.GET("/:id", resultHandler.GetResult)
			results.PUT("/:id", resultHandler.UpdateResult)
			results.PUT("/batch", resultHandler.BatchUpdateResults)
			results.DELETE("/:id", resultHandler.DeleteResult)
			results.GET("/stats", resultHandler.GetResultStats)
			results.GET("/duplicates", resultHandler.FindDuplicates)
		}

		// 调度器路由
		scheduler := v1.Group("/monitor/scheduler")
		{
			scheduler.GET("/status", schedulerHandler.GetSchedulerStatus)
			scheduler.POST("/trigger/:id", schedulerHandler.TriggerTask)
			scheduler.GET("/tasks", schedulerHandler.GetScheduledTasks)
			scheduler.GET("/running", schedulerHandler.GetRunningTasks)
			scheduler.GET("/tasks/:id/status", schedulerHandler.GetTaskStatus)
			scheduler.POST("/start", schedulerHandler.StartScheduler)
			scheduler.POST("/stop", schedulerHandler.StopScheduler)
		}

		// 扫描结果路由（简化路径）
		v1.GET("/results", resultHandler.ListResults)
		v1.GET("/results/:id", resultHandler.GetResult)
		v1.PUT("/results/:id", resultHandler.UpdateResult)
		v1.PUT("/results/batch", resultHandler.BatchUpdateResults)
		v1.DELETE("/results/:id", resultHandler.DeleteResult)
		v1.GET("/results/stats", resultHandler.GetResultStats)
	}

	return router
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
