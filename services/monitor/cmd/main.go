package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/adapters"
	"github.com/godeye/monitor/internal/github"
	"github.com/godeye/monitor/internal/handlers"
	"github.com/godeye/monitor/internal/middleware"
	"github.com/godeye/monitor/internal/models"
	"github.com/godeye/monitor/internal/scanner"
	"github.com/godeye/monitor/internal/search"
	"github.com/godeye/monitor/internal/services"
	"github.com/godeye/monitor/pkg/validator"
	"github.com/redis/go-redis/v9"
)

func main() {
	// 初始化数据库连接
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 自动迁移数据库
	if err := migrateDatabase(db); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化适配器管理器
	adapterManager := initAdapterManager()

	// 初始化搜索调度器
	scheduler := search.NewSearchScheduler(db, adapterManager, 5) // 5个工作协程

	// 启动搜索调度器
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := scheduler.Start(ctx); err != nil {
		log.Fatalf("Failed to start search scheduler: %v", err)
	}
	defer scheduler.Stop()

	// 初始化Redis客户端
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "redis:6379",
		Password: "",
		DB:       0,
	})

	// 初始化GitHub客户端
	githubConfig := github.Config{
		Token:          os.Getenv("GITHUB_TOKEN"),
		MaxRetries:     3,
		RetryDelay:     time.Second * 2,
		RequestTimeout: time.Second * 30,
	}
	githubClient, err := github.NewClient(githubConfig)
	if err != nil {
		log.Printf("Warning: Failed to initialize GitHub client: %v", err)
		// 创建一个空的客户端用于测试
		githubClient = nil
	}

	// 初始化扫描引擎
	scannerConfig := scanner.Config{
		MaxFileSize:     1024 * 1024, // 1MB
		MaxFiles:        1000,
		ScanTimeout:     time.Minute * 5,
		WorkerCount:     10,
		BatchSize:       50,
		ExcludePatterns: []string{".git", "node_modules", "vendor"},
		IncludeExts:     []string{".go", ".js", ".py", ".java", ".cpp", ".c", ".h"},
	}
	scanEngine := scanner.NewEngine(githubClient, scannerConfig)

	// 初始化服务
	monitorService := services.NewMonitorService(db, redisClient, githubClient, scanEngine)
	globalSearchService := services.NewGlobalSearchService(db, scheduler)
	accountService := services.NewAccountService(db)
	accountService.SetAdapterManager(adapterManager) // 设置适配器管理器
	whitelistService := services.NewWhitelistService(db)

	// 初始化验证器
	validatorInstance := validator.New()

	// 初始化处理器
	monitorHandler := handlers.NewMonitorHandler(monitorService, validatorInstance)
	globalSearchHandler := handlers.NewGlobalSearchHandler(globalSearchService)
	accountHandler := handlers.NewAccountHandler(accountService)
	whitelistHandler := handlers.NewWhitelistHandler(whitelistService)
	dashboardHandler := handlers.NewDashboardHandler(db)

	// 设置路由
	router := setupRouter(monitorHandler, globalSearchHandler, accountHandler, whitelistHandler, dashboardHandler)

	// 启动HTTP服务器
	server := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	// 优雅关闭
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		log.Println("Shutting down server...")
		cancel() // 取消上下文，停止搜索调度器

		shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer shutdownCancel()

		if err := server.Shutdown(shutdownCtx); err != nil {
			log.Printf("Server shutdown error: %v", err)
		}
	}()

	log.Println("Monitor service starting on :8080")
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Server failed to start: %v", err)
	}

	log.Println("Monitor service stopped")
}

// initDatabase 初始化数据库连接
func initDatabase() (*gorm.DB, error) {
	dsn := os.Getenv("DATABASE_URL")
	if dsn == "" {
		dsn = "host=postgres-dev user=godeye_dev password=dev123 dbname=godeye_dev port=5432 sslmode=disable TimeZone=UTC"
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return db, nil
}

// migrateDatabase 数据库迁移
func migrateDatabase(db *gorm.DB) error {
	return db.AutoMigrate(
		&models.MonitorTask{},
		&models.ScanRun{},
		&models.ScanResult{},
		&models.ScanKeyword{},
		&models.PlatformAccount{},
		&models.GlobalSearchTask{},
		&models.GlobalSearchRun{},
		&models.GlobalSearchResult{},
		&models.Whitelist{},
	)
}

// initAdapterManager 初始化适配器管理器
func initAdapterManager() *adapters.AdapterManager {
	manager := adapters.NewAdapterManager()

	// 注册平台适配器
	manager.RegisterAdapter("github", adapters.NewGitHubAdapter())
	manager.RegisterAdapter("gitlab", adapters.NewGitLabAdapter())
	manager.RegisterAdapter("gitee", adapters.NewGiteeAdapter())

	return manager
}

// setupRouter 设置路由
func setupRouter(monitorHandler *handlers.MonitorHandler, globalSearchHandler *handlers.GlobalSearchHandler, accountHandler *handlers.AccountHandler, whitelistHandler *handlers.WhitelistHandler, dashboardHandler *handlers.DashboardHandler) *gin.Engine {
	// 设置Gin模式
	if os.Getenv("GIN_MODE") == "" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"service":   "monitor",
			"timestamp": time.Now().Unix(),
		})
	})

	// API路由组 - 需要认证
	api := router.Group("/api/v1")
	api.Use(middleware.Auth()) // 添加JWT认证中间件
	{
		// 监控任务管理
		monitors := api.Group("/monitors")
		{
			monitors.GET("", monitorHandler.ListTasks)
			monitors.POST("", monitorHandler.CreateTask)
			monitors.GET("/:id", monitorHandler.GetTask)
			monitors.PUT("/:id", monitorHandler.UpdateTask)
			monitors.DELETE("/:id", monitorHandler.DeleteTask)
		}

		// 全量搜索管理
		globalSearch := api.Group("/global-search")
		{
			globalSearch.GET("/tasks", globalSearchHandler.ListTasks)
			globalSearch.POST("/tasks", globalSearchHandler.CreateTask)
			globalSearch.GET("/tasks/:id", globalSearchHandler.GetTask)
			globalSearch.PUT("/tasks/:id", globalSearchHandler.UpdateTask)
			globalSearch.DELETE("/tasks/:id", globalSearchHandler.DeleteTask)
			globalSearch.POST("/tasks/:id/start", globalSearchHandler.StartTask)
			globalSearch.POST("/tasks/:id/stop", globalSearchHandler.StopTask)
			globalSearch.GET("/tasks/:id/runs", globalSearchHandler.ListRuns)
			globalSearch.GET("/tasks/:id/runs/:runId", globalSearchHandler.GetRun)
			globalSearch.GET("/tasks/:id/results", globalSearchHandler.ListResults)
			globalSearch.POST("/search", globalSearchHandler.QuickSearch)
		}

		// 平台账号管理
		accounts := api.Group("/accounts")
		{
			accounts.GET("", accountHandler.ListAccounts)
			accounts.POST("", accountHandler.CreateAccount)
			accounts.GET("/:id", accountHandler.GetAccount)
			accounts.PUT("/:id", accountHandler.UpdateAccount)
			accounts.DELETE("/:id", accountHandler.DeleteAccount)
			accounts.POST("/:id/validate", accountHandler.ValidateAccount)
			accounts.GET("/:id/rate-limit", accountHandler.GetRateLimit)
			accounts.GET("/stats", accountHandler.GetAccountStats)
		}

		// 白名单管理
		whitelist := api.Group("/whitelist")
		{
			whitelist.GET("", whitelistHandler.ListWhitelists)
			whitelist.POST("", whitelistHandler.CreateWhitelist)
			whitelist.GET("/:id", whitelistHandler.GetWhitelist)
			whitelist.PUT("/:id", whitelistHandler.UpdateWhitelist)
			whitelist.DELETE("/:id", whitelistHandler.DeleteWhitelist)
			whitelist.GET("/check", whitelistHandler.CheckWhitelist)
		}

		// 关键词和模式管理功能暂时移除，将在后续版本中实现

		// 统计和报告
		stats := api.Group("/stats")
		{
			stats.GET("/platform-usage", accountHandler.GetPlatformUsageStats)
			stats.GET("/search-trends", globalSearchHandler.GetSearchTrends)
		}

		// 仪表板统计
		dashboard := api.Group("/dashboard")
		{
			dashboard.GET("/stats", dashboardHandler.GetOverallStats)
			dashboard.GET("/trends", dashboardHandler.GetTrendData)
			dashboard.GET("/risk-distribution", dashboardHandler.GetRiskDistribution)
			dashboard.GET("/platform-stats", dashboardHandler.GetPlatformStats)
			dashboard.GET("/recent-activity", dashboardHandler.GetRecentActivity)
			dashboard.GET("/task-stats", dashboardHandler.GetTaskStats)
		}

		// 系统管理
		system := api.Group("/system")
		{
			system.GET("/platforms", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"success": true,
					"data": gin.H{
						"platforms": []string{"github", "gitlab", "gitee"},
					},
				})
			})
		}
	}

	return router
}
