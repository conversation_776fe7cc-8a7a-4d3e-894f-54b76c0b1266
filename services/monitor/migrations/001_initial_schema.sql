-- 创建UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建监控任务表
CREATE TABLE IF NOT EXISTS monitor_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    target_type VARCHAR(50) NOT NULL, -- repository, user, organization, topic
    target_value VARCHAR(255) NOT NULL,
    platform VARCHAR(50) NOT NULL, -- github, gitlab, gitee
    keywords JSONB,
    patterns JSONB,
    exclude_rules JSONB,
    schedule JSONB,
    is_active BOOLEAN DEFAULT false,
    status VARCHAR(50) DEFAULT 'created', -- created, active, paused, stopped, error
    priority INTEGER DEFAULT 5,
    last_run_at TIMESTAMP WITH TIME ZONE,
    next_run_at TIMESTAMP WITH TIME ZONE,
    config <PERSON><PERSON><PERSON><PERSON>,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- 创建扫描运行记录表
CREATE TABLE IF NOT EXISTS scan_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES monitor_tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    status VARCHAR(50) DEFAULT 'running', -- running, completed, failed, cancelled
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- 运行时长（秒）
    total_repositories INTEGER DEFAULT 0,
    scanned_repositories INTEGER DEFAULT 0,
    total_files INTEGER DEFAULT 0,
    scanned_files INTEGER DEFAULT 0,
    total_matches INTEGER DEFAULT 0,
    error_message TEXT,
    config JSONB,
    stats JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建扫描结果表
CREATE TABLE IF NOT EXISTS scan_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES monitor_tasks(id) ON DELETE CASCADE,
    run_id UUID NOT NULL REFERENCES scan_runs(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    platform VARCHAR(50) NOT NULL,
    repo_id VARCHAR(255) NOT NULL,
    repo_name VARCHAR(255) NOT NULL,
    repo_owner VARCHAR(255) NOT NULL,
    repo_url VARCHAR(500) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_sha VARCHAR(255),
    match_content TEXT NOT NULL,
    match_line INTEGER,
    match_context TEXT,
    keyword VARCHAR(255),
    pattern VARCHAR(500),
    risk_level VARCHAR(50) DEFAULT 'medium', -- critical, high, medium, low, info
    risk_score DECIMAL(5,2) DEFAULT 0.0,
    risk_reasons JSONB,
    status VARCHAR(50) DEFAULT 'new', -- new, reviewed, ignored, resolved
    is_duplicate BOOLEAN DEFAULT false,
    duplicate_hash VARCHAR(255),
    similarity_score DECIMAL(5,2),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建关键词表
CREATE TABLE IF NOT EXISTS scan_keywords (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    value VARCHAR(500) NOT NULL,
    category VARCHAR(100), -- password, api_key, secret, company, etc.
    is_regex BOOLEAN DEFAULT false,
    is_case_sensitive BOOLEAN DEFAULT false,
    risk_level VARCHAR(50) DEFAULT 'medium',
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建模式表
CREATE TABLE IF NOT EXISTS scan_patterns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    pattern VARCHAR(1000) NOT NULL,
    category VARCHAR(100),
    risk_level VARCHAR(50) DEFAULT 'medium',
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建平台账号表
CREATE TABLE IF NOT EXISTS platform_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    platform VARCHAR(50) NOT NULL, -- github, gitlab, gitee
    account_type VARCHAR(50) NOT NULL, -- token, oauth, username_password
    username VARCHAR(255),
    token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    scopes JSONB,
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 5,
    rate_limit INTEGER DEFAULT 0,
    rate_remaining INTEGER DEFAULT 0,
    rate_reset_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    last_checked_at TIMESTAMP WITH TIME ZONE,
    last_validated_at TIMESTAMP WITH TIME ZONE,
    error_count INTEGER DEFAULT 0,
    last_error TEXT,
    config JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建全量搜索任务表
CREATE TABLE IF NOT EXISTS global_search_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    platforms JSONB NOT NULL, -- ["github", "gitlab", "gitee"]
    keywords JSONB NOT NULL,
    search_type VARCHAR(50) DEFAULT 'code', -- code, repository, both
    file_types JSONB, -- [".js", ".py", ".java"]
    exclude_rules JSONB,
    schedule JSONB,
    is_active BOOLEAN DEFAULT false,
    status VARCHAR(50) DEFAULT 'created',
    priority INTEGER DEFAULT 5,
    search_depth INTEGER DEFAULT 100, -- 搜索深度（页数）
    max_results INTEGER DEFAULT 1000, -- 最大结果数
    concurrent_limit INTEGER DEFAULT 3, -- 并发限制
    last_run_at TIMESTAMP WITH TIME ZONE,
    next_run_at TIMESTAMP WITH TIME ZONE,
    config JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- 创建全量搜索运行记录表
CREATE TABLE IF NOT EXISTS global_search_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES global_search_tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    status VARCHAR(50) DEFAULT 'running',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    duration INTEGER,
    total_platforms INTEGER DEFAULT 0,
    searched_platforms INTEGER DEFAULT 0,
    total_queries INTEGER DEFAULT 0,
    executed_queries INTEGER DEFAULT 0,
    total_results INTEGER DEFAULT 0,
    unique_results INTEGER DEFAULT 0,
    error_message TEXT,
    config JSONB,
    stats JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建全量搜索结果表
CREATE TABLE IF NOT EXISTS global_search_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES global_search_tasks(id) ON DELETE CASCADE,
    run_id UUID NOT NULL REFERENCES global_search_runs(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    platform VARCHAR(50) NOT NULL,
    search_query VARCHAR(1000) NOT NULL,
    repo_id VARCHAR(255) NOT NULL,
    repo_name VARCHAR(255) NOT NULL,
    repo_owner VARCHAR(255) NOT NULL,
    repo_url VARCHAR(500) NOT NULL,
    repo_description TEXT,
    repo_language VARCHAR(100),
    repo_stars INTEGER DEFAULT 0,
    repo_forks INTEGER DEFAULT 0,
    repo_is_private BOOLEAN DEFAULT false,
    file_path VARCHAR(1000),
    file_name VARCHAR(255),
    file_url VARCHAR(500),
    file_sha VARCHAR(255),
    match_content TEXT,
    match_line INTEGER,
    match_context TEXT,
    keyword VARCHAR(255),
    risk_level VARCHAR(50) DEFAULT 'medium',
    risk_score DECIMAL(5,2) DEFAULT 0.0,
    risk_reasons JSONB,
    status VARCHAR(50) DEFAULT 'new',
    is_duplicate BOOLEAN DEFAULT false,
    duplicate_hash VARCHAR(255),
    similarity_score DECIMAL(5,2),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_user_id ON monitor_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_platform ON monitor_tasks(platform);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_status ON monitor_tasks(status);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_is_active ON monitor_tasks(is_active);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_next_run_at ON monitor_tasks(next_run_at);

CREATE INDEX IF NOT EXISTS idx_scan_runs_task_id ON scan_runs(task_id);
CREATE INDEX IF NOT EXISTS idx_scan_runs_user_id ON scan_runs(user_id);
CREATE INDEX IF NOT EXISTS idx_scan_runs_status ON scan_runs(status);
CREATE INDEX IF NOT EXISTS idx_scan_runs_started_at ON scan_runs(started_at);

CREATE INDEX IF NOT EXISTS idx_scan_results_task_id ON scan_results(task_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_run_id ON scan_results(run_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_user_id ON scan_results(user_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_platform ON scan_results(platform);
CREATE INDEX IF NOT EXISTS idx_scan_results_risk_level ON scan_results(risk_level);
CREATE INDEX IF NOT EXISTS idx_scan_results_status ON scan_results(status);
CREATE INDEX IF NOT EXISTS idx_scan_results_duplicate_hash ON scan_results(duplicate_hash);
CREATE INDEX IF NOT EXISTS idx_scan_results_created_at ON scan_results(created_at);

CREATE INDEX IF NOT EXISTS idx_scan_keywords_user_id ON scan_keywords(user_id);
CREATE INDEX IF NOT EXISTS idx_scan_keywords_category ON scan_keywords(category);
CREATE INDEX IF NOT EXISTS idx_scan_keywords_is_active ON scan_keywords(is_active);

CREATE INDEX IF NOT EXISTS idx_scan_patterns_user_id ON scan_patterns(user_id);
CREATE INDEX IF NOT EXISTS idx_scan_patterns_category ON scan_patterns(category);
CREATE INDEX IF NOT EXISTS idx_scan_patterns_is_active ON scan_patterns(is_active);

CREATE INDEX IF NOT EXISTS idx_platform_accounts_user_id ON platform_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_platform_accounts_platform ON platform_accounts(platform);
CREATE INDEX IF NOT EXISTS idx_platform_accounts_is_active ON platform_accounts(is_active);
CREATE INDEX IF NOT EXISTS idx_platform_accounts_priority ON platform_accounts(priority);

CREATE INDEX IF NOT EXISTS idx_global_search_tasks_user_id ON global_search_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_global_search_tasks_status ON global_search_tasks(status);
CREATE INDEX IF NOT EXISTS idx_global_search_tasks_is_active ON global_search_tasks(is_active);
CREATE INDEX IF NOT EXISTS idx_global_search_tasks_next_run_at ON global_search_tasks(next_run_at);

CREATE INDEX IF NOT EXISTS idx_global_search_runs_task_id ON global_search_runs(task_id);
CREATE INDEX IF NOT EXISTS idx_global_search_runs_user_id ON global_search_runs(user_id);
CREATE INDEX IF NOT EXISTS idx_global_search_runs_status ON global_search_runs(status);
CREATE INDEX IF NOT EXISTS idx_global_search_runs_started_at ON global_search_runs(started_at);

CREATE INDEX IF NOT EXISTS idx_global_search_results_task_id ON global_search_results(task_id);
CREATE INDEX IF NOT EXISTS idx_global_search_results_run_id ON global_search_results(run_id);
CREATE INDEX IF NOT EXISTS idx_global_search_results_user_id ON global_search_results(user_id);
CREATE INDEX IF NOT EXISTS idx_global_search_results_platform ON global_search_results(platform);
CREATE INDEX IF NOT EXISTS idx_global_search_results_risk_level ON global_search_results(risk_level);
CREATE INDEX IF NOT EXISTS idx_global_search_results_status ON global_search_results(status);
CREATE INDEX IF NOT EXISTS idx_global_search_results_duplicate_hash ON global_search_results(duplicate_hash);
CREATE INDEX IF NOT EXISTS idx_global_search_results_created_at ON global_search_results(created_at);
