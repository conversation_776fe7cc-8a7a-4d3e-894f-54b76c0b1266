# 密码和密钥相关
password
passwd
pwd
secret
key
token
api_key
apikey
access_key
secret_key
private_key
public_key
auth_token
bearer_token
jwt_token
session_token
csrf_token
oauth_token
refresh_token

# 数据库连接
database_url
db_url
db_host
db_password
mysql_password
postgres_password
mongodb_uri
redis_password
connection_string
dsn

# 云服务密钥
aws_access_key_id
aws_secret_access_key
aws_session_token
azure_client_secret
gcp_service_account
google_api_key
alibaba_access_key
tencent_secret_key

# 第三方服务
github_token
gitlab_token
bitbucket_token
slack_token
discord_token
telegram_token
wechat_secret
dingtalk_secret

# 邮件服务
smtp_password
email_password
mailgun_api_key
sendgrid_api_key

# 支付服务
stripe_secret_key
paypal_secret
alipay_private_key
wechat_pay_key

# 加密相关
encryption_key
decrypt_key
cipher_key
hash_salt
iv
nonce

# 证书相关
certificate
cert
private_key
public_key
ssl_key
tls_key
ca_cert
client_cert

# 配置文件敏感信息
admin_password
root_password
default_password
master_key
signing_key
webhook_secret

# 常见弱密码
123456
password123
admin123
root123
qwerty
abc123
password1
admin
root
guest
test
demo

# 中文敏感词
密码
口令
秘钥
令牌
访问密钥
私钥
公钥
证书
管理员密码
数据库密码
