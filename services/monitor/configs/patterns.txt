# AWS密钥模式
AKIA[0-9A-Z]{16}
[0-9a-zA-Z/+]{40}

# GitHub Token
ghp_[0-9a-zA-Z]{36}
gho_[0-9a-zA-Z]{36}
ghu_[0-9a-zA-Z]{36}
ghs_[0-9a-zA-Z]{36}
ghr_[0-9a-zA-Z]{36}

# Google API Key
AIza[0-9A-Za-z\\-_]{35}

# Slack Token
xox[baprs]-[0-9]{12}-[0-9]{12}-[0-9a-zA-Z]{24}

# JWT Token
eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*

# 邮箱地址
[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}

# IP地址
\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b

# 端口号
:[0-9]{1,5}

# URL模式
https?://[^\s<>"{}|\\^`\[\]]+

# 数据库连接字符串
mysql://[^\\s]+
postgresql://[^\\s]+
mongodb://[^\\s]+
redis://[^\\s]+

# 私钥模式
-----BEGIN [A-Z ]+PRIVATE KEY-----
-----BEGIN RSA PRIVATE KEY-----
-----BEGIN DSA PRIVATE KEY-----
-----BEGIN EC PRIVATE KEY-----
-----BEGIN OPENSSH PRIVATE KEY-----

# 证书模式
-----BEGIN CERTIFICATE-----
-----BEGIN X509 CERTIFICATE-----

# 密码模式（简单）
password\s*[:=]\s*["\']?[^"\'\s]{6,}["\']?
passwd\s*[:=]\s*["\']?[^"\'\s]{6,}["\']?
secret\s*[:=]\s*["\']?[^"\'\s]{8,}["\']?
key\s*[:=]\s*["\']?[^"\'\s]{8,}["\']?
token\s*[:=]\s*["\']?[^"\'\s]{16,}["\']?

# API密钥模式
api[_-]?key\s*[:=]\s*["\']?[a-zA-Z0-9]{16,}["\']?
access[_-]?key\s*[:=]\s*["\']?[a-zA-Z0-9]{16,}["\']?
secret[_-]?key\s*[:=]\s*["\']?[a-zA-Z0-9]{16,}["\']?

# 十六进制密钥
[a-fA-F0-9]{32,}

# Base64编码
[A-Za-z0-9+/]{20,}={0,2}

# 手机号码
1[3-9]\d{9}

# 身份证号码
[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]

# 银行卡号
[1-9]\d{15,18}

# 信用卡号
4[0-9]{12}(?:[0-9]{3})?
5[1-5][0-9]{14}
3[47][0-9]{13}
3[0-9]{13}
6(?:011|5[0-9]{2})[0-9]{12}

# 社会保险号
\d{3}-\d{2}-\d{4}

# 常见配置文件密码模式
DB_PASSWORD\s*[:=]\s*["\']?[^"\'\s]+["\']?
DATABASE_PASSWORD\s*[:=]\s*["\']?[^"\'\s]+["\']?
MYSQL_PASSWORD\s*[:=]\s*["\']?[^"\'\s]+["\']?
POSTGRES_PASSWORD\s*[:=]\s*["\']?[^"\'\s]+["\']?
REDIS_PASSWORD\s*[:=]\s*["\']?[^"\'\s]+["\']?

# 环境变量密码模式
export\s+[A-Z_]*PASSWORD\s*=\s*["\']?[^"\'\s]+["\']?
export\s+[A-Z_]*SECRET\s*=\s*["\']?[^"\'\s]+["\']?
export\s+[A-Z_]*KEY\s*=\s*["\']?[^"\'\s]+["\']?
export\s+[A-Z_]*TOKEN\s*=\s*["\']?[^"\'\s]+["\']?

# Docker密钥模式
DOCKER_PASSWORD\s*[:=]\s*["\']?[^"\'\s]+["\']?
REGISTRY_PASSWORD\s*[:=]\s*["\']?[^"\'\s]+["\']?

# 云服务密钥模式
AWS_ACCESS_KEY_ID\s*[:=]\s*["\']?AKIA[0-9A-Z]{16}["\']?
AWS_SECRET_ACCESS_KEY\s*[:=]\s*["\']?[0-9a-zA-Z/+]{40}["\']?
AZURE_CLIENT_SECRET\s*[:=]\s*["\']?[^"\'\s]{32,}["\']?
GCP_SERVICE_ACCOUNT_KEY\s*[:=]\s*["\']?[^"\'\s]+["\']?

# 微信小程序密钥
wx[a-f0-9]{16}

# 支付宝应用密钥
[0-9]{16,20}

# 腾讯云密钥
LTAI[a-zA-Z0-9]{12,20}

# 阿里云密钥
[a-zA-Z0-9]{24,30}
