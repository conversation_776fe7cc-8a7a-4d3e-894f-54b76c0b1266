package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/models"
)

// ResultService 结果服务
type ResultService struct {
	db    *gorm.DB
	redis *redis.Client
}

// NewResultService 创建结果服务
func NewResultService(db *gorm.DB, redis *redis.Client) *ResultService {
	return &ResultService{
		db:    db,
		redis: redis,
	}
}

// ListResultsRequest 列出结果请求
type ListResultsRequest struct {
	UserID     uuid.UUID `json:"user_id" validate:"required"`
	TaskID     *uuid.UUID `json:"task_id,omitempty"`
	RunID      *uuid.UUID `json:"run_id,omitempty"`
	Status     string    `json:"status,omitempty"`
	RiskLevel  string    `json:"risk_level,omitempty"`
	MatchType  string    `json:"match_type,omitempty"`
	RepoOwner  string    `json:"repo_owner,omitempty"`
	RepoName   string    `json:"repo_name,omitempty"`
	Search     string    `json:"search,omitempty"`
	Page       int       `json:"page" validate:"min=1"`
	Limit      int       `json:"limit" validate:"min=1,max=100"`
	SortBy     string    `json:"sort_by,omitempty"`
	SortDesc   bool      `json:"sort_desc,omitempty"`
	DateFrom   *time.Time `json:"date_from,omitempty"`
	DateTo     *time.Time `json:"date_to,omitempty"`
}

// UpdateResultRequest 更新结果请求
type UpdateResultRequest struct {
	Status      *string `json:"status,omitempty" validate:"omitempty,oneof=new reviewed ignored resolved false_positive"`
	ReviewNotes *string `json:"review_notes,omitempty"`
}

// ResultStats 结果统计
type ResultStats struct {
	Total        int64            `json:"total"`
	ByStatus     map[string]int64 `json:"by_status"`
	ByRiskLevel  map[string]int64 `json:"by_risk_level"`
	ByMatchType  map[string]int64 `json:"by_match_type"`
	RecentTrends []TrendData      `json:"recent_trends"`
}

// TrendData 趋势数据
type TrendData struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// DuplicateGroup 重复组
type DuplicateGroup struct {
	ContentHash string                `json:"content_hash"`
	Count       int64                 `json:"count"`
	Results     []*models.ScanResult  `json:"results"`
}

// ListResults 列出扫描结果
func (s *ResultService) ListResults(ctx context.Context, req *ListResultsRequest) ([]*models.ScanResult, int64, error) {
	query := s.db.WithContext(ctx).Model(&models.ScanResult{}).
		Where("user_id = ?", req.UserID)

	// 应用过滤条件
	s.applyFilters(query, req)

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count results: %w", err)
	}

	// 排序
	sortBy := "created_at"
	if req.SortBy != "" {
		sortBy = req.SortBy
	}
	
	order := sortBy + " ASC"
	if req.SortDesc {
		order = sortBy + " DESC"
	}
	query = query.Order(order)

	// 分页
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	// 查询结果
	var results []*models.ScanResult
	if err := query.Preload("Task").Preload("Run").Find(&results).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list results: %w", err)
	}

	return results, total, nil
}

// GetResult 获取结果详情
func (s *ResultService) GetResult(ctx context.Context, userID, resultID uuid.UUID) (*models.ScanResult, error) {
	var result models.ScanResult
	if err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", resultID, userID).
		Preload("Task").
		Preload("Run").
		First(&result).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("result not found")
		}
		return nil, fmt.Errorf("failed to get result: %w", err)
	}

	return &result, nil
}

// UpdateResult 更新结果
func (s *ResultService) UpdateResult(ctx context.Context, userID, resultID uuid.UUID, req *UpdateResultRequest) (*models.ScanResult, error) {
	// 获取现有结果
	result, err := s.GetResult(ctx, userID, resultID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	updates := make(map[string]interface{})
	
	if req.Status != nil {
		updates["status"] = *req.Status
		updates["reviewed_by"] = userID
		updates["reviewed_at"] = time.Now()
	}
	
	if req.ReviewNotes != nil {
		updates["review_notes"] = *req.ReviewNotes
	}

	// 执行更新
	if err := s.db.WithContext(ctx).Model(result).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update result: %w", err)
	}

	// 重新获取更新后的结果
	return s.GetResult(ctx, userID, resultID)
}

// BatchUpdateResults 批量更新结果
func (s *ResultService) BatchUpdateResults(ctx context.Context, userID uuid.UUID, resultIDs []uuid.UUID, req *UpdateResultRequest) error {
	updates := make(map[string]interface{})
	
	if req.Status != nil {
		updates["status"] = *req.Status
		updates["reviewed_by"] = userID
		updates["reviewed_at"] = time.Now()
	}
	
	if req.ReviewNotes != nil {
		updates["review_notes"] = *req.ReviewNotes
	}

	if len(updates) == 0 {
		return fmt.Errorf("no updates provided")
	}

	// 执行批量更新
	result := s.db.WithContext(ctx).Model(&models.ScanResult{}).
		Where("id IN ? AND user_id = ?", resultIDs, userID).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to batch update results: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("no results updated")
	}

	return nil
}

// DeleteResult 删除结果
func (s *ResultService) DeleteResult(ctx context.Context, userID, resultID uuid.UUID) error {
	result := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", resultID, userID).
		Delete(&models.ScanResult{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete result: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("result not found")
	}

	return nil
}

// GetResultStats 获取结果统计
func (s *ResultService) GetResultStats(ctx context.Context, userID uuid.UUID, taskID *uuid.UUID, days int) (*ResultStats, error) {
	query := s.db.WithContext(ctx).Model(&models.ScanResult{}).
		Where("user_id = ?", userID)

	if taskID != nil {
		query = query.Where("task_id = ?", *taskID)
	}

	if days > 0 {
		since := time.Now().AddDate(0, 0, -days)
		query = query.Where("created_at >= ?", since)
	}

	// 总数统计
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count total results: %w", err)
	}

	// 按状态统计
	byStatus, err := s.getStatsByField(ctx, query, "status")
	if err != nil {
		return nil, fmt.Errorf("failed to get stats by status: %w", err)
	}

	// 按风险级别统计
	byRiskLevel, err := s.getStatsByField(ctx, query, "risk_level")
	if err != nil {
		return nil, fmt.Errorf("failed to get stats by risk level: %w", err)
	}

	// 按匹配类型统计
	byMatchType, err := s.getStatsByField(ctx, query, "match_type")
	if err != nil {
		return nil, fmt.Errorf("failed to get stats by match type: %w", err)
	}

	// 趋势数据
	trends, err := s.getTrendData(ctx, userID, taskID, 30)
	if err != nil {
		return nil, fmt.Errorf("failed to get trend data: %w", err)
	}

	return &ResultStats{
		Total:        total,
		ByStatus:     byStatus,
		ByRiskLevel:  byRiskLevel,
		ByMatchType:  byMatchType,
		RecentTrends: trends,
	}, nil
}

// FindDuplicates 查找重复结果
func (s *ResultService) FindDuplicates(ctx context.Context, userID uuid.UUID, taskID *uuid.UUID) ([]*DuplicateGroup, error) {
	query := s.db.WithContext(ctx).Model(&models.ScanResult{}).
		Where("user_id = ?", userID)

	if taskID != nil {
		query = query.Where("task_id = ?", *taskID)
	}

	// 查找重复的内容哈希
	var duplicateHashes []struct {
		ContentHash string `json:"content_hash"`
		Count       int64  `json:"count"`
	}

	if err := query.Select("content_hash, COUNT(*) as count").
		Group("content_hash").
		Having("COUNT(*) > 1").
		Scan(&duplicateHashes).Error; err != nil {
		return nil, fmt.Errorf("failed to find duplicate hashes: %w", err)
	}

	// 获取每个重复组的详细信息
	var groups []*DuplicateGroup
	for _, dup := range duplicateHashes {
		var results []*models.ScanResult
		if err := s.db.WithContext(ctx).
			Where("user_id = ? AND content_hash = ?", userID, dup.ContentHash).
			Preload("Task").
			Preload("Run").
			Find(&results).Error; err != nil {
			continue
		}

		groups = append(groups, &DuplicateGroup{
			ContentHash: dup.ContentHash,
			Count:       dup.Count,
			Results:     results,
		})
	}

	return groups, nil
}

// MergeDuplicates 合并重复结果
func (s *ResultService) MergeDuplicates(ctx context.Context, userID uuid.UUID, contentHash string, keepResultID uuid.UUID) error {
	// 获取要保留的结果
	var keepResult models.ScanResult
	if err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ? AND content_hash = ?", keepResultID, userID, contentHash).
		First(&keepResult).Error; err != nil {
		return fmt.Errorf("keep result not found: %w", err)
	}

	// 删除其他重复结果
	result := s.db.WithContext(ctx).
		Where("user_id = ? AND content_hash = ? AND id != ?", userID, contentHash, keepResultID).
		Delete(&models.ScanResult{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete duplicate results: %w", result.Error)
	}

	return nil
}

// applyFilters 应用过滤条件
func (s *ResultService) applyFilters(query *gorm.DB, req *ListResultsRequest) {
	if req.TaskID != nil {
		query.Where("task_id = ?", *req.TaskID)
	}

	if req.RunID != nil {
		query.Where("run_id = ?", *req.RunID)
	}

	if req.Status != "" {
		query.Where("status = ?", req.Status)
	}

	if req.RiskLevel != "" {
		query.Where("risk_level = ?", req.RiskLevel)
	}

	if req.MatchType != "" {
		query.Where("match_type = ?", req.MatchType)
	}

	if req.RepoOwner != "" {
		query.Where("repo_owner = ?", req.RepoOwner)
	}

	if req.RepoName != "" {
		query.Where("repo_name = ?", req.RepoName)
	}

	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query.Where("match_content ILIKE ? OR file_path ILIKE ? OR repo_full_name ILIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	if req.DateFrom != nil {
		query.Where("created_at >= ?", *req.DateFrom)
	}

	if req.DateTo != nil {
		query.Where("created_at <= ?", *req.DateTo)
	}
}

// getStatsByField 按字段获取统计
func (s *ResultService) getStatsByField(ctx context.Context, query *gorm.DB, field string) (map[string]int64, error) {
	var stats []struct {
		Field string `json:"field"`
		Count int64  `json:"count"`
	}

	if err := query.Select(fmt.Sprintf("%s as field, COUNT(*) as count", field)).
		Group(field).
		Scan(&stats).Error; err != nil {
		return nil, err
	}

	result := make(map[string]int64)
	for _, stat := range stats {
		result[stat.Field] = stat.Count
	}

	return result, nil
}

// getTrendData 获取趋势数据
func (s *ResultService) getTrendData(ctx context.Context, userID uuid.UUID, taskID *uuid.UUID, days int) ([]TrendData, error) {
	query := s.db.WithContext(ctx).Model(&models.ScanResult{}).
		Where("user_id = ?", userID)

	if taskID != nil {
		query = query.Where("task_id = ?", *taskID)
	}

	since := time.Now().AddDate(0, 0, -days)
	query = query.Where("created_at >= ?", since)

	var trends []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	if err := query.Select("DATE(created_at) as date, COUNT(*) as count").
		Group("DATE(created_at)").
		Order("date ASC").
		Scan(&trends).Error; err != nil {
		return nil, err
	}

	result := make([]TrendData, len(trends))
	for i, trend := range trends {
		result[i] = TrendData{
			Date:  trend.Date,
			Count: trend.Count,
		}
	}

	return result, nil
}
