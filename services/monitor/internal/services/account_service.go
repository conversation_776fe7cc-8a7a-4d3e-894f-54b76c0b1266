package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/adapters"
	"github.com/godeye/monitor/internal/models"
)

// getStringValue 安全获取字符串指针的值
func getStringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// AccountService 账号管理服务
type AccountService struct {
	db             *gorm.DB
	adapterManager *adapters.AdapterManager
}

// NewAccountService 创建账号管理服务
func NewAccountService(db *gorm.DB) *AccountService {
	return &AccountService{
		db:             db,
		adapterManager: adapters.NewAdapterManager(),
	}
}

// SetAdapterManager 设置适配器管理器
func (s *AccountService) SetAdapterManager(manager *adapters.AdapterManager) {
	s.adapterManager = manager
}

// CreateAccount 创建平台账号
func (s *AccountService) CreateAccount(ctx context.Context, req *CreateAccountRequest) (*models.PlatformAccount, error) {
	account := &models.PlatformAccount{
		UserID:        req.UserID,
		Platform:      req.Platform,
		AccountType:   req.AccountType,
		Username:      req.Username,
		Token:         req.Token,
		RefreshToken:  getStringValue(req.RefreshToken),
		ExpiresAt:     req.ExpiresAt,
		RateLimit:     req.RateLimit,
		RateRemaining: req.RateRemaining,
	}

	// 验证账号有效性
	if err := s.validateAccount(ctx, account); err != nil {
		return nil, fmt.Errorf("account validation failed: %w", err)
	}

	if err := s.db.Create(account).Error; err != nil {
		return nil, fmt.Errorf("failed to create account: %w", err)
	}

	return account, nil
}

// GetAccount 获取平台账号
func (s *AccountService) GetAccount(ctx context.Context, accountID uuid.UUID) (*models.PlatformAccount, error) {
	var account models.PlatformAccount
	if err := s.db.First(&account, "id = ?", accountID).Error; err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}
	return &account, nil
}

// ListAccounts 列出平台账号
func (s *AccountService) ListAccounts(ctx context.Context, req *ListAccountsRequest) ([]*models.PlatformAccount, int64, error) {
	var accounts []*models.PlatformAccount
	var total int64

	query := s.db.Model(&models.PlatformAccount{})

	// 添加过滤条件
	if req.UserID != uuid.Nil {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.Platform != "" {
		query = query.Where("platform = ?", req.Platform)
	}
	if req.AccountType != "" {
		query = query.Where("account_type = ?", req.AccountType)
	}
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count accounts: %w", err)
	}

	// 添加排序和分页
	query = query.Order("created_at DESC")
	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}
	if req.Offset > 0 {
		query = query.Offset(req.Offset)
	}

	if err := query.Find(&accounts).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list accounts: %w", err)
	}

	return accounts, total, nil
}

// UpdateAccount 更新平台账号
func (s *AccountService) UpdateAccount(ctx context.Context, accountID uuid.UUID, req *UpdateAccountRequest) (*models.PlatformAccount, error) {
	var account models.PlatformAccount
	if err := s.db.First(&account, "id = ?", accountID).Error; err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// 更新字段
	if req.Username != nil {
		account.Username = *req.Username
	}
	if req.Token != nil {
		account.Token = *req.Token
	}
	if req.RefreshToken != nil {
		account.RefreshToken = getStringValue(req.RefreshToken)
	}
	if req.ExpiresAt != nil {
		account.ExpiresAt = req.ExpiresAt
	}

	account.UpdatedAt = time.Now()

	// 如果更新了关键信息，重新验证账号
	if req.Token != nil || req.Username != nil {
		if err := s.validateAccount(ctx, &account); err != nil {
			return nil, fmt.Errorf("account validation failed: %w", err)
		}
	}

	if err := s.db.Save(&account).Error; err != nil {
		return nil, fmt.Errorf("failed to update account: %w", err)
	}

	return &account, nil
}

// DeleteAccount 删除平台账号
func (s *AccountService) DeleteAccount(ctx context.Context, accountID uuid.UUID) error {
	if err := s.db.Delete(&models.PlatformAccount{}, "id = ?", accountID).Error; err != nil {
		return fmt.Errorf("failed to delete account: %w", err)
	}
	return nil
}

// ValidateAccount 验证账号有效性
func (s *AccountService) ValidateAccount(ctx context.Context, accountID uuid.UUID) (*AccountValidationResult, error) {
	var account models.PlatformAccount
	if err := s.db.First(&account, "id = ?", accountID).Error; err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	result := &AccountValidationResult{
		AccountID: accountID,
		Platform:  account.Platform,
		Valid:     false,
	}

	if err := s.validateAccount(ctx, &account); err != nil {
		result.Error = err.Error()
		result.ValidatedAt = time.Now()
		return result, nil
	}

	result.Valid = true
	result.ValidatedAt = time.Now()

	// 保存账号状态
	s.db.Save(&account)

	return result, nil
}

// GetRateLimit 获取账号速率限制信息
func (s *AccountService) GetRateLimit(ctx context.Context, accountID uuid.UUID) (*RateLimitInfo, error) {
	var account models.PlatformAccount
	if err := s.db.First(&account, "id = ?", accountID).Error; err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	adapter, exists := s.adapterManager.GetAdapter(account.Platform)
	if !exists {
		return nil, fmt.Errorf("unsupported platform: %s", account.Platform)
	}

	adapterAccount := &adapters.Account{
		Platform:     account.Platform,
		AccountType:  account.AccountType,
		Username:     account.Username,
		Token:        account.Token,
		RefreshToken: account.RefreshToken,
	}

	rateLimit, err := adapter.GetRateLimit(ctx, adapterAccount)
	if err != nil {
		return nil, fmt.Errorf("failed to get rate limit: %w", err)
	}

	// 更新数据库中的速率限制信息
	account.RateLimit = rateLimit.Limit
	account.RateRemaining = rateLimit.Remaining
	if rateLimit.ResetAt != nil {
		account.RateResetAt = rateLimit.ResetAt
	}
	now := time.Now()
	s.db.Save(&account)

	return &RateLimitInfo{
		AccountID: accountID,
		Platform:  account.Platform,
		Limit:     rateLimit.Limit,
		Remaining: rateLimit.Remaining,
		ResetAt:   rateLimit.ResetAt,
		Used:      rateLimit.Used,
		CheckedAt: now,
	}, nil
}

// GetAccountStats 获取账号统计信息
func (s *AccountService) GetAccountStats(ctx context.Context, userID uuid.UUID) (*AccountStats, error) {
	stats := &AccountStats{}

	// 统计总账号数
	if err := s.db.Model(&models.PlatformAccount{}).Where("user_id = ?", userID).Count(&stats.TotalAccounts).Error; err != nil {
		return nil, fmt.Errorf("failed to count total accounts: %w", err)
	}

	// 统计活跃账号数
	if err := s.db.Model(&models.PlatformAccount{}).Where("user_id = ? AND is_active = ?", userID, true).Count(&stats.ActiveAccounts).Error; err != nil {
		return nil, fmt.Errorf("failed to count active accounts: %w", err)
	}

	// 按平台统计
	var platformStats []struct {
		Platform string `json:"platform"`
		Count    int64  `json:"count"`
	}

	if err := s.db.Model(&models.PlatformAccount{}).
		Select("platform, COUNT(*) as count").
		Where("user_id = ?", userID).
		Group("platform").
		Scan(&platformStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get platform stats: %w", err)
	}

	stats.PlatformStats = make(map[string]int64)
	for _, stat := range platformStats {
		stats.PlatformStats[stat.Platform] = stat.Count
	}

	// 统计速率限制使用情况
	var rateLimitStats []struct {
		Platform      string  `json:"platform"`
		AvgUsage      float64 `json:"avg_usage"`
		TotalLimit    int64   `json:"total_limit"`
		TotalRemaining int64  `json:"total_remaining"`
	}

	if err := s.db.Model(&models.PlatformAccount{}).
		Select("platform, AVG(CASE WHEN rate_limit > 0 THEN (rate_limit - rate_remaining) * 100.0 / rate_limit ELSE 0 END) as avg_usage, SUM(rate_limit) as total_limit, SUM(rate_remaining) as total_remaining").
		Where("user_id = ? AND is_active = ?", userID, true).
		Group("platform").
		Scan(&rateLimitStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get rate limit stats: %w", err)
	}

	stats.RateLimitStats = make(map[string]map[string]interface{})
	for _, stat := range rateLimitStats {
		stats.RateLimitStats[stat.Platform] = map[string]interface{}{
			"avg_usage":       stat.AvgUsage,
			"total_limit":     stat.TotalLimit,
			"total_remaining": stat.TotalRemaining,
		}
	}

	return stats, nil
}

// GetPlatformUsageStats 获取平台使用统计
func (s *AccountService) GetPlatformUsageStats(ctx context.Context, userID uuid.UUID, days int) (*PlatformUsageStats, error) {
	stats := &PlatformUsageStats{
		Days: days,
	}

	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)

	// 这里需要从搜索结果表中统计平台使用情况
	// 由于涉及多个表的关联查询，这里简化处理
	var dailyUsage []struct {
		Date     string `json:"date"`
		Platform string `json:"platform"`
		Count    int64  `json:"count"`
	}

	// 从全量搜索结果表统计
	if err := s.db.Model(&models.GlobalSearchResult{}).
		Select("DATE(created_at) as date, platform, COUNT(*) as count").
		Where("user_id = ? AND created_at >= ? AND created_at <= ?", userID, startTime, endTime).
		Group("DATE(created_at), platform").
		Order("date, platform").
		Scan(&dailyUsage).Error; err != nil {
		return nil, fmt.Errorf("failed to get daily usage stats: %w", err)
	}

	// 转换dailyUsage为map格式
	stats.DailyUsage = make([]map[string]interface{}, len(dailyUsage))
	for i, usage := range dailyUsage {
		stats.DailyUsage[i] = map[string]interface{}{
			"date":     usage.Date,
			"platform": usage.Platform,
			"count":    usage.Count,
		}
	}

	return stats, nil
}

// 辅助方法

func (s *AccountService) validateAccount(ctx context.Context, account *models.PlatformAccount) error {
	if s.adapterManager == nil {
		return fmt.Errorf("adapter manager not initialized")
	}

	adapter, exists := s.adapterManager.GetAdapter(account.Platform)
	if !exists {
		return fmt.Errorf("unsupported platform: %s", account.Platform)
	}

	adapterAccount := &adapters.Account{
		Platform:     account.Platform,
		AccountType:  account.AccountType,
		Username:     account.Username,
		Token:        account.Token,
		RefreshToken: account.RefreshToken,
	}

	return adapter.ValidateAccount(ctx, adapterAccount)
}

// 请求和响应结构

type CreateAccountRequest struct {
	UserID        uuid.UUID     `json:"user_id"`
	Platform      string        `json:"platform"`
	AccountType   string        `json:"account_type"`
	Username      string        `json:"username"`
	Token         string        `json:"token"`
	RefreshToken  *string       `json:"refresh_token,omitempty"`
	ExpiresAt     *time.Time    `json:"expires_at,omitempty"`
	Scopes        models.JSONB  `json:"scopes,omitempty"`
	IsActive      bool          `json:"is_active"`
	Priority      int           `json:"priority"`
	RateLimit     int           `json:"rate_limit"`
	RateRemaining int           `json:"rate_remaining"`
	Config        models.JSONB  `json:"config,omitempty"`
}

type UpdateAccountRequest struct {
	Username     *string       `json:"username,omitempty"`
	Token        *string       `json:"token,omitempty"`
	RefreshToken *string       `json:"refresh_token,omitempty"`
	ExpiresAt    *time.Time    `json:"expires_at,omitempty"`
	Scopes       models.JSONB  `json:"scopes,omitempty"`
	IsActive     *bool         `json:"is_active,omitempty"`
	Priority     *int          `json:"priority,omitempty"`
	Config       models.JSONB  `json:"config,omitempty"`
}

type ListAccountsRequest struct {
	UserID      uuid.UUID `json:"user_id"`
	Platform    string    `json:"platform"`
	AccountType string    `json:"account_type"`
	IsActive    *bool     `json:"is_active"`
	Limit       int       `json:"limit"`
	Offset      int       `json:"offset"`
}

type AccountValidationResult struct {
	AccountID   uuid.UUID `json:"account_id"`
	Platform    string    `json:"platform"`
	Valid       bool      `json:"valid"`
	Error       string    `json:"error,omitempty"`
	ValidatedAt time.Time `json:"validated_at"`
}

type RateLimitInfo struct {
	AccountID uuid.UUID  `json:"account_id"`
	Platform  string     `json:"platform"`
	Limit     int        `json:"limit"`
	Remaining int        `json:"remaining"`
	ResetAt   *time.Time `json:"reset_at,omitempty"`
	Used      int        `json:"used"`
	CheckedAt time.Time  `json:"checked_at"`
}

type AccountStats struct {
	TotalAccounts  int64                              `json:"total_accounts"`
	ActiveAccounts int64                              `json:"active_accounts"`
	PlatformStats  map[string]int64                   `json:"platform_stats"`
	RateLimitStats map[string]map[string]interface{} `json:"rate_limit_stats"`
	DailyUsage     []map[string]interface{}          `json:"daily_usage"`
}

type PlatformUsageStats struct {
	Days       int                      `json:"days"`
	DailyUsage []map[string]interface{} `json:"daily_usage"`
}
