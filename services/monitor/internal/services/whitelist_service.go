package services

import (
	"context"
	"fmt"
	"math"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/models"
)

// WhitelistService 白名单服务
type WhitelistService struct {
	db *gorm.DB
}

// NewWhitelistService 创建白名单服务
func NewWhitelistService(db *gorm.DB) *WhitelistService {
	return &WhitelistService{
		db: db,
	}
}

// CreateWhitelist 创建白名单条目
func (s *WhitelistService) CreateWhitelist(ctx context.Context, userID uuid.UUID, req *models.WhitelistCreateRequest) (*models.Whitelist, error) {
	// 检查是否已存在
	var existing models.Whitelist
	err := s.db.WithContext(ctx).Where(
		"user_id = ? AND type = ? AND platform = ? AND identifier = ? AND deleted_at IS NULL",
		userID, req.Type, req.Platform, req.Identifier,
	).First(&existing).Error

	if err == nil {
		return nil, fmt.Errorf("whitelist entry already exists")
	}
	if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing whitelist: %w", err)
	}

	// 创建新的白名单条目
	whitelist := &models.Whitelist{
		UserID:     userID,
		Type:       req.Type,
		Platform:   req.Platform,
		Identifier: req.Identifier,
		Reason:     req.Reason,
		Status:     "active",
	}

	// 如果是仓库类型，设置仓库相关字段
	if req.Type == "repository" {
		if req.RepoOwner != "" {
			whitelist.RepoOwner = &req.RepoOwner
		}
		if req.RepoName != "" {
			whitelist.RepoName = &req.RepoName
		}
		if req.RepoURL != "" {
			whitelist.RepoURL = &req.RepoURL
		}
	}

	if err := s.db.WithContext(ctx).Create(whitelist).Error; err != nil {
		return nil, fmt.Errorf("failed to create whitelist: %w", err)
	}

	return whitelist, nil
}

// GetWhitelistByID 根据ID获取白名单条目
func (s *WhitelistService) GetWhitelistByID(ctx context.Context, userID, whitelistID uuid.UUID) (*models.Whitelist, error) {
	var whitelist models.Whitelist
	err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", whitelistID, userID).First(&whitelist).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("whitelist not found")
		}
		return nil, fmt.Errorf("failed to get whitelist: %w", err)
	}
	return &whitelist, nil
}

// UpdateWhitelist 更新白名单条目
func (s *WhitelistService) UpdateWhitelist(ctx context.Context, userID, whitelistID uuid.UUID, req *models.WhitelistUpdateRequest) (*models.Whitelist, error) {
	var whitelist models.Whitelist
	err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", whitelistID, userID).First(&whitelist).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("whitelist not found")
		}
		return nil, fmt.Errorf("failed to get whitelist: %w", err)
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Reason != "" {
		updates["reason"] = req.Reason
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	if len(updates) > 0 {
		if err := s.db.WithContext(ctx).Model(&whitelist).Updates(updates).Error; err != nil {
			return nil, fmt.Errorf("failed to update whitelist: %w", err)
		}
	}

	return &whitelist, nil
}

// DeleteWhitelist 删除白名单条目
func (s *WhitelistService) DeleteWhitelist(ctx context.Context, userID, whitelistID uuid.UUID) error {
	result := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", whitelistID, userID).Delete(&models.Whitelist{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete whitelist: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("whitelist not found")
	}
	return nil
}

// ListWhitelists 获取白名单列表
func (s *WhitelistService) ListWhitelists(ctx context.Context, userID uuid.UUID, req *models.WhitelistListRequest) (*models.WhitelistListResponse, error) {
	query := s.db.WithContext(ctx).Where("user_id = ?", userID)

	// 添加过滤条件
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}
	if req.Platform != "" {
		query = query.Where("platform = ?", req.Platform)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Keyword != "" {
		keyword := "%" + strings.ToLower(req.Keyword) + "%"
		query = query.Where("LOWER(identifier) LIKE ? OR LOWER(reason) LIKE ?", keyword, keyword)
	}

	// 获取总数
	var total int64
	if err := query.Model(&models.Whitelist{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count whitelists: %w", err)
	}

	// 分页
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize
	var whitelists []models.Whitelist
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&whitelists).Error; err != nil {
		return nil, fmt.Errorf("failed to get whitelists: %w", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	return &models.WhitelistListResponse{
		Items:      whitelists,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// IsWhitelisted 检查是否在白名单中
func (s *WhitelistService) IsWhitelisted(ctx context.Context, userID uuid.UUID, platform, repoFullName string) (bool, error) {
	var count int64
	err := s.db.WithContext(ctx).Model(&models.Whitelist{}).Where(
		"user_id = ? AND platform = ? AND status = 'active' AND (identifier = ? OR (type = 'repository' AND identifier = ?))",
		userID, platform, repoFullName, repoFullName,
	).Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check whitelist: %w", err)
	}

	return count > 0, nil
}
