package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/models"
	"github.com/godeye/monitor/internal/search"
)

// GlobalSearchService 全量搜索服务
type GlobalSearchService struct {
	db        *gorm.DB
	scheduler *search.SearchScheduler
}

// NewGlobalSearchService 创建全量搜索服务
func NewGlobalSearchService(db *gorm.DB, scheduler *search.SearchScheduler) *GlobalSearchService {
	return &GlobalSearchService{
		db:        db,
		scheduler: scheduler,
	}
}

// CreateTask 创建全量搜索任务
func (s *GlobalSearchService) CreateTask(ctx context.Context, req *CreateGlobalSearchTaskRequest) (*models.GlobalSearchTask, error) {
	task := &models.GlobalSearchTask{
		UserID:          req.UserID,
		Name:            req.Name,
		Description:     req.Description,
		Platforms:       req.Platforms,
		Keywords:        req.Keywords,
		SearchType:      req.SearchType,
		FileTypes:       req.FileTypes,
		ExcludeRules:    req.ExcludeRules,
		IsActive:        req.IsActive,
		SearchDepth:     req.SearchDepth,
		MaxResults:      req.MaxResults,
		ConcurrentLimit: req.ConcurrentLimit,
	}

	if err := s.db.Create(task).Error; err != nil {
		return nil, fmt.Errorf("failed to create global search task: %w", err)
	}

	// 如果任务是活跃的，计算下次运行时间
	if task.IsActive && task.ScheduleRule != "" {
		nextRun, err := s.calculateNextRun(task.ScheduleRule)
		if err == nil {
			task.NextRunAt = &nextRun
			s.db.Save(task)
		}
	}

	return task, nil
}

// GetTask 获取全量搜索任务
func (s *GlobalSearchService) GetTask(ctx context.Context, taskID uuid.UUID) (*models.GlobalSearchTask, error) {
	var task models.GlobalSearchTask
	if err := s.db.First(&task, "id = ?", taskID).Error; err != nil {
		return nil, fmt.Errorf("failed to get global search task: %w", err)
	}
	return &task, nil
}

// ListTasks 列出全量搜索任务
func (s *GlobalSearchService) ListTasks(ctx context.Context, req *ListGlobalSearchTasksRequest) ([]*models.GlobalSearchTask, int64, error) {
	var tasks []*models.GlobalSearchTask
	var total int64

	query := s.db.Model(&models.GlobalSearchTask{})

	// 添加过滤条件
	if req.UserID != uuid.Nil {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.SearchType != "" {
		query = query.Where("search_type = ?", req.SearchType)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count global search tasks: %w", err)
	}

	// 添加排序和分页
	query = query.Order("created_at DESC")
	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}
	if req.Offset > 0 {
		query = query.Offset(req.Offset)
	}

	if err := query.Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list global search tasks: %w", err)
	}

	return tasks, total, nil
}

// UpdateTask 更新全量搜索任务
func (s *GlobalSearchService) UpdateTask(ctx context.Context, taskID uuid.UUID, req *UpdateGlobalSearchTaskRequest) (*models.GlobalSearchTask, error) {
	var task models.GlobalSearchTask
	if err := s.db.First(&task, "id = ?", taskID).Error; err != nil {
		return nil, fmt.Errorf("failed to get global search task: %w", err)
	}

	// 更新字段
	if req.Name != nil {
		task.Name = *req.Name
	}
	if req.Description != nil {
		task.Description = *req.Description
	}
	if req.Platforms != nil {
		task.Platforms = req.Platforms
	}
	if req.Keywords != nil {
		task.Keywords = req.Keywords
	}
	if req.SearchType != nil {
		task.SearchType = *req.SearchType
	}
	if req.FileTypes != nil {
		task.FileTypes = req.FileTypes
	}
	if req.ExcludeRules != nil {
		task.ExcludeRules = req.ExcludeRules
	}
	if req.Schedule != nil {
		// 从Schedule JSONB中提取ScheduleRule
		if scheduleRule, ok := req.Schedule["rule"].(string); ok {
			task.ScheduleRule = scheduleRule
			// 重新计算下次运行时间
			if nextRun, err := s.calculateNextRun(task.ScheduleRule); err == nil {
				task.NextRunAt = &nextRun
			}
		}
	}
	if req.IsActive != nil {
		task.IsActive = *req.IsActive
	}
	if req.SearchDepth != nil {
		task.SearchDepth = *req.SearchDepth
	}
	if req.MaxResults != nil {
		task.MaxResults = *req.MaxResults
	}
	if req.ConcurrentLimit != nil {
		task.ConcurrentLimit = *req.ConcurrentLimit
	}

	task.UpdatedAt = time.Now()

	if err := s.db.Save(&task).Error; err != nil {
		return nil, fmt.Errorf("failed to update global search task: %w", err)
	}

	return &task, nil
}

// DeleteTask 删除全量搜索任务
func (s *GlobalSearchService) DeleteTask(ctx context.Context, taskID uuid.UUID) error {
	// 软删除任务
	if err := s.db.Delete(&models.GlobalSearchTask{}, "id = ?", taskID).Error; err != nil {
		return fmt.Errorf("failed to delete global search task: %w", err)
	}
	return nil
}

// StartTask 启动全量搜索任务
func (s *GlobalSearchService) StartTask(ctx context.Context, taskID uuid.UUID) error {
	var task models.GlobalSearchTask
	if err := s.db.First(&task, "id = ?", taskID).Error; err != nil {
		return fmt.Errorf("failed to get global search task: %w", err)
	}

	// 更新任务状态
	task.IsActive = true
	task.Status = "active"
	if task.ScheduleRule != "" {
		if nextRun, err := s.calculateNextRun(task.ScheduleRule); err == nil {
			task.NextRunAt = &nextRun
		}
	}

	if err := s.db.Save(&task).Error; err != nil {
		return fmt.Errorf("failed to update global search task: %w", err)
	}

	// 提交任务到调度器
	return s.scheduler.SubmitGlobalSearchTask(taskID, task.UserID, 5) // 默认优先级
}

// StopTask 停止全量搜索任务
func (s *GlobalSearchService) StopTask(ctx context.Context, taskID uuid.UUID) error {
	var task models.GlobalSearchTask
	if err := s.db.First(&task, "id = ?", taskID).Error; err != nil {
		return fmt.Errorf("failed to get global search task: %w", err)
	}

	// 更新任务状态
	task.IsActive = false
	task.Status = "stopped"
	task.NextRunAt = nil

	if err := s.db.Save(&task).Error; err != nil {
		return fmt.Errorf("failed to update global search task: %w", err)
	}

	return nil
}

// QuickSearch 快速搜索
func (s *GlobalSearchService) QuickSearch(ctx context.Context, req *QuickSearchRequest) (*QuickSearchResponse, error) {
	// 创建临时搜索任务
	task := &models.GlobalSearchTask{
		UserID:          req.UserID,
		Name:            "Quick Search",
		Description:     "One-time quick search",
		Platforms:       req.Platforms,
		Keywords:        req.Keywords,
		SearchType:      req.SearchType,
		FileTypes:       req.FileTypes,
		ExcludeRules:    req.ExcludeRules,
		IsActive:        false,

		SearchDepth:     req.SearchDepth,
		MaxResults:      req.MaxResults,
		ConcurrentLimit: req.ConcurrentLimit,
		Status:          "running",
	}

	if err := s.db.Create(task).Error; err != nil {
		return nil, fmt.Errorf("failed to create quick search task: %w", err)
	}

	// 创建搜索运行记录
	run := &models.GlobalSearchRun{
		TaskID:    task.ID,
		UserID:    task.UserID,
		Status:    "running",
		StartedAt: time.Now(),
		Config: models.JSONB{
			"platforms":     req.Platforms,
			"keywords":      req.Keywords,
			"search_type":   req.SearchType,
			"file_types":    req.FileTypes,
			"exclude_rules": req.ExcludeRules,
		},
	}

	if err := s.db.Create(run).Error; err != nil {
		return nil, fmt.Errorf("failed to create search run: %w", err)
	}

	// 提交到调度器执行
	if err := s.scheduler.SubmitGlobalSearchTask(task.ID, task.UserID, 10); err != nil { // 高优先级
		return nil, fmt.Errorf("failed to submit quick search task: %w", err)
	}

	return &QuickSearchResponse{
		TaskID: task.ID,
		RunID:  run.ID,
		Status: "running",
	}, nil
}

// ListRuns 列出搜索运行记录
func (s *GlobalSearchService) ListRuns(ctx context.Context, taskID uuid.UUID, req *ListGlobalSearchRunsRequest) ([]*models.GlobalSearchRun, int64, error) {
	var runs []*models.GlobalSearchRun
	var total int64

	query := s.db.Model(&models.GlobalSearchRun{}).Where("task_id = ?", taskID)

	// 添加过滤条件
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if !req.StartTime.IsZero() {
		query = query.Where("started_at >= ?", req.StartTime)
	}
	if !req.EndTime.IsZero() {
		query = query.Where("started_at <= ?", req.EndTime)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count global search runs: %w", err)
	}

	// 添加排序和分页
	query = query.Order("started_at DESC")
	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}
	if req.Offset > 0 {
		query = query.Offset(req.Offset)
	}

	if err := query.Find(&runs).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list global search runs: %w", err)
	}

	return runs, total, nil
}

// GetRun 获取搜索运行记录
func (s *GlobalSearchService) GetRun(ctx context.Context, runID uuid.UUID) (*models.GlobalSearchRun, error) {
	var run models.GlobalSearchRun
	if err := s.db.First(&run, "id = ?", runID).Error; err != nil {
		return nil, fmt.Errorf("failed to get global search run: %w", err)
	}
	return &run, nil
}

// ListResults 列出搜索结果
func (s *GlobalSearchService) ListResults(ctx context.Context, taskID uuid.UUID, req *ListGlobalSearchResultsRequest) ([]*models.GlobalSearchResult, int64, error) {
	var results []*models.GlobalSearchResult
	var total int64

	query := s.db.Model(&models.GlobalSearchResult{}).Where("task_id = ?", taskID)

	// 添加过滤条件
	if req.Platform != "" {
		query = query.Where("platform = ?", req.Platform)
	}
	if req.RiskLevel != "" {
		query = query.Where("risk_level = ?", req.RiskLevel)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.RepoOwner != "" {
		query = query.Where("repo_owner = ?", req.RepoOwner)
	}
	if req.RepoName != "" {
		query = query.Where("repo_name = ?", req.RepoName)
	}
	if !req.StartTime.IsZero() {
		query = query.Where("created_at >= ?", req.StartTime)
	}
	if !req.EndTime.IsZero() {
		query = query.Where("created_at <= ?", req.EndTime)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count global search results: %w", err)
	}

	// 添加排序和分页
	query = query.Order("created_at DESC")
	if req.Limit > 0 {
		query = query.Limit(req.Limit)
	}
	if req.Offset > 0 {
		query = query.Offset(req.Offset)
	}

	if err := query.Find(&results).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list global search results: %w", err)
	}

	return results, total, nil
}

// GetSearchTrends 获取搜索趋势
func (s *GlobalSearchService) GetSearchTrends(ctx context.Context, userID uuid.UUID, days int) (*SearchTrends, error) {
	trends := &SearchTrends{
		Days: days,
	}

	// 计算时间范围
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -days)

	// 按天统计搜索次数
	var dailyStats []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	if err := s.db.Model(&models.GlobalSearchRun{}).
		Select("DATE(started_at) as date, COUNT(*) as count").
		Where("user_id = ? AND started_at >= ? AND started_at <= ?", userID, startTime, endTime).
		Group("DATE(started_at)").
		Order("date").
		Scan(&dailyStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get daily search stats: %w", err)
	}

	// 转换dailyStats为map格式
	trends.DailySearches = make([]map[string]interface{}, len(dailyStats))
	for i, stat := range dailyStats {
		trends.DailySearches[i] = map[string]interface{}{
			"date":  stat.Date,
			"count": stat.Count,
		}
	}

	// 按平台统计
	var platformStats []struct {
		Platform string `json:"platform"`
		Count    int64  `json:"count"`
	}

	if err := s.db.Model(&models.GlobalSearchResult{}).
		Select("platform, COUNT(*) as count").
		Where("user_id = ? AND created_at >= ? AND created_at <= ?", userID, startTime, endTime).
		Group("platform").
		Order("count DESC").
		Scan(&platformStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get platform stats: %w", err)
	}

	// 转换platformStats为map格式
	trends.PlatformStats = make([]map[string]interface{}, len(platformStats))
	for i, stat := range platformStats {
		trends.PlatformStats[i] = map[string]interface{}{
			"platform": stat.Platform,
			"count":    stat.Count,
		}
	}

	return trends, nil
}

// 辅助方法

func (s *GlobalSearchService) calculateNextRun(scheduleRule string) (time.Time, error) {
	if scheduleRule == "" {
		return time.Time{}, fmt.Errorf("schedule rule is empty")
	}

	// 简单的间隔解析，可以根据需要扩展
	intervalStr := scheduleRule

	var duration time.Duration
	switch intervalStr {
	case "hourly":
		duration = time.Hour
	case "daily":
		duration = 24 * time.Hour
	case "weekly":
		duration = 7 * 24 * time.Hour
	default:
		return time.Time{}, fmt.Errorf("unsupported interval: %s", intervalStr)
	}

	return time.Now().Add(duration), nil
}

// 请求和响应结构

type CreateGlobalSearchTaskRequest struct {
	UserID          uuid.UUID    `json:"user_id"`
	Name            string       `json:"name"`
	Description     string       `json:"description"`
	Platforms       models.JSONB `json:"platforms"`
	Keywords        models.JSONB `json:"keywords"`
	SearchType      string       `json:"search_type"`
	FileTypes       models.JSONB `json:"file_types"`
	ExcludeRules    models.JSONB `json:"exclude_rules"`
	Schedule        models.JSONB `json:"schedule"`
	IsActive        bool         `json:"is_active"`
	SearchDepth     int          `json:"search_depth"`
	MaxResults      int          `json:"max_results"`
	ConcurrentLimit int          `json:"concurrent_limit"`
}

type UpdateGlobalSearchTaskRequest struct {
	Name            *string      `json:"name,omitempty"`
	Description     *string      `json:"description,omitempty"`
	Platforms       models.JSONB `json:"platforms,omitempty"`
	Keywords        models.JSONB `json:"keywords,omitempty"`
	SearchType      *string      `json:"search_type,omitempty"`
	FileTypes       models.JSONB `json:"file_types,omitempty"`
	ExcludeRules    models.JSONB `json:"exclude_rules,omitempty"`
	Schedule        models.JSONB `json:"schedule,omitempty"`
	IsActive        *bool        `json:"is_active,omitempty"`
	SearchDepth     *int         `json:"search_depth,omitempty"`
	MaxResults      *int         `json:"max_results,omitempty"`
	ConcurrentLimit *int         `json:"concurrent_limit,omitempty"`
}

type ListGlobalSearchTasksRequest struct {
	UserID     uuid.UUID `json:"user_id"`
	SearchType string    `json:"search_type"`
	Status     string    `json:"status"`
	IsActive   *bool     `json:"is_active"`
	Limit      int       `json:"limit"`
	Offset     int       `json:"offset"`
}

type QuickSearchRequest struct {
	UserID          uuid.UUID    `json:"user_id"`
	Platforms       models.JSONB `json:"platforms"`
	Keywords        models.JSONB `json:"keywords"`
	SearchType      string       `json:"search_type"`
	FileTypes       models.JSONB `json:"file_types"`
	ExcludeRules    models.JSONB `json:"exclude_rules"`
	SearchDepth     int          `json:"search_depth"`
	MaxResults      int          `json:"max_results"`
	ConcurrentLimit int          `json:"concurrent_limit"`
}

type QuickSearchResponse struct {
	TaskID uuid.UUID `json:"task_id"`
	RunID  uuid.UUID `json:"run_id"`
	Status string    `json:"status"`
}

type ListGlobalSearchRunsRequest struct {
	Status    string    `json:"status"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Limit     int       `json:"limit"`
	Offset    int       `json:"offset"`
}

type ListGlobalSearchResultsRequest struct {
	Platform  string    `json:"platform"`
	RiskLevel string    `json:"risk_level"`
	Status    string    `json:"status"`
	RepoOwner string    `json:"repo_owner"`
	RepoName  string    `json:"repo_name"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Limit     int       `json:"limit"`
	Offset    int       `json:"offset"`
}

type SearchTrends struct {
	Days           int                    `json:"days"`
	DailySearches  []map[string]interface{} `json:"daily_searches"`
	PlatformStats  []map[string]interface{} `json:"platform_stats"`
}
