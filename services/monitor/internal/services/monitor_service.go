package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/github"
	"github.com/godeye/monitor/internal/models"
	"github.com/godeye/monitor/internal/scanner"
)

// MonitorService 监控服务
type MonitorService struct {
	db           *gorm.DB
	redis        *redis.Client
	githubClient *github.Client
	scanEngine   *scanner.Engine
}

// NewMonitorService 创建监控服务
func NewMonitorService(db *gorm.DB, redis *redis.Client, githubClient *github.Client, scanEngine *scanner.Engine) *MonitorService {
	return &MonitorService{
		db:           db,
		redis:        redis,
		githubClient: githubClient,
		scanEngine:   scanEngine,
	}
}

// CreateTaskRequest 创建任务请求
type CreateTaskRequest struct {
	UserID       uuid.UUID              `json:"user_id" validate:"required"`
	Name         string                 `json:"name" validate:"required,min=1,max=255"`
	Description  string                 `json:"description"`
	TargetType   string                 `json:"target_type" validate:"required,oneof=repository organization user platform"`
	TargetValue  string                 `json:"target_value"`
	Keywords     map[string]interface{} `json:"keywords"`
	Patterns     map[string]interface{} `json:"patterns"`
	ExcludeRules map[string]interface{} `json:"exclude_rules"`
	ScanDepth    int                    `json:"scan_depth" validate:"min=1,max=10"`
	MaxFileSize  int64                  `json:"max_file_size" validate:"min=1"`
	IncludeForks bool                   `json:"include_forks"`
	ScheduleType string                 `json:"schedule_type" validate:"oneof=manual interval cron"`
	ScheduleRule string                 `json:"schedule_rule"`
}

// UpdateTaskRequest 更新任务请求
type UpdateTaskRequest struct {
	Name         *string                 `json:"name,omitempty" validate:"omitempty,min=1,max=255"`
	Description  *string                 `json:"description,omitempty"`
	TargetType   *string                 `json:"target_type,omitempty" validate:"omitempty,oneof=repository organization user"`
	TargetValue  *string                 `json:"target_value,omitempty"`
	Keywords     *map[string]interface{} `json:"keywords,omitempty"`
	Patterns     *map[string]interface{} `json:"patterns,omitempty"`
	ExcludeRules *map[string]interface{} `json:"exclude_rules,omitempty"`
	ScanDepth    *int                    `json:"scan_depth,omitempty" validate:"omitempty,min=1,max=10"`
	MaxFileSize  *int64                  `json:"max_file_size,omitempty" validate:"omitempty,min=1"`
	IncludeForks *bool                   `json:"include_forks,omitempty"`
	ScheduleType *string                 `json:"schedule_type,omitempty" validate:"omitempty,oneof=manual interval cron"`
	ScheduleRule *string                 `json:"schedule_rule,omitempty"`
	IsActive     *bool                   `json:"is_active,omitempty"`
}

// ListTasksRequest 列出任务请求
type ListTasksRequest struct {
	UserID   uuid.UUID `json:"user_id" validate:"required"`
	Page     int       `json:"page" validate:"min=1"`
	Limit    int       `json:"limit" validate:"min=1,max=100"`
	Status   string    `json:"status,omitempty"`
	Search   string    `json:"search,omitempty"`
	SortBy   string    `json:"sort_by,omitempty"`
	SortDesc bool      `json:"sort_desc,omitempty"`
}

// CreateTask 创建监控任务
func (s *MonitorService) CreateTask(ctx context.Context, req *CreateTaskRequest) (*models.MonitorTask, error) {
	// 验证目标是否有效（platform类型不需要验证TargetValue）
	if req.TargetType != "platform" {
		if req.TargetValue == "" {
			return nil, fmt.Errorf("target_value is required for target_type: %s", req.TargetType)
		}
		if err := s.validateTarget(ctx, req.TargetType, req.TargetValue); err != nil {
			return nil, fmt.Errorf("invalid target: %w", err)
		}
	}

	// 创建任务
	task := &models.MonitorTask{
		UserID:       req.UserID,
		Name:         req.Name,
		Description:  req.Description,
		TargetType:   req.TargetType,
		TargetValue:  req.TargetValue,
		Keywords:     models.JSONB(req.Keywords),
		Patterns:     models.JSONB(req.Patterns),
		ExcludeRules: models.JSONB(req.ExcludeRules),
		ScanDepth:    req.ScanDepth,
		MaxFileSize:  req.MaxFileSize,
		IncludeForks: req.IncludeForks,
		ScheduleType: req.ScheduleType,
		ScheduleRule: req.ScheduleRule,
		IsActive:     true,
		Status:       "pending",
	}

	// 计算下次运行时间
	if req.ScheduleType != "manual" {
		nextRun, err := s.calculateNextRun(req.ScheduleType, req.ScheduleRule)
		if err != nil {
			return nil, fmt.Errorf("invalid schedule rule: %w", err)
		}
		task.NextRunAt = &nextRun
	}

	// 保存到数据库
	if err := s.db.WithContext(ctx).Create(task).Error; err != nil {
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	return task, nil
}

// GetTask 获取任务详情
func (s *MonitorService) GetTask(ctx context.Context, userID, taskID uuid.UUID) (*models.MonitorTask, error) {
	var task models.MonitorTask
	err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", taskID, userID).
		First(&task).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("task not found")
		}
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	return &task, nil
}

// UpdateTask 更新任务
func (s *MonitorService) UpdateTask(ctx context.Context, userID, taskID uuid.UUID, req *UpdateTaskRequest) (*models.MonitorTask, error) {
	// 获取现有任务
	task, err := s.GetTask(ctx, userID, taskID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	updates := make(map[string]interface{})
	
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.TargetType != nil {
		// 验证新目标
		targetValue := task.TargetValue
		if req.TargetValue != nil {
			targetValue = *req.TargetValue
		}
		if err := s.validateTarget(ctx, *req.TargetType, targetValue); err != nil {
			return nil, fmt.Errorf("invalid target: %w", err)
		}
		updates["target_type"] = *req.TargetType
	}
	if req.TargetValue != nil {
		targetType := task.TargetType
		if req.TargetType != nil {
			targetType = *req.TargetType
		}
		if err := s.validateTarget(ctx, targetType, *req.TargetValue); err != nil {
			return nil, fmt.Errorf("invalid target: %w", err)
		}
		updates["target_value"] = *req.TargetValue
	}
	if req.Keywords != nil {
		updates["keywords"] = models.JSONB(*req.Keywords)
	}
	if req.Patterns != nil {
		updates["patterns"] = models.JSONB(*req.Patterns)
	}
	if req.ExcludeRules != nil {
		updates["exclude_rules"] = models.JSONB(*req.ExcludeRules)
	}
	if req.ScanDepth != nil {
		updates["scan_depth"] = *req.ScanDepth
	}
	if req.MaxFileSize != nil {
		updates["max_file_size"] = *req.MaxFileSize
	}
	if req.IncludeForks != nil {
		updates["include_forks"] = *req.IncludeForks
	}
	if req.ScheduleType != nil {
		updates["schedule_type"] = *req.ScheduleType
		
		// 重新计算下次运行时间
		scheduleRule := task.ScheduleRule
		if req.ScheduleRule != nil {
			scheduleRule = *req.ScheduleRule
		}
		
		if *req.ScheduleType != "manual" {
			nextRun, err := s.calculateNextRun(*req.ScheduleType, scheduleRule)
			if err != nil {
				return nil, fmt.Errorf("invalid schedule rule: %w", err)
			}
			updates["next_run_at"] = nextRun
		} else {
			updates["next_run_at"] = nil
		}
	}
	if req.ScheduleRule != nil {
		updates["schedule_rule"] = *req.ScheduleRule
		
		// 重新计算下次运行时间
		scheduleType := task.ScheduleType
		if req.ScheduleType != nil {
			scheduleType = *req.ScheduleType
		}
		
		if scheduleType != "manual" {
			nextRun, err := s.calculateNextRun(scheduleType, *req.ScheduleRule)
			if err != nil {
				return nil, fmt.Errorf("invalid schedule rule: %w", err)
			}
			updates["next_run_at"] = nextRun
		}
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	// 执行更新
	if err := s.db.WithContext(ctx).Model(task).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update task: %w", err)
	}

	// 重新获取更新后的任务
	return s.GetTask(ctx, userID, taskID)
}

// DeleteTask 删除任务
func (s *MonitorService) DeleteTask(ctx context.Context, userID, taskID uuid.UUID) error {
	result := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", taskID, userID).
		Delete(&models.MonitorTask{})
	
	if result.Error != nil {
		return fmt.Errorf("failed to delete task: %w", result.Error)
	}
	
	if result.RowsAffected == 0 {
		return fmt.Errorf("task not found")
	}

	return nil
}

// ListTasks 列出任务
func (s *MonitorService) ListTasks(ctx context.Context, req *ListTasksRequest) ([]*models.MonitorTask, int64, error) {
	query := s.db.WithContext(ctx).Model(&models.MonitorTask{}).
		Where("user_id = ?", req.UserID)

	// 状态过滤
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 搜索过滤
	if req.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", 
			"%"+req.Search+"%", "%"+req.Search+"%")
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count tasks: %w", err)
	}

	// 排序
	sortBy := "created_at"
	if req.SortBy != "" {
		sortBy = req.SortBy
	}
	
	order := sortBy + " ASC"
	if req.SortDesc {
		order = sortBy + " DESC"
	}
	query = query.Order(order)

	// 分页
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	// 查询结果
	var tasks []*models.MonitorTask
	if err := query.Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list tasks: %w", err)
	}

	return tasks, total, nil
}

// validateTarget 验证目标是否有效
func (s *MonitorService) validateTarget(ctx context.Context, targetType, targetValue string) error {
	switch targetType {
	case "repository":
		// 验证仓库格式和存在性
		parts := strings.Split(targetValue, "/")
		if len(parts) != 2 {
			return fmt.Errorf("repository format should be 'owner/repo'")
		}
		
		_, err := s.githubClient.GetRepository(ctx, parts[0], parts[1])
		if err != nil {
			return fmt.Errorf("repository not found or not accessible: %w", err)
		}
		
	case "organization", "user":
		// 验证用户或组织存在性
		_, err := s.githubClient.ListRepositories(ctx, targetValue, nil)
		if err != nil {
			return fmt.Errorf("user/organization not found or not accessible: %w", err)
		}
		
	default:
		return fmt.Errorf("unsupported target type: %s", targetType)
	}

	return nil
}

// calculateNextRun 计算下次运行时间
func (s *MonitorService) calculateNextRun(scheduleType, scheduleRule string) (time.Time, error) {
	now := time.Now()
	
	switch scheduleType {
	case "interval":
		// 间隔调度，格式：5m, 1h, 1d
		duration, err := time.ParseDuration(scheduleRule)
		if err != nil {
			return time.Time{}, fmt.Errorf("invalid interval format: %w", err)
		}
		return now.Add(duration), nil
		
	case "cron":
		// Cron表达式调度
		// 这里需要使用cron库来解析，暂时简化处理
		return now.Add(time.Hour), nil // 临时返回1小时后
		
	default:
		return time.Time{}, fmt.Errorf("unsupported schedule type: %s", scheduleType)
	}
}
