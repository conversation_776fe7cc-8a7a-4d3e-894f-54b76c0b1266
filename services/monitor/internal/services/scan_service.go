package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/github"
	"github.com/godeye/monitor/internal/models"
	"github.com/godeye/monitor/internal/scanner"
)

// ScanService 扫描服务
type ScanService struct {
	db           *gorm.DB
	redis        *redis.Client
	githubClient *github.Client
	scanEngine   *scanner.Engine
}

// NewScanService 创建扫描服务
func NewScanService(db *gorm.DB, redis *redis.Client, githubClient *github.Client, scanEngine *scanner.Engine) *ScanService {
	return &ScanService{
		db:           db,
		redis:        redis,
		githubClient: githubClient,
		scanEngine:   scanEngine,
	}
}

// StartScanRequest 开始扫描请求
type StartScanRequest struct {
	TaskID uuid.UUID `json:"task_id" validate:"required"`
	UserID uuid.UUID `json:"user_id" validate:"required"`
}

// ScanRunResponse 扫描运行响应
type ScanRunResponse struct {
	*models.ScanRun
	Progress float64 `json:"progress"`
}

// StartScan 开始扫描
func (s *ScanService) StartScan(ctx context.Context, req *StartScanRequest) (*models.ScanRun, error) {
	// 获取任务信息
	var task models.MonitorTask
	if err := s.db.WithContext(ctx).Where("id = ? AND user_id = ?", req.TaskID, req.UserID).First(&task).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("task not found")
		}
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// 检查任务状态
	if !task.IsActive {
		return nil, fmt.Errorf("task is not active")
	}

	if task.Status == "running" {
		return nil, fmt.Errorf("task is already running")
	}

	// 创建扫描运行记录
	scanRun := &models.ScanRun{
		TaskID:    req.TaskID,
		UserID:    req.UserID,
		Status:    "pending",
		StartedAt: time.Now(),
		Config:    models.JSONB{
			"target_type":    task.TargetType,
			"target_value":   task.TargetValue,
			"keywords":       task.Keywords,
			"patterns":       task.Patterns,
			"exclude_rules":  task.ExcludeRules,
			"scan_depth":     task.ScanDepth,
			"max_file_size":  task.MaxFileSize,
			"include_forks":  task.IncludeForks,
		},
	}

	// 保存扫描运行记录
	if err := s.db.WithContext(ctx).Create(scanRun).Error; err != nil {
		return nil, fmt.Errorf("failed to create scan run: %w", err)
	}

	// 更新任务状态
	if err := s.db.WithContext(ctx).Model(&task).Updates(map[string]interface{}{
		"status":      "running",
		"last_run_at": time.Now(),
		"last_run_id": scanRun.ID,
	}).Error; err != nil {
		return nil, fmt.Errorf("failed to update task status: %w", err)
	}

	// 异步执行扫描
	go s.performScan(context.Background(), scanRun, &task)

	return scanRun, nil
}

// GetScanRun 获取扫描运行详情
func (s *ScanService) GetScanRun(ctx context.Context, userID, runID uuid.UUID) (*ScanRunResponse, error) {
	var scanRun models.ScanRun
	if err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", runID, userID).
		Preload("Task").
		First(&scanRun).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("scan run not found")
		}
		return nil, fmt.Errorf("failed to get scan run: %w", err)
	}

	// 计算进度
	progress := s.calculateProgress(&scanRun)

	return &ScanRunResponse{
		ScanRun:  &scanRun,
		Progress: progress,
	}, nil
}

// ListScanRuns 列出扫描运行记录
func (s *ScanService) ListScanRuns(ctx context.Context, userID, taskID uuid.UUID, page, limit int) ([]*models.ScanRun, int64, error) {
	query := s.db.WithContext(ctx).Model(&models.ScanRun{}).
		Where("user_id = ?", userID)

	if taskID != uuid.Nil {
		query = query.Where("task_id = ?", taskID)
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count scan runs: %w", err)
	}

	// 查询结果
	var scanRuns []*models.ScanRun
	offset := (page - 1) * limit
	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Preload("Task").
		Find(&scanRuns).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list scan runs: %w", err)
	}

	return scanRuns, total, nil
}

// StopScan 停止扫描
func (s *ScanService) StopScan(ctx context.Context, userID, runID uuid.UUID) error {
	// 获取扫描运行记录
	var scanRun models.ScanRun
	if err := s.db.WithContext(ctx).
		Where("id = ? AND user_id = ?", runID, userID).
		First(&scanRun).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("scan run not found")
		}
		return fmt.Errorf("failed to get scan run: %w", err)
	}

	// 检查状态
	if scanRun.Status != "running" && scanRun.Status != "pending" {
		return fmt.Errorf("scan is not running")
	}

	// 更新状态
	now := time.Now()
	duration := int64(now.Sub(scanRun.StartedAt).Seconds())

	updates := map[string]interface{}{
		"status":       "cancelled",
		"completed_at": now,
		"duration":     duration,
	}

	if err := s.db.WithContext(ctx).Model(&scanRun).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update scan run: %w", err)
	}

	// 更新任务状态
	if err := s.db.WithContext(ctx).Model(&models.MonitorTask{}).
		Where("id = ?", scanRun.TaskID).
		Update("status", "completed").Error; err != nil {
		return fmt.Errorf("failed to update task status: %w", err)
	}

	return nil
}

// performScan 执行扫描
func (s *ScanService) performScan(ctx context.Context, scanRun *models.ScanRun, task *models.MonitorTask) {
	// 更新状态为运行中
	s.updateScanRunStatus(ctx, scanRun.ID, "running", nil)

	// 加载扫描规则
	if err := s.loadScanRules(ctx, task); err != nil {
		s.updateScanRunStatus(ctx, scanRun.ID, "failed", err)
		return
	}

	// 构建扫描请求
	scanRequest := scanner.ScanRequest{
		TaskID:       task.ID.String(),
		UserID:       task.UserID.String(),
		TargetType:   task.TargetType,
		TargetValue:  task.TargetValue,
		ScanDepth:    task.ScanDepth,
		MaxFileSize:  task.MaxFileSize,
		IncludeForks: task.IncludeForks,
	}

	// 执行扫描
	stats, resultChan, errorChan := s.scanEngine.Scan(ctx, scanRequest)

	// 处理扫描结果
	go s.processScanResults(ctx, scanRun.ID, resultChan)

	// 处理错误
	go s.processScanErrors(ctx, scanRun.ID, errorChan)

	// 等待扫描完成
	select {
	case <-ctx.Done():
		s.updateScanRunStatus(ctx, scanRun.ID, "cancelled", ctx.Err())
	case <-time.After(time.Hour): // 最大扫描时间1小时
		s.updateScanRunStatus(ctx, scanRun.ID, "timeout", fmt.Errorf("scan timeout"))
	}

	// 更新最终统计
	s.updateScanStats(ctx, scanRun.ID, stats)
}

// loadScanRules 加载扫描规则
func (s *ScanService) loadScanRules(ctx context.Context, task *models.MonitorTask) error {
	// 加载系统默认关键词
	var keywords []models.ScanKeyword
	if err := s.db.WithContext(ctx).
		Where("user_id IS NULL AND is_active = ?", true).
		Find(&keywords).Error; err != nil {
		return fmt.Errorf("failed to load system keywords: %w", err)
	}

	// 加载用户自定义关键词
	var userKeywords []models.ScanKeyword
	if err := s.db.WithContext(ctx).
		Where("user_id = ? AND is_active = ?", task.UserID, true).
		Find(&userKeywords).Error; err != nil {
		return fmt.Errorf("failed to load user keywords: %w", err)
	}

	// 合并关键词
	allKeywords := append(keywords, userKeywords...)

	// 加载到扫描引擎
	if err := s.scanEngine.LoadKeywords(allKeywords); err != nil {
		return fmt.Errorf("failed to load keywords to engine: %w", err)
	}

	if err := s.scanEngine.LoadPatterns(allKeywords); err != nil {
		return fmt.Errorf("failed to load patterns to engine: %w", err)
	}

	// 加载排除规则
	excludeRules := make([]string, 0)
	if task.ExcludeRules != nil {
		if rules, ok := task.ExcludeRules["rules"].([]interface{}); ok {
			for _, rule := range rules {
				if ruleStr, ok := rule.(string); ok {
					excludeRules = append(excludeRules, ruleStr)
				}
			}
		}
	}

	if err := s.scanEngine.LoadExcludeRules(excludeRules); err != nil {
		return fmt.Errorf("failed to load exclude rules: %w", err)
	}

	return nil
}

// processScanResults 处理扫描结果
func (s *ScanService) processScanResults(ctx context.Context, runID uuid.UUID, resultChan <-chan scanner.ScanResult) {
	for result := range resultChan {
		// 转换为数据库模型
		scanResult := &models.ScanResult{
			RunID:        runID,
			TaskID:       uuid.MustParse(result.TaskID),
			UserID:       uuid.MustParse(result.RunID), // 这里RunID实际是UserID
			RepoOwner:    result.RepoOwner,
			RepoName:     result.RepoName,
			RepoURL:      result.RepoURL,
			RepoFullName: result.RepoOwner + "/" + result.RepoName,
			FilePath:     result.FilePath,
			FileName:     result.FileName,
			FileSize:     result.FileSize,
			FileURL:      result.FileURL,
			MatchType:    result.MatchType,
			MatchRule:    result.MatchRule,
			MatchContent: result.MatchContent,
			LineNumber:   result.LineNumber,
			ColumnNumber: result.ColumnNumber,
			Context:      result.Context,
			RiskLevel:    result.RiskLevel,
			Confidence:   result.Confidence,
			ContentHash:  result.ContentHash,
			Status:       "new",
		}

		// 保存结果
		if err := s.db.WithContext(ctx).Create(scanResult).Error; err != nil {
			// 如果是重复内容，跳过
			if strings.Contains(err.Error(), "duplicate") {
				continue
			}
			// 记录错误但继续处理
			fmt.Printf("Failed to save scan result: %v\n", err)
		}
	}
}

// processScanErrors 处理扫描错误
func (s *ScanService) processScanErrors(ctx context.Context, runID uuid.UUID, errorChan <-chan error) {
	var errors []string
	for err := range errorChan {
		errors = append(errors, err.Error())
		fmt.Printf("Scan error: %v\n", err)
	}

	if len(errors) > 0 {
		// 更新扫描运行记录的错误信息
		errorDetails := models.JSONB{"errors": errors}
		s.db.WithContext(ctx).Model(&models.ScanRun{}).
			Where("id = ?", runID).
			Updates(map[string]interface{}{
				"error_details": errorDetails,
			})
	}
}

// updateScanRunStatus 更新扫描运行状态
func (s *ScanService) updateScanRunStatus(ctx context.Context, runID uuid.UUID, status string, err error) {
	updates := map[string]interface{}{
		"status": status,
	}

	if status == "completed" || status == "failed" || status == "cancelled" || status == "timeout" {
		now := time.Now()
		updates["completed_at"] = now

		// 计算运行时长
		var scanRun models.ScanRun
		if dbErr := s.db.WithContext(ctx).Where("id = ?", runID).First(&scanRun).Error; dbErr == nil {
			duration := int64(now.Sub(scanRun.StartedAt).Seconds())
			updates["duration"] = duration
		}
	}

	if err != nil {
		updates["error_message"] = err.Error()
	}

	s.db.WithContext(ctx).Model(&models.ScanRun{}).Where("id = ?", runID).Updates(updates)

	// 更新任务状态
	taskStatus := "completed"
	if status == "failed" {
		taskStatus = "failed"
	}

	s.db.WithContext(ctx).Model(&models.MonitorTask{}).
		Where("last_run_id = ?", runID).
		Update("status", taskStatus)
}

// updateScanStats 更新扫描统计
func (s *ScanService) updateScanStats(ctx context.Context, runID uuid.UUID, stats *scanner.ScanStats) {
	updates := map[string]interface{}{
		"total_repos":   stats.TotalRepos,
		"scanned_repos": stats.ScannedRepos,
		"total_files":   stats.TotalFiles,
		"scanned_files": stats.ScannedFiles,
		"matched_files": stats.MatchedFiles,
		"total_matches": stats.TotalMatches,
	}

	s.db.WithContext(ctx).Model(&models.ScanRun{}).Where("id = ?", runID).Updates(updates)
}

// calculateProgress 计算扫描进度
func (s *ScanService) calculateProgress(scanRun *models.ScanRun) float64 {
	if scanRun.Status == "completed" {
		return 100.0
	}

	if scanRun.Status == "failed" || scanRun.Status == "cancelled" {
		return 0.0
	}

	if scanRun.TotalFiles == 0 {
		return 0.0
	}

	return float64(scanRun.ScannedFiles) / float64(scanRun.TotalFiles) * 100.0
}
