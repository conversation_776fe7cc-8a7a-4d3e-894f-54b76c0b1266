package config

import (
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Environment string        `mapstructure:"environment"`
	LogLevel    string        `mapstructure:"log_level"`
	Server      ServerConfig  `mapstructure:"server"`
	Database    DatabaseConfig `mapstructure:"database"`
	Redis       RedisConfig   `mapstructure:"redis"`
	GitHub      GitHubConfig  `mapstructure:"github"`
	Scanner     ScannerConfig `mapstructure:"scanner"`
	Queue       QueueConfig   `mapstructure:"queue"`
	Auth        AuthConfig    `mapstructure:"auth"`
	RateLimit   RateLimitConfig `mapstructure:"rate_limit"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int           `mapstructure:"port"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host         string `mapstructure:"host"`
	Port         int    `mapstructure:"port"`
	User         string `mapstructure:"user"`
	Password     string `mapstructure:"password"`
	Database     string `mapstructure:"database"`
	SSLMode      string `mapstructure:"ssl_mode"`
	MaxOpenConns int    `mapstructure:"max_open_conns"`
	MaxIdleConns int    `mapstructure:"max_idle_conns"`
	MaxLifetime  time.Duration `mapstructure:"max_lifetime"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Password     string        `mapstructure:"password"`
	Database     int           `mapstructure:"database"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

// GitHubConfig GitHub API配置
type GitHubConfig struct {
	Token           string        `mapstructure:"token"`
	BaseURL         string        `mapstructure:"base_url"`
	UploadURL       string        `mapstructure:"upload_url"`
	RateLimit       int           `mapstructure:"rate_limit"`
	RetryAttempts   int           `mapstructure:"retry_attempts"`
	RetryDelay      time.Duration `mapstructure:"retry_delay"`
	RequestTimeout  time.Duration `mapstructure:"request_timeout"`
	MaxConcurrency  int           `mapstructure:"max_concurrency"`
}

// ScannerConfig 扫描器配置
type ScannerConfig struct {
	MaxFileSize     int64         `mapstructure:"max_file_size"`
	MaxFiles        int           `mapstructure:"max_files"`
	ScanTimeout     time.Duration `mapstructure:"scan_timeout"`
	WorkerCount     int           `mapstructure:"worker_count"`
	BatchSize       int           `mapstructure:"batch_size"`
	KeywordFile     string        `mapstructure:"keyword_file"`
	PatternFile     string        `mapstructure:"pattern_file"`
	ExcludePatterns []string      `mapstructure:"exclude_patterns"`
	IncludeExts     []string      `mapstructure:"include_extensions"`
}

// QueueConfig 队列配置
type QueueConfig struct {
	URL             string        `mapstructure:"url"`
	Exchange        string        `mapstructure:"exchange"`
	RoutingKey      string        `mapstructure:"routing_key"`
	QueueName       string        `mapstructure:"queue_name"`
	ConsumerCount   int           `mapstructure:"consumer_count"`
	PrefetchCount   int           `mapstructure:"prefetch_count"`
	RetryAttempts   int           `mapstructure:"retry_attempts"`
	RetryDelay      time.Duration `mapstructure:"retry_delay"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	ServiceURL string `mapstructure:"service_url"`
	APIKey     string `mapstructure:"api_key"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	RequestsPerMinute int           `mapstructure:"requests_per_minute"`
	BurstSize         int           `mapstructure:"burst_size"`
	CleanupInterval   time.Duration `mapstructure:"cleanup_interval"`
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("/etc/godeye/monitor")

	// 设置环境变量前缀
	viper.SetEnvPrefix("MONITOR")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		log.Println("No config file found, using defaults and environment variables")
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 基础配置
	viper.SetDefault("environment", "development")
	viper.SetDefault("log_level", "info")

	// 服务器配置
	viper.SetDefault("server.port", 8082)
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "120s")

	// 数据库配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.user", "godeye")
	viper.SetDefault("database.password", "godeye123")
	viper.SetDefault("database.database", "godeye_monitor")
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.max_lifetime", "5m")

	// Redis配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.database", 1)
	viper.SetDefault("redis.pool_size", 10)
	viper.SetDefault("redis.min_idle_conns", 2)
	viper.SetDefault("redis.dial_timeout", "5s")
	viper.SetDefault("redis.read_timeout", "3s")
	viper.SetDefault("redis.write_timeout", "3s")

	// GitHub配置
	viper.SetDefault("github.base_url", "https://api.github.com/")
	viper.SetDefault("github.upload_url", "https://uploads.github.com/")
	viper.SetDefault("github.rate_limit", 5000)
	viper.SetDefault("github.retry_attempts", 3)
	viper.SetDefault("github.retry_delay", "1s")
	viper.SetDefault("github.request_timeout", "30s")
	viper.SetDefault("github.max_concurrency", 10)

	// 扫描器配置
	viper.SetDefault("scanner.max_file_size", 1048576) // 1MB
	viper.SetDefault("scanner.max_files", 1000)
	viper.SetDefault("scanner.scan_timeout", "5m")
	viper.SetDefault("scanner.worker_count", 5)
	viper.SetDefault("scanner.batch_size", 100)
	viper.SetDefault("scanner.keyword_file", "./configs/keywords.txt")
	viper.SetDefault("scanner.pattern_file", "./configs/patterns.txt")
	viper.SetDefault("scanner.exclude_patterns", []string{
		"*.min.js", "*.min.css", "node_modules/*", ".git/*", "vendor/*",
	})
	viper.SetDefault("scanner.include_extensions", []string{
		".js", ".ts", ".py", ".java", ".go", ".php", ".rb", ".cs", ".cpp", ".c",
		".h", ".hpp", ".sql", ".json", ".xml", ".yaml", ".yml", ".env", ".config",
	})

	// 队列配置
	viper.SetDefault("queue.url", "amqp://guest:guest@localhost:5672/")
	viper.SetDefault("queue.exchange", "godeye.monitor")
	viper.SetDefault("queue.routing_key", "scan.task")
	viper.SetDefault("queue.queue_name", "scan_tasks")
	viper.SetDefault("queue.consumer_count", 3)
	viper.SetDefault("queue.prefetch_count", 10)
	viper.SetDefault("queue.retry_attempts", 3)
	viper.SetDefault("queue.retry_delay", "5s")

	// 认证配置
	viper.SetDefault("auth.service_url", "http://localhost:8081")
	viper.SetDefault("auth.api_key", "")

	// 限流配置
	viper.SetDefault("rate_limit.requests_per_minute", 100)
	viper.SetDefault("rate_limit.burst_size", 20)
	viper.SetDefault("rate_limit.cleanup_interval", "1m")
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.User, c.Password, c.Database, c.SSLMode)
}

// GetRedisAddr 获取Redis地址
func (c *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}
