package github

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/google/go-github/v56/github"
	"golang.org/x/oauth2"
	"golang.org/x/time/rate"
)

// Client GitHub API客户端
type Client struct {
	client      *github.Client
	rateLimiter *rate.Limiter
	token       string
	maxRetries  int
	retryDelay  time.Duration
}

// Config GitHub客户端配置
type Config struct {
	Token          string
	BaseURL        string
	UploadURL      string
	RateLimit      int
	MaxRetries     int
	RetryDelay     time.Duration
	RequestTimeout time.Duration
}

// NewClient 创建GitHub客户端
func NewClient(config Config) (*Client, error) {
	if config.Token == "" {
		return nil, fmt.Errorf("GitHub token is required")
	}

	// 创建OAuth2客户端
	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: config.Token},
	)
	tc := oauth2.NewClient(context.Background(), ts)

	// 设置请求超时
	if config.RequestTimeout > 0 {
		tc.Timeout = config.RequestTimeout
	}

	// 创建GitHub客户端
	client := github.NewClient(tc)

	// 设置自定义URL（如果提供）
	if config.BaseURL != "" {
		baseURL, err := client.BaseURL.Parse(config.BaseURL)
		if err != nil {
			return nil, fmt.Errorf("invalid base URL: %w", err)
		}
		client.BaseURL = baseURL
	}

	if config.UploadURL != "" {
		uploadURL, err := client.UploadURL.Parse(config.UploadURL)
		if err != nil {
			return nil, fmt.Errorf("invalid upload URL: %w", err)
		}
		client.UploadURL = uploadURL
	}

	// 创建限流器
	rateLimit := config.RateLimit
	if rateLimit <= 0 {
		rateLimit = 5000 // GitHub默认限制
	}
	rateLimiter := rate.NewLimiter(rate.Limit(rateLimit)/3600, 1) // 每小时限制转换为每秒

	maxRetries := config.MaxRetries
	if maxRetries <= 0 {
		maxRetries = 3
	}

	retryDelay := config.RetryDelay
	if retryDelay <= 0 {
		retryDelay = time.Second
	}

	return &Client{
		client:      client,
		rateLimiter: rateLimiter,
		token:       config.Token,
		maxRetries:  maxRetries,
		retryDelay:  retryDelay,
	}, nil
}

// SearchRepositories 搜索仓库
func (c *Client) SearchRepositories(ctx context.Context, query string, opts *github.SearchOptions) (*github.RepositoriesSearchResult, error) {
	return c.withRetrySearch(func() (*github.RepositoriesSearchResult, *github.Response, error) {
		if err := c.rateLimiter.Wait(ctx); err != nil {
			return nil, nil, err
		}
		return c.client.Search.Repositories(ctx, query, opts)
	})
}

// GetRepository 获取仓库信息
func (c *Client) GetRepository(ctx context.Context, owner, repo string) (*github.Repository, error) {
	result, err := c.withRetry(func() (*github.Repository, *github.Response, error) {
		if err := c.rateLimiter.Wait(ctx); err != nil {
			return nil, nil, err
		}
		return c.client.Repositories.Get(ctx, owner, repo)
	})
	return result, err
}

// ListRepositories 列出用户或组织的仓库
func (c *Client) ListRepositories(ctx context.Context, owner string, opts *github.RepositoryListOptions) ([]*github.Repository, error) {
	var allRepos []*github.Repository
	
	for {
		repos, resp, err := c.withRetryList(func() ([]*github.Repository, *github.Response, error) {
			if err := c.rateLimiter.Wait(ctx); err != nil {
				return nil, nil, err
			}
			return c.client.Repositories.List(ctx, owner, opts)
		})
		
		if err != nil {
			return nil, err
		}
		
		allRepos = append(allRepos, repos...)
		
		if resp.NextPage == 0 {
			break
		}
		opts.Page = resp.NextPage
	}
	
	return allRepos, nil
}

// GetRepositoryContent 获取仓库内容
func (c *Client) GetRepositoryContent(ctx context.Context, owner, repo, path string, opts *github.RepositoryContentGetOptions) (*github.RepositoryContent, []*github.RepositoryContent, error) {
	file, dir, resp, err := c.withRetryContent(func() (*github.RepositoryContent, []*github.RepositoryContent, *github.Response, error) {
		if err := c.rateLimiter.Wait(ctx); err != nil {
			return nil, nil, nil, err
		}
		return c.client.Repositories.GetContents(ctx, owner, repo, path, opts)
	})
	
	if err != nil {
		return nil, nil, err
	}
	
	_ = resp // 忽略响应对象
	return file, dir, nil
}

// GetFileContent 获取文件内容
func (c *Client) GetFileContent(ctx context.Context, owner, repo, path string, opts *github.RepositoryContentGetOptions) (string, error) {
	file, _, err := c.GetRepositoryContent(ctx, owner, repo, path, opts)
	if err != nil {
		return "", err
	}
	
	if file == nil {
		return "", fmt.Errorf("file not found: %s", path)
	}
	
	content, err := file.GetContent()
	if err != nil {
		return "", fmt.Errorf("failed to decode file content: %w", err)
	}
	
	return content, nil
}

// ListRepositoryFiles 递归列出仓库文件
func (c *Client) ListRepositoryFiles(ctx context.Context, owner, repo, path string, maxDepth int) ([]FileInfo, error) {
	var files []FileInfo
	
	err := c.listFilesRecursive(ctx, owner, repo, path, "", maxDepth, 0, &files)
	if err != nil {
		return nil, err
	}
	
	return files, nil
}

// FileInfo 文件信息
type FileInfo struct {
	Path        string
	Name        string
	Size        int
	Type        string
	DownloadURL string
	HTMLURL     string
	SHA         string
}

// listFilesRecursive 递归列出文件
func (c *Client) listFilesRecursive(ctx context.Context, owner, repo, path, basePath string, maxDepth, currentDepth int, files *[]FileInfo) error {
	if currentDepth >= maxDepth {
		return nil
	}
	
	_, contents, err := c.GetRepositoryContent(ctx, owner, repo, path, nil)
	if err != nil {
		return err
	}
	
	for _, content := range contents {
		fullPath := content.GetPath()
		if basePath != "" {
			fullPath = basePath + "/" + content.GetName()
		}
		
		if content.GetType() == "file" {
			*files = append(*files, FileInfo{
				Path:        fullPath,
				Name:        content.GetName(),
				Size:        content.GetSize(),
				Type:        content.GetType(),
				DownloadURL: content.GetDownloadURL(),
				HTMLURL:     content.GetHTMLURL(),
				SHA:         content.GetSHA(),
			})
		} else if content.GetType() == "dir" {
			err := c.listFilesRecursive(ctx, owner, repo, content.GetPath(), fullPath, maxDepth, currentDepth+1, files)
			if err != nil {
				return err
			}
		}
	}
	
	return nil
}

// GetRateLimit 获取API限制信息
func (c *Client) GetRateLimit(ctx context.Context) (*github.RateLimits, error) {
	limits, _, err := c.client.RateLimits(ctx)
	return limits, err
}

// withRetry 重试机制包装器
func (c *Client) withRetry(fn func() (*github.Repository, *github.Response, error)) (*github.Repository, error) {
	var lastErr error
	
	for i := 0; i <= c.maxRetries; i++ {
		result, resp, err := fn()
		if err == nil {
			return result, nil
		}
		
		lastErr = err
		
		// 检查是否是可重试的错误
		if !c.isRetryableError(err, resp) {
			break
		}
		
		if i < c.maxRetries {
			time.Sleep(c.retryDelay * time.Duration(i+1))
		}
	}
	
	return nil, lastErr
}

// withRetryList 列表请求重试包装器
func (c *Client) withRetryList(fn func() ([]*github.Repository, *github.Response, error)) ([]*github.Repository, *github.Response, error) {
	var lastErr error
	var lastResp *github.Response
	
	for i := 0; i <= c.maxRetries; i++ {
		result, resp, err := fn()
		if err == nil {
			return result, resp, nil
		}
		
		lastErr = err
		lastResp = resp
		
		if !c.isRetryableError(err, resp) {
			break
		}
		
		if i < c.maxRetries {
			time.Sleep(c.retryDelay * time.Duration(i+1))
		}
	}
	
	return nil, lastResp, lastErr
}

// withRetrySearch 搜索请求重试包装器
func (c *Client) withRetrySearch(fn func() (*github.RepositoriesSearchResult, *github.Response, error)) (*github.RepositoriesSearchResult, error) {
	var lastErr error

	for i := 0; i <= c.maxRetries; i++ {
		result, resp, err := fn()
		if err == nil {
			return result, nil
		}

		// 检查是否是速率限制错误
		if resp != nil && resp.StatusCode == 403 {
			resetTime := time.Unix(resp.Rate.Reset.Unix(), 0)
			sleepDuration := time.Until(resetTime) + time.Second
			if sleepDuration > 0 && sleepDuration < time.Hour {
				time.Sleep(sleepDuration)
				continue
			}
		}

		lastErr = err
		if i < c.maxRetries {
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	return nil, lastErr
}

// withRetryContent 内容请求重试包装器
func (c *Client) withRetryContent(fn func() (*github.RepositoryContent, []*github.RepositoryContent, *github.Response, error)) (*github.RepositoryContent, []*github.RepositoryContent, *github.Response, error) {
	var lastErr error
	var lastResp *github.Response
	
	for i := 0; i <= c.maxRetries; i++ {
		file, dir, resp, err := fn()
		if err == nil {
			return file, dir, resp, nil
		}
		
		lastErr = err
		lastResp = resp
		
		if !c.isRetryableError(err, resp) {
			break
		}
		
		if i < c.maxRetries {
			time.Sleep(c.retryDelay * time.Duration(i+1))
		}
	}
	
	return nil, nil, lastResp, lastErr
}

// isRetryableError 判断是否为可重试的错误
func (c *Client) isRetryableError(err error, resp *github.Response) bool {
	if resp == nil {
		return true // 网络错误等
	}
	
	// 5xx服务器错误可重试
	if resp.StatusCode >= 500 {
		return true
	}
	
	// 429限流错误可重试
	if resp.StatusCode == http.StatusTooManyRequests {
		return true
	}
	
	// 403可能是限流，检查是否有Retry-After头
	if resp.StatusCode == http.StatusForbidden {
		if resp.Header.Get("X-RateLimit-Remaining") == "0" {
			return true
		}
	}
	
	return false
}
