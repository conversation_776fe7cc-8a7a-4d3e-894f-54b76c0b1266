package scheduler

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/models"
	"github.com/godeye/monitor/internal/services"
)

// Scheduler 任务调度器
type Scheduler struct {
	db           *gorm.DB
	redis        *redis.Client
	scanService  *services.ScanService
	cron         *cron.Cron
	jobs         map[uuid.UUID]cron.EntryID
	mu           sync.RWMutex
	ctx          context.Context
	cancel       context.CancelFunc
	running      bool
}

// Config 调度器配置
type Config struct {
	CheckInterval    time.Duration `json:"check_interval"`
	MaxConcurrentJobs int          `json:"max_concurrent_jobs"`
	JobTimeout       time.Duration `json:"job_timeout"`
}

// NewScheduler 创建调度器
func NewScheduler(db *gorm.DB, redis *redis.Client, scanService *services.ScanService, config Config) *Scheduler {
	ctx, cancel := context.WithCancel(context.Background())
	
	// 创建cron调度器
	cronScheduler := cron.New(cron.WithSeconds())
	
	return &Scheduler{
		db:          db,
		redis:       redis,
		scanService: scanService,
		cron:        cronScheduler,
		jobs:        make(map[uuid.UUID]cron.EntryID),
		ctx:         ctx,
		cancel:      cancel,
		running:     false,
	}
}

// Start 启动调度器
func (s *Scheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("scheduler is already running")
	}

	// 启动cron调度器
	s.cron.Start()

	// 加载现有的定时任务
	if err := s.loadExistingTasks(); err != nil {
		return fmt.Errorf("failed to load existing tasks: %w", err)
	}

	// 启动间隔任务检查器
	go s.runIntervalChecker()

	s.running = true
	log.Println("Task scheduler started")

	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return fmt.Errorf("scheduler is not running")
	}

	// 停止cron调度器
	s.cron.Stop()

	// 取消上下文
	s.cancel()

	// 清空任务
	s.jobs = make(map[uuid.UUID]cron.EntryID)

	s.running = false
	log.Println("Task scheduler stopped")

	return nil
}

// AddTask 添加任务到调度器
func (s *Scheduler) AddTask(task *models.MonitorTask) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !task.IsActive {
		return fmt.Errorf("task is not active")
	}

	switch task.ScheduleType {
	case "cron":
		return s.addCronTask(task)
	case "interval":
		// 间隔任务由间隔检查器处理
		return nil
	case "manual":
		// 手动任务不需要调度
		return nil
	default:
		return fmt.Errorf("unsupported schedule type: %s", task.ScheduleType)
	}
}

// RemoveTask 从调度器移除任务
func (s *Scheduler) RemoveTask(taskID uuid.UUID) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if entryID, exists := s.jobs[taskID]; exists {
		s.cron.Remove(entryID)
		delete(s.jobs, taskID)
		log.Printf("Removed task %s from scheduler", taskID)
	}

	return nil
}

// UpdateTask 更新调度器中的任务
func (s *Scheduler) UpdateTask(task *models.MonitorTask) error {
	// 先移除旧任务
	if err := s.RemoveTask(task.ID); err != nil {
		return err
	}

	// 添加新任务
	if task.IsActive {
		return s.AddTask(task)
	}

	return nil
}

// TriggerTask 手动触发任务
func (s *Scheduler) TriggerTask(taskID uuid.UUID) error {
	// 获取任务信息
	var task models.MonitorTask
	if err := s.db.WithContext(s.ctx).Where("id = ?", taskID).First(&task).Error; err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// 检查任务状态
	if !task.IsActive {
		return fmt.Errorf("task is not active")
	}

	if task.Status == "running" {
		return fmt.Errorf("task is already running")
	}

	// 异步执行任务
	go s.executeTask(&task)

	return nil
}

// GetScheduledTasks 获取已调度的任务
func (s *Scheduler) GetScheduledTasks() []uuid.UUID {
	s.mu.RLock()
	defer s.mu.RUnlock()

	tasks := make([]uuid.UUID, 0, len(s.jobs))
	for taskID := range s.jobs {
		tasks = append(tasks, taskID)
	}

	return tasks
}

// IsRunning 检查调度器是否运行中
func (s *Scheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// loadExistingTasks 加载现有任务
func (s *Scheduler) loadExistingTasks() error {
	var tasks []models.MonitorTask
	if err := s.db.WithContext(s.ctx).
		Where("is_active = ? AND schedule_type IN ?", true, []string{"cron", "interval"}).
		Find(&tasks).Error; err != nil {
		return err
	}

	for _, task := range tasks {
		if err := s.AddTask(&task); err != nil {
			log.Printf("Failed to add task %s to scheduler: %v", task.ID, err)
		}
	}

	log.Printf("Loaded %d existing tasks", len(tasks))
	return nil
}

// addCronTask 添加cron任务
func (s *Scheduler) addCronTask(task *models.MonitorTask) error {
	if task.ScheduleRule == "" {
		return fmt.Errorf("schedule rule is required for cron task")
	}

	cronExpr := task.ScheduleRule
	if cronExpr == "" {
		return fmt.Errorf("cron expression is required")
	}

	// 添加到cron调度器
	entryID, err := s.cron.AddFunc(cronExpr, func() {
		s.executeTask(task)
	})
	if err != nil {
		return fmt.Errorf("failed to add cron job: %w", err)
	}

	s.jobs[task.ID] = entryID
	log.Printf("Added cron task %s with expression: %s", task.ID, cronExpr)

	return nil
}

// runIntervalChecker 运行间隔任务检查器
func (s *Scheduler) runIntervalChecker() {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.checkIntervalTasks()
		}
	}
}

// checkIntervalTasks 检查间隔任务
func (s *Scheduler) checkIntervalTasks() {
	var tasks []models.MonitorTask
	if err := s.db.WithContext(s.ctx).
		Where("is_active = ? AND schedule_type = ?", true, "interval").
		Find(&tasks).Error; err != nil {
		log.Printf("Failed to get interval tasks: %v", err)
		return
	}

	for _, task := range tasks {
		if s.shouldRunIntervalTask(&task) {
			go s.executeTask(&task)
		}
	}
}

// shouldRunIntervalTask 检查间隔任务是否应该运行
func (s *Scheduler) shouldRunIntervalTask(task *models.MonitorTask) bool {
	// 检查任务状态
	if task.Status == "running" {
		return false
	}

	// 获取间隔配置
	if task.ScheduleRule == "" {
		return false
	}

	// 简单解析间隔规则，例如 "30m", "1h", "2h"
	duration, err := time.ParseDuration(task.ScheduleRule)
	if err != nil {
		return false
	}
	intervalMinutes := duration.Minutes()
	if intervalMinutes <= 0 {
		return false
	}

	// 检查上次运行时间
	if task.LastRunAt == nil {
		return true // 从未运行过
	}

	nextRunTime := task.LastRunAt.Add(time.Duration(intervalMinutes) * time.Minute)
	return time.Now().After(nextRunTime)
}

// executeTask 执行任务
func (s *Scheduler) executeTask(task *models.MonitorTask) {
	log.Printf("Executing task: %s", task.ID)

	// 创建扫描请求
	scanRequest := &services.StartScanRequest{
		TaskID: task.ID,
		UserID: task.UserID,
	}

	// 启动扫描
	scanRun, err := s.scanService.StartScan(s.ctx, scanRequest)
	if err != nil {
		log.Printf("Failed to start scan for task %s: %v", task.ID, err)
		
		// 更新任务状态为失败
		s.db.WithContext(s.ctx).Model(task).Updates(map[string]interface{}{
			"status":       "failed",
			"last_run_at":  time.Now(),
			"error_message": err.Error(),
		})
		return
	}

	log.Printf("Started scan run %s for task %s", scanRun.ID, task.ID)
}

// GetTaskStatus 获取任务状态
func (s *Scheduler) GetTaskStatus(taskID uuid.UUID) (string, error) {
	var task models.MonitorTask
	if err := s.db.WithContext(s.ctx).
		Where("id = ?", taskID).
		First(&task).Error; err != nil {
		return "", fmt.Errorf("failed to get task: %w", err)
	}

	return task.Status, nil
}

// GetRunningTasks 获取正在运行的任务
func (s *Scheduler) GetRunningTasks() ([]models.MonitorTask, error) {
	var tasks []models.MonitorTask
	if err := s.db.WithContext(s.ctx).
		Where("status = ?", "running").
		Find(&tasks).Error; err != nil {
		return nil, fmt.Errorf("failed to get running tasks: %w", err)
	}

	return tasks, nil
}

// GetSchedulerStats 获取调度器统计信息
func (s *Scheduler) GetSchedulerStats() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 获取任务统计
	var stats struct {
		TotalTasks   int64 `json:"total_tasks"`
		ActiveTasks  int64 `json:"active_tasks"`
		RunningTasks int64 `json:"running_tasks"`
		CronTasks    int64 `json:"cron_tasks"`
		IntervalTasks int64 `json:"interval_tasks"`
		ManualTasks  int64 `json:"manual_tasks"`
	}

	s.db.WithContext(s.ctx).Model(&models.MonitorTask{}).Count(&stats.TotalTasks)
	s.db.WithContext(s.ctx).Model(&models.MonitorTask{}).Where("is_active = ?", true).Count(&stats.ActiveTasks)
	s.db.WithContext(s.ctx).Model(&models.MonitorTask{}).Where("status = ?", "running").Count(&stats.RunningTasks)
	s.db.WithContext(s.ctx).Model(&models.MonitorTask{}).Where("schedule_type = ?", "cron").Count(&stats.CronTasks)
	s.db.WithContext(s.ctx).Model(&models.MonitorTask{}).Where("schedule_type = ?", "interval").Count(&stats.IntervalTasks)
	s.db.WithContext(s.ctx).Model(&models.MonitorTask{}).Where("schedule_type = ?", "manual").Count(&stats.ManualTasks)

	return map[string]interface{}{
		"running":         s.running,
		"scheduled_jobs":  len(s.jobs),
		"total_tasks":     stats.TotalTasks,
		"active_tasks":    stats.ActiveTasks,
		"running_tasks":   stats.RunningTasks,
		"cron_tasks":      stats.CronTasks,
		"interval_tasks":  stats.IntervalTasks,
		"manual_tasks":    stats.ManualTasks,
	}
}
