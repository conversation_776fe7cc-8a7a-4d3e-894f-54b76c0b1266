package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MonitorTask 监控任务模型
type MonitorTask struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID      uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	Name        string    `gorm:"size:255;not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	
	// 监控配置
	TargetType   string    `gorm:"size:50;not null;default:'repository'" json:"target_type"` // repository, organization, user
	TargetValue  string    `gorm:"size:255;not null" json:"target_value"`
	Platform     string    `gorm:"size:50;not null;default:'github'" json:"platform"` // github, gitlab, gitee
	Keywords     JSONB     `gorm:"type:jsonb" json:"keywords"`
	Patterns     JSONB     `gorm:"type:jsonb" json:"patterns"`
	ExcludeRules JSONB     `gorm:"type:jsonb" json:"exclude_rules"`
	
	// 扫描设置
	ScanDepth    int  `gorm:"default:3" json:"scan_depth"`
	MaxFileSize  int64 `gorm:"default:1048576" json:"max_file_size"` // 1MB
	IncludeForks bool `gorm:"default:false" json:"include_forks"`
	
	// 调度设置
	IsActive     bool      `gorm:"default:true" json:"is_active"`
	ScheduleType string    `gorm:"size:50;default:'manual'" json:"schedule_type"` // manual, interval, cron
	ScheduleRule string    `gorm:"size:255" json:"schedule_rule"`
	NextRunAt    *time.Time `gorm:"index" json:"next_run_at"`
	
	// 状态信息
	Status       string    `gorm:"size:50;default:'pending'" json:"status"` // pending, running, completed, failed, paused
	LastRunAt    *time.Time `json:"last_run_at"`
	LastRunID    *uuid.UUID `gorm:"type:uuid" json:"last_run_id"`
	
	// 统计信息
	TotalRuns    int `gorm:"default:0" json:"total_runs"`
	SuccessRuns  int `gorm:"default:0" json:"success_runs"`
	FailedRuns   int `gorm:"default:0" json:"failed_runs"`
	
	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	
	// 关联
	ScanRuns []ScanRun `gorm:"foreignKey:TaskID" json:"scan_runs,omitempty"`
}

// ScanRun 扫描运行记录
type ScanRun struct {
	ID       uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	TaskID   uuid.UUID `gorm:"type:uuid;not null;index" json:"task_id"`
	UserID   uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	
	// 运行信息
	Status      string    `gorm:"size:50;default:'pending'" json:"status"` // pending, running, completed, failed, cancelled
	StartedAt   time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	Duration    int64     `json:"duration"` // 运行时长（秒）
	
	// 扫描配置快照
	Config JSONB `gorm:"type:jsonb" json:"config"`
	
	// 扫描统计
	TotalRepos     int  `gorm:"default:0" json:"total_repos"`
	ScannedRepos   int  `gorm:"default:0" json:"scanned_repos"`
	TotalFiles     int  `gorm:"default:0" json:"total_files"`
	ScannedFiles   int  `gorm:"default:0" json:"scanned_files"`
	MatchedFiles   int  `gorm:"default:0" json:"matched_files"`
	TotalMatches   int  `gorm:"default:0" json:"total_matches"`
	TotalResults   int  `gorm:"default:0" json:"total_results"`
	ScanStats      JSONB `gorm:"type:jsonb" json:"scan_stats,omitempty"`
	
	// 错误信息
	ErrorMessage string `gorm:"type:text" json:"error_message,omitempty"`
	ErrorDetails JSONB  `gorm:"type:jsonb" json:"error_details,omitempty"`
	
	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	
	// 关联
	Task        MonitorTask  `gorm:"foreignKey:TaskID" json:"task,omitempty"`
	ScanResults []ScanResult `gorm:"foreignKey:RunID" json:"scan_results,omitempty"`
}

// ScanResult 扫描结果
type ScanResult struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	RunID  uuid.UUID `gorm:"type:uuid;not null;index" json:"run_id"`
	TaskID uuid.UUID `gorm:"type:uuid;not null;index" json:"task_id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	
	// 仓库信息
	RepoOwner    string `gorm:"size:255;not null;index" json:"repo_owner"`
	RepoName     string `gorm:"size:255;not null;index" json:"repo_name"`
	RepoURL      string `gorm:"size:500;not null" json:"repo_url"`
	RepoFullName string `gorm:"size:500;not null;index" json:"repo_full_name"`
	
	// 文件信息
	FilePath     string `gorm:"size:1000;not null" json:"file_path"`
	FileName     string `gorm:"size:255;not null" json:"file_name"`
	FileSize     int64  `json:"file_size"`
	FileURL      string `gorm:"size:1000" json:"file_url"`
	
	// 匹配信息
	MatchType    string `gorm:"size:50;not null" json:"match_type"` // keyword, pattern, custom
	MatchRule    string `gorm:"type:text;not null" json:"match_rule"`
	MatchContent string `gorm:"type:text" json:"match_content"`
	LineNumber   int    `json:"line_number"`
	ColumnNumber int    `json:"column_number"`
	Context      string `gorm:"type:text" json:"context"` // 上下文内容
	
	// 风险评估
	RiskLevel   string  `gorm:"size:50;default:'medium'" json:"risk_level"` // low, medium, high, critical
	Confidence  float64 `gorm:"default:0.5" json:"confidence"` // 0.0-1.0
	Severity    int     `gorm:"default:5" json:"severity"` // 1-10
	
	// 状态信息
	Status       string    `gorm:"size:50;default:'new'" json:"status"` // new, reviewed, ignored, resolved, false_positive
	ReviewedBy   *uuid.UUID `gorm:"type:uuid" json:"reviewed_by,omitempty"`
	ReviewedAt   *time.Time `json:"reviewed_at,omitempty"`
	ReviewNotes  string    `gorm:"type:text" json:"review_notes,omitempty"`
	
	// 去重标识
	ContentHash  string `gorm:"size:64;index" json:"content_hash"`
	
	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
	
	// 关联
	Run  ScanRun     `gorm:"foreignKey:RunID" json:"run,omitempty"`
	Task MonitorTask `gorm:"foreignKey:TaskID" json:"task,omitempty"`
}

// Repository 仓库信息缓存
type Repository struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	GitHubID    int64     `gorm:"unique;not null" json:"github_id"`
	Owner       string    `gorm:"size:255;not null;index" json:"owner"`
	Name        string    `gorm:"size:255;not null;index" json:"name"`
	FullName    string    `gorm:"size:500;not null;unique" json:"full_name"`
	Description string    `gorm:"type:text" json:"description"`
	
	// 仓库属性
	IsPrivate    bool   `json:"is_private"`
	IsFork       bool   `json:"is_fork"`
	IsArchived   bool   `json:"is_archived"`
	Language     string `gorm:"size:100" json:"language"`
	Size         int64  `json:"size"`
	StarCount    int    `json:"star_count"`
	ForkCount    int    `json:"fork_count"`
	
	// URL信息
	HTMLURL     string `gorm:"size:500" json:"html_url"`
	CloneURL    string `gorm:"size:500" json:"clone_url"`
	GitURL      string `gorm:"size:500" json:"git_url"`
	SSHURL      string `gorm:"size:500" json:"ssh_url"`
	
	// 时间信息
	GitHubCreatedAt time.Time  `json:"github_created_at"`
	GitHubUpdatedAt time.Time  `json:"github_updated_at"`
	GitHubPushedAt  *time.Time `json:"github_pushed_at,omitempty"`
	LastScanAt      *time.Time `json:"last_scan_at,omitempty"`
	
	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// ScanKeyword 扫描关键词
type ScanKeyword struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID      *uuid.UUID `gorm:"type:uuid;index" json:"user_id,omitempty"` // null表示系统默认
	Category    string    `gorm:"size:100;not null;index" json:"category"`
	Keyword     string    `gorm:"size:500;not null" json:"keyword"`
	Pattern     string    `gorm:"size:1000" json:"pattern"` // 正则表达式
	Description string    `gorm:"type:text" json:"description"`
	RiskLevel   string    `gorm:"size:50;default:'medium'" json:"risk_level"`
	IsActive    bool      `gorm:"default:true" json:"is_active"`

	// 平台支持
	Platforms   JSONB `gorm:"type:jsonb" json:"platforms"` // 支持的平台列表
	FileTypes   JSONB `gorm:"type:jsonb" json:"file_types"` // 适用的文件类型
	ExcludeRules JSONB `gorm:"type:jsonb" json:"exclude_rules"` // 排除规则

	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// PlatformAccount 平台账号管理
type PlatformAccount struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID      uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	Platform    string    `gorm:"size:50;not null;index" json:"platform"` // github, gitlab, gitee
	AccountType string    `gorm:"size:50;not null" json:"account_type"` // token, oauth, username_password

	// 认证信息 (加密存储)
	Username    string `gorm:"size:255" json:"username,omitempty"`
	Token       string `gorm:"size:500" json:"token,omitempty"`
	RefreshToken string `gorm:"size:500" json:"refresh_token,omitempty"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`

	// 配额信息
	RateLimit     int `gorm:"default:0" json:"rate_limit"`
	RateRemaining int `gorm:"default:0" json:"rate_remaining"`
	RateResetAt   *time.Time `json:"rate_reset_at,omitempty"`

	// 状态信息
	Status      string    `gorm:"size:50;default:'active'" json:"status"` // active, suspended, expired, invalid
	LastUsedAt  *time.Time `json:"last_used_at,omitempty"`
	LastError   string    `gorm:"type:text" json:"last_error,omitempty"`

	// 统计信息
	TotalRequests   int `gorm:"default:0" json:"total_requests"`
	SuccessRequests int `gorm:"default:0" json:"success_requests"`
	FailedRequests  int `gorm:"default:0" json:"failed_requests"`

	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// GlobalSearchTask 全量搜索任务
type GlobalSearchTask struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID      uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`
	Name        string    `gorm:"size:255;not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`

	// 搜索配置
	Platforms   JSONB `gorm:"type:jsonb;not null" json:"platforms"` // 搜索的平台列表
	Keywords    JSONB `gorm:"type:jsonb;not null" json:"keywords"`  // 关键词列表
	SearchType  string `gorm:"size:50;default:'code'" json:"search_type"` // code, repository, both
	FileTypes   JSONB `gorm:"type:jsonb" json:"file_types"` // 文件类型过滤
	ExcludeRules JSONB `gorm:"type:jsonb" json:"exclude_rules"` // 排除规则

	// 搜索策略
	MaxResults   int    `gorm:"default:1000" json:"max_results"` // 每个平台最大结果数
	SearchDepth  int    `gorm:"default:10" json:"search_depth"`  // 搜索深度（页数）
	ConcurrentLimit int `gorm:"default:3" json:"concurrent_limit"` // 并发限制

	// 调度设置
	IsActive     bool      `gorm:"default:true" json:"is_active"`
	ScheduleType string    `gorm:"size:50;default:'manual'" json:"schedule_type"` // manual, interval, cron
	ScheduleRule string    `gorm:"size:255" json:"schedule_rule"`
	NextRunAt    *time.Time `gorm:"index" json:"next_run_at"`

	// 状态信息
	Status       string    `gorm:"size:50;default:'pending'" json:"status"` // pending, running, completed, failed, paused
	LastRunAt    *time.Time `json:"last_run_at"`
	LastRunID    *uuid.UUID `gorm:"type:uuid" json:"last_run_id"`

	// 统计信息
	TotalRuns    int `gorm:"default:0" json:"total_runs"`
	SuccessRuns  int `gorm:"default:0" json:"success_runs"`
	FailedRuns   int `gorm:"default:0" json:"failed_runs"`

	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联
	SearchRuns []GlobalSearchRun `gorm:"foreignKey:TaskID" json:"search_runs,omitempty"`
}

// GlobalSearchRun 全量搜索运行记录
type GlobalSearchRun struct {
	ID       uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	TaskID   uuid.UUID `gorm:"type:uuid;not null;index" json:"task_id"`
	UserID   uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`

	// 运行信息
	Status      string    `gorm:"size:50;default:'pending'" json:"status"` // pending, running, completed, failed, cancelled
	StartedAt   time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	Duration    int64     `json:"duration"` // 运行时长（秒）

	// 搜索配置快照
	Config JSONB `gorm:"type:jsonb" json:"config"`

	// 平台统计
	PlatformStats JSONB `gorm:"type:jsonb" json:"platform_stats"` // 各平台的搜索统计

	// 搜索统计
	TotalQueries    int `gorm:"default:0" json:"total_queries"`
	CompletedQueries int `gorm:"default:0" json:"completed_queries"`
	TotalResults    int `gorm:"default:0" json:"total_results"`
	UniqueResults   int `gorm:"default:0" json:"unique_results"`
	DuplicateResults int `gorm:"default:0" json:"duplicate_results"`

	// 错误信息
	ErrorMessage string `gorm:"type:text" json:"error_message,omitempty"`
	ErrorDetails JSONB  `gorm:"type:jsonb" json:"error_details,omitempty"`

	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联
	Task           GlobalSearchTask   `gorm:"foreignKey:TaskID" json:"task,omitempty"`
	SearchResults  []GlobalSearchResult `gorm:"foreignKey:RunID" json:"search_results,omitempty"`
}

// GlobalSearchResult 全量搜索结果
type GlobalSearchResult struct {
	ID     uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	RunID  uuid.UUID `gorm:"type:uuid;not null;index" json:"run_id"`
	TaskID uuid.UUID `gorm:"type:uuid;not null;index" json:"task_id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;index" json:"user_id"`

	// 平台信息
	Platform     string `gorm:"size:50;not null;index" json:"platform"` // github, gitlab, gitee
	PlatformID   string `gorm:"size:255;index" json:"platform_id"` // 平台上的唯一标识

	// 仓库信息
	RepoOwner    string `gorm:"size:255;not null;index" json:"repo_owner"`
	RepoName     string `gorm:"size:255;not null;index" json:"repo_name"`
	RepoURL      string `gorm:"type:text;not null" json:"repo_url"`
	RepoFullName string `gorm:"size:1000;not null;index" json:"repo_full_name"`

	// 文件信息
	FilePath     string `gorm:"type:text;not null" json:"file_path"`
	FileName     string `gorm:"size:255;not null" json:"file_name"`
	FileSize     int64  `json:"file_size"`
	FileURL      string `gorm:"type:text" json:"file_url"`

	// 匹配信息
	MatchType    string `gorm:"size:50;not null" json:"match_type"` // keyword, pattern, custom
	MatchRule    string `gorm:"type:text;not null" json:"match_rule"`
	MatchContent string `gorm:"type:text" json:"match_content"`
	LineNumber   int    `json:"line_number"`
	ColumnNumber int    `json:"column_number"`
	Context      string `gorm:"type:text" json:"context"` // 上下文内容

	// 风险评估
	RiskLevel   string  `gorm:"size:50;default:'medium'" json:"risk_level"` // low, medium, high, critical
	Confidence  float64 `gorm:"default:0.5" json:"confidence"` // 0.0-1.0
	Severity    int     `gorm:"default:5" json:"severity"` // 1-10

	// 状态信息
	Status       string    `gorm:"size:50;default:'new'" json:"status"` // new, reviewed, ignored, resolved, false_positive
	ReviewedBy   *uuid.UUID `gorm:"type:uuid" json:"reviewed_by,omitempty"`
	ReviewedAt   *time.Time `json:"reviewed_at,omitempty"`
	ReviewNotes  string    `gorm:"type:text" json:"review_notes,omitempty"`

	// 去重标识
	ContentHash  string `gorm:"size:64;index" json:"content_hash"`
	SimilarityHash string `gorm:"size:64;index" json:"similarity_hash"` // 用于相似度检测

	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联
	Run  GlobalSearchRun  `gorm:"foreignKey:RunID" json:"run,omitempty"`
	Task GlobalSearchTask `gorm:"foreignKey:TaskID" json:"task,omitempty"`
}

// JSONB 自定义JSONB类型
type JSONB map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONB", value)
	}
	
	return json.Unmarshal(bytes, j)
}

// TableName 指定表名
func (MonitorTask) TableName() string {
	return "monitor_tasks"
}

func (ScanRun) TableName() string {
	return "scan_runs"
}

func (ScanResult) TableName() string {
	return "scan_results"
}

func (Repository) TableName() string {
	return "repositories"
}

func (ScanKeyword) TableName() string {
	return "scan_keywords"
}

func (PlatformAccount) TableName() string {
	return "platform_accounts"
}

func (GlobalSearchTask) TableName() string {
	return "global_search_tasks"
}

func (GlobalSearchRun) TableName() string {
	return "global_search_runs"
}

func (GlobalSearchResult) TableName() string {
	return "global_search_results"
}

// BeforeCreate GORM钩子
func (m *MonitorTask) BeforeCreate(tx *gorm.DB) error {
	if m.ID == uuid.Nil {
		m.ID = uuid.New()
	}
	return nil
}

func (s *ScanRun) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

func (s *ScanResult) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

func (r *Repository) BeforeCreate(tx *gorm.DB) error {
	if r.ID == uuid.Nil {
		r.ID = uuid.New()
	}
	return nil
}

func (s *ScanKeyword) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

func (p *PlatformAccount) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

func (g *GlobalSearchTask) BeforeCreate(tx *gorm.DB) error {
	if g.ID == uuid.Nil {
		g.ID = uuid.New()
	}
	return nil
}

func (g *GlobalSearchRun) BeforeCreate(tx *gorm.DB) error {
	if g.ID == uuid.Nil {
		g.ID = uuid.New()
	}
	return nil
}

func (g *GlobalSearchResult) BeforeCreate(tx *gorm.DB) error {
	if g.ID == uuid.Nil {
		g.ID = uuid.New()
	}
	return nil
}
