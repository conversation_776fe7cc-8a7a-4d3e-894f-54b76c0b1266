package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Whitelist 白名单模型
type Whitelist struct {
	ID          uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID      uuid.UUID      `gorm:"type:uuid;not null;index" json:"user_id"`
	Type        string         `gorm:"size:50;not null" json:"type"` // repository, user, organization
	Platform    string         `gorm:"size:50;not null" json:"platform"` // github, gitlab, gitee
	Identifier  string         `gorm:"size:500;not null" json:"identifier"` // repo full name, username, org name
	RepoOwner   *string        `gorm:"size:255" json:"repo_owner,omitempty"`
	RepoName    *string        `gorm:"size:255" json:"repo_name,omitempty"`
	RepoURL     *string        `gorm:"type:text" json:"repo_url,omitempty"`
	Reason      string         `gorm:"type:text" json:"reason"` // 加入白名单的原因
	Status      string         `gorm:"size:20;not null;default:'active'" json:"status"` // active, disabled
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TableName 指定表名
func (Whitelist) TableName() string {
	return "whitelists"
}

// BeforeCreate 创建前的钩子
func (w *Whitelist) BeforeCreate(tx *gorm.DB) error {
	if w.ID == uuid.Nil {
		w.ID = uuid.New()
	}
	return nil
}

// WhitelistCreateRequest 创建白名单请求
type WhitelistCreateRequest struct {
	Type       string `json:"type" binding:"required,oneof=repository user organization file"`
	Platform   string `json:"platform" binding:"required,oneof=github gitlab gitee"`
	Identifier string `json:"identifier" binding:"required"`
	RepoOwner  string `json:"repo_owner,omitempty"`
	RepoName   string `json:"repo_name,omitempty"`
	RepoURL    string `json:"repo_url,omitempty"`
	Reason     string `json:"reason"`
}

// WhitelistUpdateRequest 更新白名单请求
type WhitelistUpdateRequest struct {
	Reason string `json:"reason"`
	Status string `json:"status" binding:"omitempty,oneof=active disabled"`
}

// WhitelistListRequest 白名单列表请求
type WhitelistListRequest struct {
	Type     string `form:"type"`
	Platform string `form:"platform"`
	Status   string `form:"status"`
	Keyword  string `form:"keyword"`
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
}

// WhitelistListResponse 白名单列表响应
type WhitelistListResponse struct {
	Items      []Whitelist `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}
