package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/godeye/monitor/internal/scheduler"
	"github.com/godeye/monitor/pkg/errors"
)

// SchedulerHandler 调度器处理器
type SchedulerHandler struct {
	scheduler *scheduler.Scheduler
}

// NewSchedulerHandler 创建调度器处理器
func NewSchedulerHandler(scheduler *scheduler.Scheduler) *SchedulerHandler {
	return &SchedulerHandler{
		scheduler: scheduler,
	}
}

// GetSchedulerStatus 获取调度器状态
// @Summary 获取调度器状态
// @Description 获取任务调度器的运行状态和统计信息
// @Tags scheduler
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=map[string]interface{}}
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/scheduler/status [get]
func (h *SchedulerHandler) GetSchedulerStatus(c *gin.Context) {
	// 获取调度器统计信息
	stats := h.scheduler.GetSchedulerStats()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    stats,
	})
}

// TriggerTask 手动触发任务
// @Summary 手动触发任务
// @Description 手动触发指定的监控任务
// @Tags scheduler
// @Accept json
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/scheduler/trigger/{id} [post]
func (h *SchedulerHandler) TriggerTask(c *gin.Context) {
	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := uuid.Parse(taskIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid task ID", err))
		return
	}

	// 触发任务
	err = h.scheduler.TriggerTask(taskID)
	if err != nil {
		switch err.Error() {
		case "failed to get task: record not found":
			c.JSON(http.StatusNotFound, errors.NewNotFoundError("Task not found"))
			return
		case "task is not active":
			c.JSON(http.StatusBadRequest, errors.NewValidationError("Task is not active", nil))
			return
		case "task is already running":
			c.JSON(http.StatusConflict, errors.NewConflictError("Task is already running", nil))
			return
		default:
			c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to trigger task", err))
			return
		}
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Task triggered successfully",
	})
}

// GetScheduledTasks 获取已调度的任务
// @Summary 获取已调度的任务
// @Description 获取当前在调度器中的任务列表
// @Tags scheduler
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=[]uuid.UUID}
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/scheduler/tasks [get]
func (h *SchedulerHandler) GetScheduledTasks(c *gin.Context) {
	tasks := h.scheduler.GetScheduledTasks()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    tasks,
	})
}

// GetRunningTasks 获取正在运行的任务
// @Summary 获取正在运行的任务
// @Description 获取当前正在运行的监控任务列表
// @Tags scheduler
// @Accept json
// @Produce json
// @Success 200 {object} Response{data=[]models.MonitorTask}
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/scheduler/running [get]
func (h *SchedulerHandler) GetRunningTasks(c *gin.Context) {
	tasks, err := h.scheduler.GetRunningTasks()
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to get running tasks", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    tasks,
	})
}

// GetTaskStatus 获取任务状态
// @Summary 获取任务状态
// @Description 获取指定任务的当前状态
// @Tags scheduler
// @Accept json
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} Response{data=map[string]string}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/scheduler/tasks/{id}/status [get]
func (h *SchedulerHandler) GetTaskStatus(c *gin.Context) {
	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := uuid.Parse(taskIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid task ID", err))
		return
	}

	// 获取任务状态
	status, err := h.scheduler.GetTaskStatus(taskID)
	if err != nil {
		if err.Error() == "failed to get task: record not found" {
			c.JSON(http.StatusNotFound, errors.NewNotFoundError("Task not found"))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to get task status", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data: map[string]string{
			"task_id": taskID.String(),
			"status":  status,
		},
	})
}

// StartScheduler 启动调度器
// @Summary 启动调度器
// @Description 启动任务调度器（管理员功能）
// @Tags scheduler
// @Accept json
// @Produce json
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/scheduler/start [post]
func (h *SchedulerHandler) StartScheduler(c *gin.Context) {
	// 检查用户权限（这里应该检查是否为管理员）
	// TODO: 实现权限检查

	err := h.scheduler.Start()
	if err != nil {
		if err.Error() == "scheduler is already running" {
			c.JSON(http.StatusBadRequest, errors.NewValidationError("Scheduler is already running", nil))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to start scheduler", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Scheduler started successfully",
	})
}

// StopScheduler 停止调度器
// @Summary 停止调度器
// @Description 停止任务调度器（管理员功能）
// @Tags scheduler
// @Accept json
// @Produce json
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 403 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/scheduler/stop [post]
func (h *SchedulerHandler) StopScheduler(c *gin.Context) {
	// 检查用户权限（这里应该检查是否为管理员）
	// TODO: 实现权限检查

	err := h.scheduler.Stop()
	if err != nil {
		if err.Error() == "scheduler is not running" {
			c.JSON(http.StatusBadRequest, errors.NewValidationError("Scheduler is not running", nil))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to stop scheduler", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Scheduler stopped successfully",
	})
}
