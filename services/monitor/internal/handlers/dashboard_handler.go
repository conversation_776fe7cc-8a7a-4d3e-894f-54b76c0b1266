package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/models"
)

// DashboardHandler 仪表板处理器
type DashboardHandler struct {
	db *gorm.DB
}

// NewDashboardHandler 创建仪表板处理器
func NewDashboardHandler(db *gorm.DB) *DashboardHandler {
	return &DashboardHandler{db: db}
}

// OverallStats 总体统计响应
type OverallStats struct {
	TotalTasks      int64 `json:"total_tasks"`
	ActiveTasks     int64 `json:"active_tasks"`
	TotalResults    int64 `json:"total_results"`
	HighRiskResults int64 `json:"high_risk_results"`
	TotalAccounts   int64 `json:"total_accounts"`
	ActiveAccounts  int64 `json:"active_accounts"`
	TodayTasks      int64 `json:"today_tasks"`
	TodayResults    int64 `json:"today_results"`
}

// TrendData 趋势数据
type TrendData struct {
	Date    string `json:"date"`
	Tasks   int64  `json:"tasks"`
	Results int64  `json:"results"`
	Threats int64  `json:"threats"`
}

// RiskDistribution 风险分布
type RiskDistribution struct {
	Name  string `json:"name"`
	Value int64  `json:"value"`
	Color string `json:"color"`
}

// PlatformStats 平台统计
type PlatformStats struct {
	Platform string `json:"platform"`
	Tasks    int64  `json:"tasks"`
	Results  int64  `json:"results"`
	Accounts int64  `json:"accounts"`
}

// RecentActivity 最近活动
type RecentActivity struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
}

// TaskStats 任务状态统计
type TaskStats struct {
	Status string `json:"status"`
	Count  int64  `json:"count"`
	Color  string `json:"color"`
}

// GetOverallStats 获取总体统计
func (h *DashboardHandler) GetOverallStats(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	var stats OverallStats

	// 总任务数 (监控任务 + 全量搜索任务)
	var monitorTasks, globalTasks int64
	h.db.Model(&models.MonitorTask{}).Where("user_id = ?", userID).Count(&monitorTasks)
	h.db.Model(&models.GlobalSearchTask{}).Where("user_id = ?", userID).Count(&globalTasks)
	stats.TotalTasks = monitorTasks + globalTasks

	// 活跃任务数
	h.db.Model(&models.MonitorTask{}).Where("user_id = ? AND status IN ?", userID, []string{"running", "pending"}).Count(&monitorTasks)
	h.db.Model(&models.GlobalSearchTask{}).Where("user_id = ? AND status IN ?", userID, []string{"running", "pending"}).Count(&globalTasks)
	stats.ActiveTasks = monitorTasks + globalTasks

	// 总结果数
	var scanResults, searchResults int64
	h.db.Model(&models.ScanResult{}).Where("user_id = ?", userID).Count(&scanResults)
	h.db.Model(&models.GlobalSearchResult{}).Where("user_id = ?", userID).Count(&searchResults)
	stats.TotalResults = scanResults + searchResults

	// 高风险结果数
	h.db.Model(&models.ScanResult{}).Where("user_id = ? AND severity IN ?", userID, []string{"critical", "high"}).Count(&scanResults)
	h.db.Model(&models.GlobalSearchResult{}).Where("user_id = ? AND severity IN ?", userID, []string{"critical", "high"}).Count(&searchResults)
	stats.HighRiskResults = scanResults + searchResults

	// 总账号数
	h.db.Model(&models.PlatformAccount{}).Where("user_id = ?", userID).Count(&stats.TotalAccounts)

	// 活跃账号数
	h.db.Model(&models.PlatformAccount{}).Where("user_id = ? AND status = ?", userID, "active").Count(&stats.ActiveAccounts)

	// 今日任务数
	today := time.Now().Truncate(24 * time.Hour)
	h.db.Model(&models.MonitorTask{}).Where("user_id = ? AND created_at >= ?", userID, today).Count(&monitorTasks)
	h.db.Model(&models.GlobalSearchTask{}).Where("user_id = ? AND created_at >= ?", userID, today).Count(&globalTasks)
	stats.TodayTasks = monitorTasks + globalTasks

	// 今日结果数
	h.db.Model(&models.ScanResult{}).Where("user_id = ? AND created_at >= ?", userID, today).Count(&scanResults)
	h.db.Model(&models.GlobalSearchResult{}).Where("user_id = ? AND created_at >= ?", userID, today).Count(&searchResults)
	stats.TodayResults = scanResults + searchResults

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetTrendData 获取趋势数据
func (h *DashboardHandler) GetTrendData(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	daysStr := c.DefaultQuery("days", "7")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 7
	}

	var trends []TrendData
	for i := days - 1; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Truncate(24 * time.Hour)
		nextDate := date.Add(24 * time.Hour)

		var monitorTasks, globalTasks, scanResults, searchResults, scanThreats, searchThreats int64

		// 任务统计
		h.db.Model(&models.MonitorTask{}).Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, date, nextDate).Count(&monitorTasks)
		h.db.Model(&models.GlobalSearchTask{}).Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, date, nextDate).Count(&globalTasks)

		// 结果统计
		h.db.Model(&models.ScanResult{}).Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, date, nextDate).Count(&scanResults)
		h.db.Model(&models.GlobalSearchResult{}).Where("user_id = ? AND created_at >= ? AND created_at < ?", userID, date, nextDate).Count(&searchResults)

		// 威胁统计
		h.db.Model(&models.ScanResult{}).Where("user_id = ? AND severity IN ? AND created_at >= ? AND created_at < ?", userID, []string{"critical", "high"}, date, nextDate).Count(&scanThreats)
		h.db.Model(&models.GlobalSearchResult{}).Where("user_id = ? AND severity IN ? AND created_at >= ? AND created_at < ?", userID, []string{"critical", "high"}, date, nextDate).Count(&searchThreats)

		trends = append(trends, TrendData{
			Date:    date.Format("01/02"),
			Tasks:   monitorTasks + globalTasks,
			Results: scanResults + searchResults,
			Threats: scanThreats + searchThreats,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": trends})
}

// GetRiskDistribution 获取风险分布
func (h *DashboardHandler) GetRiskDistribution(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	var distribution []RiskDistribution

	severities := []struct {
		name  string
		color string
	}{
		{"critical", "#dc2626"},
		{"high", "#ea580c"},
		{"medium", "#d97706"},
		{"low", "#16a34a"},
		{"info", "#2563eb"},
	}

	for _, severity := range severities {
		var scanCount, searchCount int64
		h.db.Model(&models.ScanResult{}).Where("user_id = ? AND severity = ?", userID, severity.name).Count(&scanCount)
		h.db.Model(&models.GlobalSearchResult{}).Where("user_id = ? AND severity = ?", userID, severity.name).Count(&searchCount)

		count := scanCount + searchCount

		var name string
		switch severity.name {
		case "critical":
			name = "严重"
		case "high":
			name = "高风险"
		case "medium":
			name = "中风险"
		case "low":
			name = "低风险"
		case "info":
			name = "信息"
		}

		if count > 0 {
			distribution = append(distribution, RiskDistribution{
				Name:  name,
				Value: count,
				Color: severity.color,
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{"data": distribution})
}

// GetPlatformStats 获取平台统计
func (h *DashboardHandler) GetPlatformStats(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	var stats []PlatformStats

	platforms := []string{"github", "gitlab", "gitee"}
	for _, platform := range platforms {
		var monitorTasks, globalTasks, scanResults, searchResults, accountCount int64

		// 任务统计 - 监控任务和全量搜索任务
		h.db.Model(&models.MonitorTask{}).Where("user_id = ? AND platforms LIKE ?", userID, "%"+platform+"%").Count(&monitorTasks)
		h.db.Model(&models.GlobalSearchTask{}).Where("user_id = ? AND platforms LIKE ?", userID, "%"+platform+"%").Count(&globalTasks)

		// 结果统计
		h.db.Model(&models.ScanResult{}).Where("user_id = ? AND platform = ?", userID, platform).Count(&scanResults)
		h.db.Model(&models.GlobalSearchResult{}).Where("user_id = ? AND platform = ?", userID, platform).Count(&searchResults)

		// 账号统计
		h.db.Model(&models.PlatformAccount{}).Where("user_id = ? AND platform = ?", userID, platform).Count(&accountCount)

		stats = append(stats, PlatformStats{
			Platform: platform,
			Tasks:    monitorTasks + globalTasks,
			Results:  scanResults + searchResults,
			Accounts: accountCount,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}

// GetRecentActivity 获取最近活动
func (h *DashboardHandler) GetRecentActivity(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	var activities []RecentActivity

	// 获取最近的监控任务
	var monitorTasks []models.MonitorTask
	h.db.Where("user_id = ?", userID).Order("created_at DESC").Limit(limit/4).Find(&monitorTasks)

	for _, task := range monitorTasks {
		activities = append(activities, RecentActivity{
			ID:          task.ID.String(),
			Type:        "monitor_task",
			Title:       "监控任务",
			Description: task.Name + " - " + task.Description,
			Status:      task.Status,
			CreatedAt:   task.CreatedAt,
		})
	}

	// 获取最近的全量搜索任务
	var globalTasks []models.GlobalSearchTask
	h.db.Where("user_id = ?", userID).Order("created_at DESC").Limit(limit/4).Find(&globalTasks)

	for _, task := range globalTasks {
		keywordsStr := "未设置"
		if task.Keywords != nil {
			if keywords, ok := task.Keywords["list"]; ok {
				if keywordList, ok := keywords.([]interface{}); ok && len(keywordList) > 0 {
					keywordsStr = fmt.Sprintf("%v", keywordList[0])
				}
			}
		}
		activities = append(activities, RecentActivity{
			ID:          task.ID.String(),
			Type:        "global_task",
			Title:       "全量搜索",
			Description: task.Name + " - 关键词: " + keywordsStr,
			Status:      task.Status,
			CreatedAt:   task.CreatedAt,
		})
	}

	// 获取最近的扫描结果
	var scanResults []models.ScanResult
	h.db.Where("user_id = ?", userID).Order("created_at DESC").Limit(limit/4).Find(&scanResults)

	for _, result := range scanResults {
		activities = append(activities, RecentActivity{
			ID:          result.ID.String(),
			Type:        "scan_result",
			Title:       "发现威胁",
			Description: result.RepoFullName + " - " + result.FilePath,
			Status:      result.RiskLevel,
			CreatedAt:   result.CreatedAt,
		})
	}

	// 获取最近的搜索结果
	var searchResults []models.GlobalSearchResult
	h.db.Where("user_id = ?", userID).Order("created_at DESC").Limit(limit/4).Find(&searchResults)

	for _, result := range searchResults {
		activities = append(activities, RecentActivity{
			ID:          result.ID.String(),
			Type:        "search_result",
			Title:       "发现威胁",
			Description: result.RepoFullName + " - " + result.FilePath,
			Status:      result.RiskLevel,
			CreatedAt:   result.CreatedAt,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": activities})
}

// GetTaskStats 获取任务状态统计
func (h *DashboardHandler) GetTaskStats(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	var stats []TaskStats

	statuses := []struct {
		name  string
		color string
	}{
		{"pending", "#6b7280"},
		{"running", "#3b82f6"},
		{"completed", "#10b981"},
		{"failed", "#ef4444"},
	}

	for _, status := range statuses {
		var monitorCount, globalCount int64
		h.db.Model(&models.MonitorTask{}).Where("user_id = ? AND status = ?", userID, status.name).Count(&monitorCount)
		h.db.Model(&models.GlobalSearchTask{}).Where("user_id = ? AND status = ?", userID, status.name).Count(&globalCount)

		count := monitorCount + globalCount

		var name string
		switch status.name {
		case "pending":
			name = "等待中"
		case "running":
			name = "运行中"
		case "completed":
			name = "已完成"
		case "failed":
			name = "失败"
		}

		stats = append(stats, TaskStats{
			Status: name,
			Count:  count,
			Color:  status.color,
		})
	}

	c.JSON(http.StatusOK, gin.H{"data": stats})
}
