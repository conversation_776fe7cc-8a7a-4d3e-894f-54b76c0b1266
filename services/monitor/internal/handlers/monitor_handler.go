package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/godeye/monitor/internal/services"
	"github.com/godeye/monitor/pkg/errors"
	"github.com/godeye/monitor/pkg/validator"
)

// MonitorHandler 监控处理器
type MonitorHandler struct {
	monitorService *services.MonitorService
	validator      *validator.Validator
}

// NewMonitorHandler 创建监控处理器
func NewMonitorHandler(monitorService *services.MonitorService, validator *validator.Validator) *MonitorHandler {
	return &MonitorHandler{
		monitorService: monitorService,
		validator:      validator,
	}
}

// CreateTask 创建监控任务
// @Summary 创建监控任务
// @Description 创建新的GitHub代码泄露监控任务
// @Tags monitor
// @Accept json
// @Produce json
// @Param request body services.CreateTaskRequest true "创建任务请求"
// @Success 201 {object} Response{data=models.MonitorTask}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/tasks [post]
func (h *MonitorHandler) CreateTask(c *gin.Context) {
	var req services.CreateTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid request body", err))
		return
	}

	// 验证请求
	if err := h.validator.Validate(&req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}
	req.UserID = userID.(uuid.UUID)

	// 创建任务
	task, err := h.monitorService.CreateTask(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to create task", err))
		return
	}

	c.JSON(http.StatusCreated, Response{
		Success: true,
		Message: "Task created successfully",
		Data:    task,
	})
}

// GetTask 获取任务详情
// @Summary 获取任务详情
// @Description 根据ID获取监控任务详情
// @Tags monitor
// @Accept json
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} Response{data=models.MonitorTask}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/tasks/{id} [get]
func (h *MonitorHandler) GetTask(c *gin.Context) {
	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := uuid.Parse(taskIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid task ID", err))
		return
	}

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 获取任务
	task, err := h.monitorService.GetTask(c.Request.Context(), userID.(uuid.UUID), taskID)
	if err != nil {
		if err.Error() == "task not found" {
			c.JSON(http.StatusNotFound, errors.NewNotFoundError("Task not found"))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to get task", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    task,
	})
}

// UpdateTask 更新任务
// @Summary 更新任务
// @Description 更新监控任务信息
// @Tags monitor
// @Accept json
// @Produce json
// @Param id path string true "任务ID"
// @Param request body services.UpdateTaskRequest true "更新任务请求"
// @Success 200 {object} Response{data=models.MonitorTask}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/tasks/{id} [put]
func (h *MonitorHandler) UpdateTask(c *gin.Context) {
	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := uuid.Parse(taskIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid task ID", err))
		return
	}

	var req services.UpdateTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid request body", err))
		return
	}

	// 验证请求
	if err := h.validator.Validate(&req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 更新任务
	task, err := h.monitorService.UpdateTask(c.Request.Context(), userID.(uuid.UUID), taskID, &req)
	if err != nil {
		if err.Error() == "task not found" {
			c.JSON(http.StatusNotFound, errors.NewNotFoundError("Task not found"))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to update task", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Task updated successfully",
		Data:    task,
	})
}

// DeleteTask 删除任务
// @Summary 删除任务
// @Description 删除监控任务
// @Tags monitor
// @Accept json
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/tasks/{id} [delete]
func (h *MonitorHandler) DeleteTask(c *gin.Context) {
	// 解析任务ID
	taskIDStr := c.Param("id")
	taskID, err := uuid.Parse(taskIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid task ID", err))
		return
	}

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 删除任务
	err = h.monitorService.DeleteTask(c.Request.Context(), userID.(uuid.UUID), taskID)
	if err != nil {
		if err.Error() == "task not found" {
			c.JSON(http.StatusNotFound, errors.NewNotFoundError("Task not found"))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to delete task", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Task deleted successfully",
	})
}

// ListTasks 列出任务
// @Summary 列出任务
// @Description 获取用户的监控任务列表
// @Tags monitor
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param status query string false "任务状态"
// @Param search query string false "搜索关键词"
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Success 200 {object} Response{data=ListTasksResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/tasks [get]
func (h *MonitorHandler) ListTasks(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")
	search := c.Query("search")
	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortDesc, _ := strconv.ParseBool(c.DefaultQuery("sort_desc", "true"))

	// 验证参数
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	req := &services.ListTasksRequest{
		UserID:   userID.(uuid.UUID),
		Page:     page,
		Limit:    limit,
		Status:   status,
		Search:   search,
		SortBy:   sortBy,
		SortDesc: sortDesc,
	}

	// 获取任务列表
	tasks, total, err := h.monitorService.ListTasks(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to list tasks", err))
		return
	}

	// 计算分页信息
	totalPages := (total + int64(limit) - 1) / int64(limit)

	response := ListTasksResponse{
		Tasks: tasks,
		Pagination: PaginationResponse{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    response,
	})
}

// Response 通用响应结构
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   interface{} `json:"error,omitempty"`
}

// ListTasksResponse 任务列表响应
type ListTasksResponse struct {
	Tasks      interface{}        `json:"tasks"`
	Pagination PaginationResponse `json:"pagination"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int64 `json:"total_pages"`
}
