package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/godeye/monitor/internal/services"
)

// AccountHandler 账号管理处理器
type AccountHandler struct {
	service *services.AccountService
}

// NewAccountHandler 创建账号管理处理器
func NewAccountHandler(service *services.AccountService) *AccountHandler {
	return &AccountHandler{
		service: service,
	}
}

// ListAccounts 列出平台账号
func (h *AccountHandler) ListAccounts(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	req := &services.ListAccountsRequest{
		UserID: userID,
	}

	// 解析查询参数
	if platform := c.Query("platform"); platform != "" {
		req.Platform = platform
	}
	if accountType := c.Query("account_type"); accountType != "" {
		req.AccountType = accountType
	}
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			req.IsActive = &isActive
		}
	}
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			req.Limit = limit
		}
	}
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil {
			req.Offset = offset
		}
	}

	accounts, total, err := h.service.ListAccounts(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 隐藏敏感信息
	for _, account := range accounts {
		account.Token = maskToken(account.Token)
		if account.RefreshToken != "" {
			account.RefreshToken = maskToken(account.RefreshToken)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"accounts": accounts,
		"total":    total,
		"limit":    req.Limit,
		"offset":   req.Offset,
	})
}

// CreateAccount 创建平台账号
func (h *AccountHandler) CreateAccount(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	var req services.CreateAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	req.UserID = userID

	account, err := h.service.CreateAccount(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 隐藏敏感信息
	account.Token = maskToken(account.Token)
	if account.RefreshToken != "" {
		account.RefreshToken = maskToken(account.RefreshToken)
	}

	c.JSON(http.StatusCreated, gin.H{"account": account})
}

// GetAccount 获取平台账号
func (h *AccountHandler) GetAccount(c *gin.Context) {
	accountIDStr := c.Param("id")
	accountID, err := uuid.Parse(accountIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid account ID"})
		return
	}

	account, err := h.service.GetAccount(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "account not found"})
		return
	}

	// 检查权限
	userID, err := getUserIDFromContext(c)
	if err != nil || account.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "access denied"})
		return
	}

	// 隐藏敏感信息
	account.Token = maskToken(account.Token)
	if account.RefreshToken != "" {
		account.RefreshToken = maskToken(account.RefreshToken)
	}

	c.JSON(http.StatusOK, gin.H{"account": account})
}

// UpdateAccount 更新平台账号
func (h *AccountHandler) UpdateAccount(c *gin.Context) {
	accountIDStr := c.Param("id")
	accountID, err := uuid.Parse(accountIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid account ID"})
		return
	}

	// 检查账号存在性和权限
	existingAccount, err := h.service.GetAccount(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "account not found"})
		return
	}

	userID, err := getUserIDFromContext(c)
	if err != nil || existingAccount.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "access denied"})
		return
	}

	var req services.UpdateAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	account, err := h.service.UpdateAccount(c.Request.Context(), accountID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 隐藏敏感信息
	account.Token = maskToken(account.Token)
	if account.RefreshToken != "" {
		account.RefreshToken = maskToken(account.RefreshToken)
	}

	c.JSON(http.StatusOK, gin.H{"account": account})
}

// DeleteAccount 删除平台账号
func (h *AccountHandler) DeleteAccount(c *gin.Context) {
	accountIDStr := c.Param("id")
	accountID, err := uuid.Parse(accountIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid account ID"})
		return
	}

	// 检查账号存在性和权限
	existingAccount, err := h.service.GetAccount(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "account not found"})
		return
	}

	userID, err := getUserIDFromContext(c)
	if err != nil || existingAccount.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "access denied"})
		return
	}

	if err := h.service.DeleteAccount(c.Request.Context(), accountID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "account deleted successfully"})
}

// ValidateAccount 验证账号有效性
func (h *AccountHandler) ValidateAccount(c *gin.Context) {
	accountIDStr := c.Param("id")
	accountID, err := uuid.Parse(accountIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid account ID"})
		return
	}

	// 检查账号存在性和权限
	existingAccount, err := h.service.GetAccount(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "account not found"})
		return
	}

	userID, err := getUserIDFromContext(c)
	if err != nil || existingAccount.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "access denied"})
		return
	}

	result, err := h.service.ValidateAccount(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetRateLimit 获取账号速率限制信息
func (h *AccountHandler) GetRateLimit(c *gin.Context) {
	accountIDStr := c.Param("id")
	accountID, err := uuid.Parse(accountIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid account ID"})
		return
	}

	// 检查账号存在性和权限
	existingAccount, err := h.service.GetAccount(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "account not found"})
		return
	}

	userID, err := getUserIDFromContext(c)
	if err != nil || existingAccount.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{"error": "access denied"})
		return
	}

	rateLimitInfo, err := h.service.GetRateLimit(c.Request.Context(), accountID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, rateLimitInfo)
}

// GetAccountStats 获取账号统计信息
func (h *AccountHandler) GetAccountStats(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	stats, err := h.service.GetAccountStats(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetPlatformUsageStats 获取平台使用统计
func (h *AccountHandler) GetPlatformUsageStats(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	days := 30 // 默认30天
	if daysStr := c.Query("days"); daysStr != "" {
		if d, err := strconv.Atoi(daysStr); err == nil && d > 0 && d <= 365 {
			days = d
		}
	}

	stats, err := h.service.GetPlatformUsageStats(c.Request.Context(), userID, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// 辅助函数

func maskToken(token string) string {
	if len(token) <= 8 {
		return "****"
	}
	return token[:4] + "****" + token[len(token)-4:]
}

func getUserIDFromContext(c *gin.Context) (uuid.UUID, error) {
	// 从JWT认证中间件设置的上下文中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, fmt.Errorf("user ID not found")
	}

	id, ok := userID.(uuid.UUID)
	if !ok {
		return uuid.Nil, fmt.Errorf("invalid user ID format")
	}

	return id, nil
}
