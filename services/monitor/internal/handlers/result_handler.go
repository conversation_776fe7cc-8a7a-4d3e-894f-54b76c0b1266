package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/godeye/monitor/internal/services"
	"github.com/godeye/monitor/pkg/errors"
	"github.com/godeye/monitor/pkg/validator"
)

// ResultHandler 结果处理器
type ResultHandler struct {
	resultService *services.ResultService
	validator     *validator.Validator
}

// NewResultHandler 创建结果处理器
func NewResultHandler(resultService *services.ResultService, validator *validator.Validator) *ResultHandler {
	return &ResultHandler{
		resultService: resultService,
		validator:     validator,
	}
}

// ListResults 列出扫描结果
// @Summary 列出扫描结果
// @Description 获取用户的扫描结果列表
// @Tags results
// @Accept json
// @Produce json
// @Param task_id query string false "任务ID"
// @Param run_id query string false "运行ID"
// @Param status query string false "结果状态"
// @Param risk_level query string false "风险级别"
// @Param match_type query string false "匹配类型"
// @Param repo_owner query string false "仓库所有者"
// @Param repo_name query string false "仓库名称"
// @Param search query string false "搜索关键词"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param sort_by query string false "排序字段" default(created_at)
// @Param sort_desc query bool false "是否降序" default(true)
// @Param date_from query string false "开始日期"
// @Param date_to query string false "结束日期"
// @Success 200 {object} Response{data=ListResultsResponse}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/results [get]
func (h *ResultHandler) ListResults(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 解析查询参数
	req := &services.ListResultsRequest{
		UserID:   userID.(uuid.UUID),
		Page:     1,
		Limit:    20,
		SortBy:   "created_at",
		SortDesc: true,
	}

	// 解析可选参数
	if taskIDStr := c.Query("task_id"); taskIDStr != "" {
		if taskID, err := uuid.Parse(taskIDStr); err == nil {
			req.TaskID = &taskID
		}
	}

	if runIDStr := c.Query("run_id"); runIDStr != "" {
		if runID, err := uuid.Parse(runIDStr); err == nil {
			req.RunID = &runID
		}
	}

	req.Status = c.Query("status")
	req.RiskLevel = c.Query("risk_level")
	req.MatchType = c.Query("match_type")
	req.RepoOwner = c.Query("repo_owner")
	req.RepoName = c.Query("repo_name")
	req.Search = c.Query("search")

	if page, err := strconv.Atoi(c.DefaultQuery("page", "1")); err == nil && page > 0 {
		req.Page = page
	}

	if limit, err := strconv.Atoi(c.DefaultQuery("limit", "20")); err == nil && limit > 0 && limit <= 100 {
		req.Limit = limit
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		req.SortBy = sortBy
	}

	if sortDesc, err := strconv.ParseBool(c.DefaultQuery("sort_desc", "true")); err == nil {
		req.SortDesc = sortDesc
	}

	// 解析日期参数
	if dateFromStr := c.Query("date_from"); dateFromStr != "" {
		if dateFrom, err := time.Parse("2006-01-02", dateFromStr); err == nil {
			req.DateFrom = &dateFrom
		}
	}

	if dateToStr := c.Query("date_to"); dateToStr != "" {
		if dateTo, err := time.Parse("2006-01-02", dateToStr); err == nil {
			req.DateTo = &dateTo
		}
	}

	// 验证请求
	if err := h.validator.Validate(req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	// 获取结果列表
	results, total, err := h.resultService.ListResults(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to list results", err))
		return
	}

	// 计算分页信息
	totalPages := (total + int64(req.Limit) - 1) / int64(req.Limit)

	response := ListResultsResponse{
		Results: results,
		Pagination: PaginationResponse{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    response,
	})
}

// GetResult 获取结果详情
// @Summary 获取结果详情
// @Description 根据ID获取扫描结果详情
// @Tags results
// @Accept json
// @Produce json
// @Param id path string true "结果ID"
// @Success 200 {object} Response{data=models.ScanResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/results/{id} [get]
func (h *ResultHandler) GetResult(c *gin.Context) {
	// 解析结果ID
	resultIDStr := c.Param("id")
	resultID, err := uuid.Parse(resultIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid result ID", err))
		return
	}

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 获取结果
	result, err := h.resultService.GetResult(c.Request.Context(), userID.(uuid.UUID), resultID)
	if err != nil {
		if err.Error() == "result not found" {
			c.JSON(http.StatusNotFound, errors.NewNotFoundError("Result not found"))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to get result", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    result,
	})
}

// UpdateResult 更新结果
// @Summary 更新结果
// @Description 更新扫描结果状态和备注
// @Tags results
// @Accept json
// @Produce json
// @Param id path string true "结果ID"
// @Param request body services.UpdateResultRequest true "更新结果请求"
// @Success 200 {object} Response{data=models.ScanResult}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/results/{id} [put]
func (h *ResultHandler) UpdateResult(c *gin.Context) {
	// 解析结果ID
	resultIDStr := c.Param("id")
	resultID, err := uuid.Parse(resultIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid result ID", err))
		return
	}

	var req services.UpdateResultRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid request body", err))
		return
	}

	// 验证请求
	if err := h.validator.Validate(&req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 更新结果
	result, err := h.resultService.UpdateResult(c.Request.Context(), userID.(uuid.UUID), resultID, &req)
	if err != nil {
		if err.Error() == "result not found" {
			c.JSON(http.StatusNotFound, errors.NewNotFoundError("Result not found"))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to update result", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Result updated successfully",
		Data:    result,
	})
}

// BatchUpdateResults 批量更新结果
// @Summary 批量更新结果
// @Description 批量更新多个扫描结果的状态和备注
// @Tags results
// @Accept json
// @Produce json
// @Param request body BatchUpdateResultsRequest true "批量更新结果请求"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/results/batch [put]
func (h *ResultHandler) BatchUpdateResults(c *gin.Context) {
	var req BatchUpdateResultsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid request body", err))
		return
	}

	// 验证请求
	if err := h.validator.Validate(&req); err != nil {
		c.JSON(http.StatusBadRequest, err)
		return
	}

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 批量更新结果
	err := h.resultService.BatchUpdateResults(c.Request.Context(), userID.(uuid.UUID), req.ResultIDs, &req.UpdateResultRequest)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to batch update results", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Results updated successfully",
	})
}

// DeleteResult 删除结果
// @Summary 删除结果
// @Description 删除扫描结果
// @Tags results
// @Accept json
// @Produce json
// @Param id path string true "结果ID"
// @Success 200 {object} Response
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 404 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/results/{id} [delete]
func (h *ResultHandler) DeleteResult(c *gin.Context) {
	// 解析结果ID
	resultIDStr := c.Param("id")
	resultID, err := uuid.Parse(resultIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, errors.NewValidationError("Invalid result ID", err))
		return
	}

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 删除结果
	err = h.resultService.DeleteResult(c.Request.Context(), userID.(uuid.UUID), resultID)
	if err != nil {
		if err.Error() == "result not found" {
			c.JSON(http.StatusNotFound, errors.NewNotFoundError("Result not found"))
			return
		}
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to delete result", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Result deleted successfully",
	})
}

// GetResultStats 获取结果统计
// @Summary 获取结果统计
// @Description 获取扫描结果的统计信息
// @Tags results
// @Accept json
// @Produce json
// @Param task_id query string false "任务ID"
// @Param days query int false "统计天数" default(30)
// @Success 200 {object} Response{data=services.ResultStats}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/results/stats [get]
func (h *ResultHandler) GetResultStats(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 解析参数
	var taskID *uuid.UUID
	if taskIDStr := c.Query("task_id"); taskIDStr != "" {
		if id, err := uuid.Parse(taskIDStr); err == nil {
			taskID = &id
		}
	}

	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
	if days <= 0 {
		days = 30
	}

	// 获取统计信息
	stats, err := h.resultService.GetResultStats(c.Request.Context(), userID.(uuid.UUID), taskID, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to get result stats", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    stats,
	})
}

// FindDuplicates 查找重复结果
// @Summary 查找重复结果
// @Description 查找重复的扫描结果
// @Tags results
// @Accept json
// @Produce json
// @Param task_id query string false "任务ID"
// @Success 200 {object} Response{data=[]services.DuplicateGroup}
// @Failure 400 {object} Response
// @Failure 401 {object} Response
// @Failure 500 {object} Response
// @Router /api/v1/monitor/results/duplicates [get]
func (h *ResultHandler) FindDuplicates(c *gin.Context) {
	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, errors.NewUnauthorizedError("User not authenticated"))
		return
	}

	// 解析参数
	var taskID *uuid.UUID
	if taskIDStr := c.Query("task_id"); taskIDStr != "" {
		if id, err := uuid.Parse(taskIDStr); err == nil {
			taskID = &id
		}
	}

	// 查找重复结果
	duplicates, err := h.resultService.FindDuplicates(c.Request.Context(), userID.(uuid.UUID), taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, errors.NewInternalError("Failed to find duplicates", err))
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Data:    duplicates,
	})
}

// BatchUpdateResultsRequest 批量更新结果请求
type BatchUpdateResultsRequest struct {
	services.UpdateResultRequest
	ResultIDs []uuid.UUID `json:"result_ids" validate:"required,min=1"`
}

// ListResultsResponse 结果列表响应
type ListResultsResponse struct {
	Results    interface{}        `json:"results"`
	Pagination PaginationResponse `json:"pagination"`
}
