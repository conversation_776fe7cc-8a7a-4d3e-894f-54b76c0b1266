package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type SystemHandler struct{}

func NewSystemHandler() *SystemHandler {
	return &SystemHandler{}
}

// GetPlatforms 获取支持的平台列表
func (h *SystemHandler) GetPlatforms(c *gin.Context) {
	platforms := []string{"github", "gitlab", "gitee"}
	
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"platforms": platforms,
		},
	})
}

// GetSystemInfo 获取系统信息
func (h *SystemHandler) GetSystemInfo(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"service":     "monitor",
			"version":     "1.0.0",
			"platforms":   []string{"github", "gitlab", "gitee"},
			"features":    []string{"global_search", "repository_monitor", "account_management"},
		},
	})
}

// GetHealth 健康检查
func (h *SystemHandler) GetHealth(c *gin.Context) {
	c.<PERSON>(http.StatusOK, gin.H{
		"service":   "monitor",
		"status":    "ok",
		"timestamp": c.GetInt64("timestamp"),
	})
}
