package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/godeye/monitor/internal/models"
	"github.com/godeye/monitor/internal/services"
)

// WhitelistHandler 白名单处理器
type WhitelistHandler struct {
	whitelistService *services.WhitelistService
}

// NewWhitelistHandler 创建白名单处理器
func NewWhitelistHandler(whitelistService *services.WhitelistService) *WhitelistHandler {
	return &WhitelistHandler{
		whitelistService: whitelistService,
	}
}

// CreateWhitelist 创建白名单条目
// @Summary 创建白名单条目
// @Description 将仓库、用户或组织添加到白名单
// @Tags whitelist
// @Accept json
// @Produce json
// @Param request body models.WhitelistCreateRequest true "创建白名单请求"
// @Success 201 {object} models.Whitelist
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/whitelist [post]
func (h *WhitelistHandler) CreateWhitelist(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	var req models.WhitelistCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	whitelist, err := h.whitelistService.CreateWhitelist(c.Request.Context(), userID, &req)
	if err != nil {
		if err.Error() == "whitelist entry already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, whitelist)
}

// GetWhitelist 获取白名单条目详情
// @Summary 获取白名单条目详情
// @Description 根据ID获取白名单条目详情
// @Tags whitelist
// @Produce json
// @Param id path string true "白名单ID"
// @Success 200 {object} models.Whitelist
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/whitelist/{id} [get]
func (h *WhitelistHandler) GetWhitelist(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	whitelistIDStr := c.Param("id")
	whitelistID, err := uuid.Parse(whitelistIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid whitelist ID"})
		return
	}

	whitelist, err := h.whitelistService.GetWhitelistByID(c.Request.Context(), userID, whitelistID)
	if err != nil {
		if err.Error() == "whitelist not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, whitelist)
}

// UpdateWhitelist 更新白名单条目
// @Summary 更新白名单条目
// @Description 更新白名单条目的信息
// @Tags whitelist
// @Accept json
// @Produce json
// @Param id path string true "白名单ID"
// @Param request body models.WhitelistUpdateRequest true "更新白名单请求"
// @Success 200 {object} models.Whitelist
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/whitelist/{id} [put]
func (h *WhitelistHandler) UpdateWhitelist(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	whitelistIDStr := c.Param("id")
	whitelistID, err := uuid.Parse(whitelistIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid whitelist ID"})
		return
	}

	var req models.WhitelistUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	whitelist, err := h.whitelistService.UpdateWhitelist(c.Request.Context(), userID, whitelistID, &req)
	if err != nil {
		if err.Error() == "whitelist not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, whitelist)
}

// DeleteWhitelist 删除白名单条目
// @Summary 删除白名单条目
// @Description 从白名单中删除条目
// @Tags whitelist
// @Produce json
// @Param id path string true "白名单ID"
// @Success 204
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/whitelist/{id} [delete]
func (h *WhitelistHandler) DeleteWhitelist(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	whitelistIDStr := c.Param("id")
	whitelistID, err := uuid.Parse(whitelistIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid whitelist ID"})
		return
	}

	err = h.whitelistService.DeleteWhitelist(c.Request.Context(), userID, whitelistID)
	if err != nil {
		if err.Error() == "whitelist not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListWhitelists 获取白名单列表
// @Summary 获取白名单列表
// @Description 获取用户的白名单列表，支持分页和过滤
// @Tags whitelist
// @Produce json
// @Param type query string false "类型过滤 (repository, user, organization)"
// @Param platform query string false "平台过滤 (github, gitlab, gitee)"
// @Param status query string false "状态过滤 (active, disabled)"
// @Param keyword query string false "关键词搜索"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Success 200 {object} models.WhitelistListResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/whitelist [get]
func (h *WhitelistHandler) ListWhitelists(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	var req models.WhitelistListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.whitelistService.ListWhitelists(c.Request.Context(), userID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// CheckWhitelist 检查是否在白名单中
// @Summary 检查是否在白名单中
// @Description 检查指定仓库是否在白名单中
// @Tags whitelist
// @Produce json
// @Param platform query string true "平台 (github, gitlab, gitee)"
// @Param repo query string true "仓库全名 (owner/repo)"
// @Success 200 {object} map[string]bool
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/whitelist/check [get]
func (h *WhitelistHandler) CheckWhitelist(c *gin.Context) {
	userID, err := getUserIDFromContext(c)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	platform := c.Query("platform")
	repo := c.Query("repo")

	if platform == "" || repo == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "platform and repo are required"})
		return
	}

	isWhitelisted, err := h.whitelistService.IsWhitelisted(c.Request.Context(), userID, platform, repo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"whitelisted": isWhitelisted})
}
