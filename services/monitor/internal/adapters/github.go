package adapters

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// GitHubAdapter GitHub平台适配器
type GitHubAdapter struct {
	baseURL    string
	httpClient *http.Client
}

// NewGitHubAdapter 创建GitHub适配器
func NewGitHubAdapter() *GitHubAdapter {
	return &GitHubAdapter{
		baseURL: "https://api.github.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetPlatformName 获取平台名称
func (g *GitHubAdapter) GetPlatformName() string {
	return "github"
}

// ValidateAccount 验证账号有效性
func (g *GitHubAdapter) ValidateAccount(ctx context.Context, account *Account) error {
	req, err := http.NewRequestWithContext(ctx, "GET", g.baseURL+"/user", nil)
	if err != nil {
		return err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("invalid account: status %d", resp.StatusCode)
	}
	
	return nil
}

// GetRateLimit 获取API限制信息
func (g *GitHubAdapter) GetRateLimit(ctx context.Context, account *Account) (*RateLimit, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", g.baseURL+"/rate_limit", nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	var rateLimitResp struct {
		Resources struct {
			Search struct {
				Limit     int `json:"limit"`
				Remaining int `json:"remaining"`
				Reset     int64 `json:"reset"`
				Used      int `json:"used"`
			} `json:"search"`
		} `json:"resources"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&rateLimitResp); err != nil {
		return nil, err
	}
	
	resetTime := time.Unix(rateLimitResp.Resources.Search.Reset, 0)
	
	return &RateLimit{
		Limit:     rateLimitResp.Resources.Search.Limit,
		Remaining: rateLimitResp.Resources.Search.Remaining,
		ResetAt:   &resetTime,
		Used:      rateLimitResp.Resources.Search.Used,
	}, nil
}

// SearchCode 搜索代码
func (g *GitHubAdapter) SearchCode(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	query := g.buildSearchQuery(req)
	
	params := url.Values{}
	params.Set("q", query)
	params.Set("sort", req.Sort)
	params.Set("order", req.Order)
	params.Set("per_page", strconv.Itoa(req.PerPage))
	params.Set("page", strconv.Itoa(req.Page))
	
	searchURL := fmt.Sprintf("%s/search/code?%s", g.baseURL, params.Encode())
	
	httpReq, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(httpReq, req.Account)
	
	resp, err := g.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("search failed: status %d", resp.StatusCode)
	}
	
	var searchResp struct {
		TotalCount        int  `json:"total_count"`
		IncompleteResults bool `json:"incomplete_results"`
		Items             []struct {
			Name       string `json:"name"`
			Path       string `json:"path"`
			SHA        string `json:"sha"`
			URL        string `json:"url"`
			GitURL     string `json:"git_url"`
			HTMLURL    string `json:"html_url"`
			Repository struct {
				ID          int64  `json:"id"`
				Name        string `json:"name"`
				FullName    string `json:"full_name"`
				Owner       struct {
					Login     string `json:"login"`
					ID        int64  `json:"id"`
					Type      string `json:"type"`
					HTMLURL   string `json:"html_url"`
					AvatarURL string `json:"avatar_url"`
				} `json:"owner"`
				Private     bool      `json:"private"`
				HTMLURL     string    `json:"html_url"`
				Description string    `json:"description"`
				Fork        bool      `json:"fork"`
				CreatedAt   time.Time `json:"created_at"`
				UpdatedAt   time.Time `json:"updated_at"`
				PushedAt    time.Time `json:"pushed_at"`
				CloneURL    string    `json:"clone_url"`
				Size        int64     `json:"size"`
				Language    string    `json:"language"`
				ForksCount  int       `json:"forks_count"`
				StargazersCount int   `json:"stargazers_count"`
				Archived    bool      `json:"archived"`
			} `json:"repository"`
			Score       float64 `json:"score"`
			TextMatches []struct {
				ObjectURL  string `json:"object_url"`
				ObjectType string `json:"object_type"`
				Property   string `json:"property"`
				Fragment   string `json:"fragment"`
				Matches    []struct {
					Text    string `json:"text"`
					Indices []int  `json:"indices"`
				} `json:"matches"`
			} `json:"text_matches"`
		} `json:"items"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, err
	}
	
	// 获取速率限制信息
	rateLimit := g.parseRateLimitHeaders(resp.Header)
	
	// 转换为统一格式
	items := make([]SearchItem, len(searchResp.Items))
	for i, item := range searchResp.Items {
		items[i] = SearchItem{
			Type:       "code",
			PlatformID: strconv.FormatInt(item.Repository.ID, 10),
			Score:      item.Score,
			Repository: &Repository{
				ID:          item.Repository.ID,
				Name:        item.Repository.Name,
				FullName:    item.Repository.FullName,
				Owner: &User{
					ID:        item.Repository.Owner.ID,
					Login:     item.Repository.Owner.Login,
					Type:      item.Repository.Owner.Type,
					HTMLURL:   item.Repository.Owner.HTMLURL,
					AvatarURL: item.Repository.Owner.AvatarURL,
				},
				Description: item.Repository.Description,
				HTMLURL:     item.Repository.HTMLURL,
				CloneURL:    item.Repository.CloneURL,
				Language:    item.Repository.Language,
				Size:        item.Repository.Size,
				StarCount:   item.Repository.StargazersCount,
				ForkCount:   item.Repository.ForksCount,
				IsPrivate:   item.Repository.Private,
				IsFork:      item.Repository.Fork,
				IsArchived:  item.Repository.Archived,
				CreatedAt:   item.Repository.CreatedAt,
				UpdatedAt:   item.Repository.UpdatedAt,
				PushedAt:    &item.Repository.PushedAt,
			},
			File: &File{
				Name:    item.Name,
				Path:    item.Path,
				SHA:     item.SHA,
				URL:     item.URL,
				HTMLURL: item.HTMLURL,
			},
			Matches: g.convertTextMatches(item.TextMatches),
		}
	}
	
	return &SearchResponse{
		Platform:          "github",
		TotalCount:        searchResp.TotalCount,
		IncompleteResults: searchResp.IncompleteResults,
		Items:             items,
		RateLimit:         rateLimit,
		HasMore:           len(items) == req.PerPage && req.Page*req.PerPage < searchResp.TotalCount,
		NextPage:          req.Page + 1,
	}, nil
}

// SearchRepositories 搜索仓库
func (g *GitHubAdapter) SearchRepositories(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	query := g.buildRepositorySearchQuery(req)
	
	params := url.Values{}
	params.Set("q", query)
	params.Set("sort", req.Sort)
	params.Set("order", req.Order)
	params.Set("per_page", strconv.Itoa(req.PerPage))
	params.Set("page", strconv.Itoa(req.Page))
	
	searchURL := fmt.Sprintf("%s/search/repositories?%s", g.baseURL, params.Encode())
	
	httpReq, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(httpReq, req.Account)
	
	resp, err := g.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("repository search failed: status %d", resp.StatusCode)
	}
	
	// TODO: 实现仓库搜索响应解析
	// 类似于代码搜索，但返回仓库信息
	
	return &SearchResponse{
		Platform:   "github",
		TotalCount: 0,
		Items:      []SearchItem{},
		RateLimit:  g.parseRateLimitHeaders(resp.Header),
	}, nil
}

// GetFileContent 获取文件内容
func (g *GitHubAdapter) GetFileContent(ctx context.Context, account *Account, fileURL string) (*FileContent, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", fileURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get file content failed: status %d", resp.StatusCode)
	}
	
	var fileResp struct {
		Name        string `json:"name"`
		Path        string `json:"path"`
		SHA         string `json:"sha"`
		Size        int64  `json:"size"`
		URL         string `json:"url"`
		HTMLURL     string `json:"html_url"`
		DownloadURL string `json:"download_url"`
		Type        string `json:"type"`
		Content     string `json:"content"`
		Encoding    string `json:"encoding"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&fileResp); err != nil {
		return nil, err
	}
	
	// 解码base64内容
	var content string
	if fileResp.Encoding == "base64" {
		decoded, err := base64.StdEncoding.DecodeString(fileResp.Content)
		if err != nil {
			return nil, err
		}
		content = string(decoded)
	} else {
		content = fileResp.Content
	}
	
	return &FileContent{
		Name:        fileResp.Name,
		Path:        fileResp.Path,
		SHA:         fileResp.SHA,
		Size:        fileResp.Size,
		URL:         fileResp.URL,
		HTMLURL:     fileResp.HTMLURL,
		DownloadURL: fileResp.DownloadURL,
		Type:        fileResp.Type,
		Content:     content,
		Encoding:    fileResp.Encoding,
	}, nil
}

// 辅助方法

func (g *GitHubAdapter) setAuthHeaders(req *http.Request, account *Account) {
	if account != nil && account.Token != "" {
		req.Header.Set("Authorization", "token "+account.Token)
	}
	req.Header.Set("Accept", "application/vnd.github.v3.text-match+json")
	req.Header.Set("User-Agent", "GodEye/1.0")
}

func (g *GitHubAdapter) buildSearchQuery(req *SearchRequest) string {
	var parts []string
	
	// 添加关键词
	if len(req.Keywords) > 0 {
		keywordQuery := strings.Join(req.Keywords, " OR ")
		if len(req.Keywords) > 1 {
			keywordQuery = "(" + keywordQuery + ")"
		}
		parts = append(parts, keywordQuery)
	}
	
	// 添加文件类型过滤
	for _, fileType := range req.FileTypes {
		parts = append(parts, "extension:"+fileType)
	}
	
	// 添加语言过滤
	if req.Language != "" {
		parts = append(parts, "language:"+req.Language)
	}
	
	// 添加大小过滤
	if req.Size != "" {
		parts = append(parts, "size:"+req.Size)
	}
	
	// 添加排除条件
	for _, exclude := range req.ExcludeUsers {
		parts = append(parts, "-user:"+exclude)
	}
	
	for _, exclude := range req.ExcludeRepos {
		parts = append(parts, "-repo:"+exclude)
	}
	
	for _, exclude := range req.ExcludeTerms {
		parts = append(parts, "-"+exclude)
	}
	
	return strings.Join(parts, " ")
}

func (g *GitHubAdapter) buildRepositorySearchQuery(req *SearchRequest) string {
	// 类似于buildSearchQuery，但针对仓库搜索
	return g.buildSearchQuery(req)
}

func (g *GitHubAdapter) parseRateLimitHeaders(headers http.Header) *RateLimit {
	limit, _ := strconv.Atoi(headers.Get("X-RateLimit-Limit"))
	remaining, _ := strconv.Atoi(headers.Get("X-RateLimit-Remaining"))
	reset, _ := strconv.ParseInt(headers.Get("X-RateLimit-Reset"), 10, 64)
	used, _ := strconv.Atoi(headers.Get("X-RateLimit-Used"))
	
	var resetTime *time.Time
	if reset > 0 {
		t := time.Unix(reset, 0)
		resetTime = &t
	}
	
	return &RateLimit{
		Limit:     limit,
		Remaining: remaining,
		ResetAt:   resetTime,
		Used:      used,
	}
}

func (g *GitHubAdapter) convertTextMatches(textMatches []struct {
	ObjectURL  string `json:"object_url"`
	ObjectType string `json:"object_type"`
	Property   string `json:"property"`
	Fragment   string `json:"fragment"`
	Matches    []struct {
		Text    string `json:"text"`
		Indices []int  `json:"indices"`
	} `json:"matches"`
}) []Match {
	matches := make([]Match, len(textMatches))
	for i, tm := range textMatches {
		matches[i] = Match{
			Text:     tm.Fragment,
			Fragment: tm.Fragment,
			Matches: []TextMatch{{
				ObjectURL:  tm.ObjectURL,
				ObjectType: tm.ObjectType,
				Property:   tm.Property,
				Fragment:   tm.Fragment,
				Matches:    tm.Matches,
			}},
		}
	}
	return matches
}
