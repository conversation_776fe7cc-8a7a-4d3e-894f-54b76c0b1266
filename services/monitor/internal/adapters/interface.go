package adapters

import (
	"context"
	"time"

	"github.com/google/uuid"
)

// PlatformAdapter 平台适配器接口
type PlatformAdapter interface {
	// GetPlatformName 获取平台名称
	GetPlatformName() string
	
	// ValidateAccount 验证账号有效性
	ValidateAccount(ctx context.Context, account *Account) error
	
	// GetRateLimit 获取API限制信息
	GetRateLimit(ctx context.Context, account *Account) (*RateLimit, error)
	
	// SearchCode 搜索代码
	SearchCode(ctx context.Context, req *SearchRequest) (*SearchResponse, error)
	
	// SearchRepositories 搜索仓库
	SearchRepositories(ctx context.Context, req *SearchRequest) (*SearchResponse, error)
	
	// GetFileContent 获取文件内容
	GetFileContent(ctx context.Context, account *Account, fileURL string) (*FileContent, error)
}

// Account 账号信息
type Account struct {
	ID           uuid.UUID
	Platform     string
	AccountType  string
	Username     string
	Token        string
	RefreshToken string
	ExpiresAt    *time.Time
}

// RateLimit API限制信息
type RateLimit struct {
	Limit     int        `json:"limit"`
	Remaining int        `json:"remaining"`
	ResetAt   *time.Time `json:"reset_at"`
	Used      int        `json:"used"`
}

// SearchRequest 搜索请求
type SearchRequest struct {
	// 基本参数
	Query       string   `json:"query"`
	Keywords    []string `json:"keywords"`
	SearchType  string   `json:"search_type"` // "code", "repository", "both"
	
	// 过滤参数
	FileTypes   []string `json:"file_types"`
	Language    string   `json:"language"`
	Size        string   `json:"size"` // ">1000", "<5000"
	
	// 排除参数
	ExcludeUsers []string `json:"exclude_users"`
	ExcludeRepos []string `json:"exclude_repos"`
	ExcludeTerms []string `json:"exclude_terms"`
	
	// 分页参数
	Page    int `json:"page"`
	PerPage int `json:"per_page"`
	
	// 排序参数
	Sort  string `json:"sort"`  // "indexed", "updated", "created"
	Order string `json:"order"` // "desc", "asc"
	
	// 账号信息
	Account *Account `json:"account"`
}

// SearchResponse 搜索响应
type SearchResponse struct {
	Platform     string         `json:"platform"`
	TotalCount   int            `json:"total_count"`
	IncompleteResults bool      `json:"incomplete_results"`
	Items        []SearchItem   `json:"items"`
	RateLimit    *RateLimit     `json:"rate_limit"`
	HasMore      bool           `json:"has_more"`
	NextPage     int            `json:"next_page"`
}

// SearchItem 搜索结果项
type SearchItem struct {
	// 基本信息
	Type        string `json:"type"` // "code", "repository"
	PlatformID  string `json:"platform_id"`
	Score       float64 `json:"score"`
	
	// 仓库信息
	Repository  *Repository `json:"repository"`
	
	// 文件信息 (仅代码搜索)
	File        *File `json:"file,omitempty"`
	
	// 匹配信息
	Matches     []Match `json:"matches"`
}

// Repository 仓库信息
type Repository struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	FullName    string    `json:"full_name"`
	Owner       *User     `json:"owner"`
	Description string    `json:"description"`
	HTMLURL     string    `json:"html_url"`
	CloneURL    string    `json:"clone_url"`
	Language    string    `json:"language"`
	Size        int64     `json:"size"`
	StarCount   int       `json:"stargazers_count"`
	ForkCount   int       `json:"forks_count"`
	IsPrivate   bool      `json:"private"`
	IsFork      bool      `json:"fork"`
	IsArchived  bool      `json:"archived"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	PushedAt    *time.Time `json:"pushed_at"`
}

// User 用户信息
type User struct {
	ID        int64  `json:"id"`
	Login     string `json:"login"`
	Type      string `json:"type"`
	HTMLURL   string `json:"html_url"`
	AvatarURL string `json:"avatar_url"`
}

// File 文件信息
type File struct {
	Name     string `json:"name"`
	Path     string `json:"path"`
	SHA      string `json:"sha"`
	URL      string `json:"url"`
	HTMLURL  string `json:"html_url"`
	Size     int64  `json:"size"`
}

// Match 匹配信息
type Match struct {
	Text        string      `json:"text"`
	Indices     []int       `json:"indices"`
	LineNumber  int         `json:"line_number"`
	Fragment    string      `json:"fragment"`
	Matches     []TextMatch `json:"matches"`
}

// TextMatch 文本匹配
type TextMatch struct {
	ObjectURL  string `json:"object_url"`
	ObjectType string `json:"object_type"`
	Property   string `json:"property"`
	Fragment   string `json:"fragment"`
	Matches    []struct {
		Text    string `json:"text"`
		Indices []int  `json:"indices"`
	} `json:"matches"`
}

// FileContent 文件内容
type FileContent struct {
	Name        string `json:"name"`
	Path        string `json:"path"`
	SHA         string `json:"sha"`
	Size        int64  `json:"size"`
	URL         string `json:"url"`
	HTMLURL     string `json:"html_url"`
	DownloadURL string `json:"download_url"`
	Type        string `json:"type"`
	Content     string `json:"content"`
	Encoding    string `json:"encoding"`
}

// SearchError 搜索错误
type SearchError struct {
	Platform string `json:"platform"`
	Code     string `json:"code"`
	Message  string `json:"message"`
	Details  map[string]interface{} `json:"details"`
}

func (e *SearchError) Error() string {
	return e.Message
}

// AdapterManager 适配器管理器
type AdapterManager struct {
	adapters map[string]PlatformAdapter
}

// NewAdapterManager 创建适配器管理器
func NewAdapterManager() *AdapterManager {
	return &AdapterManager{
		adapters: make(map[string]PlatformAdapter),
	}
}

// RegisterAdapter 注册适配器
func (am *AdapterManager) RegisterAdapter(platform string, adapter PlatformAdapter) {
	am.adapters[platform] = adapter
}

// GetAdapter 获取适配器
func (am *AdapterManager) GetAdapter(platform string) (PlatformAdapter, bool) {
	adapter, exists := am.adapters[platform]
	return adapter, exists
}

// GetSupportedPlatforms 获取支持的平台列表
func (am *AdapterManager) GetSupportedPlatforms() []string {
	platforms := make([]string, 0, len(am.adapters))
	for platform := range am.adapters {
		platforms = append(platforms, platform)
	}
	return platforms
}

// SearchAllPlatforms 在所有平台搜索
func (am *AdapterManager) SearchAllPlatforms(ctx context.Context, req *SearchRequest) map[string]*SearchResponse {
	results := make(map[string]*SearchResponse)
	
	for platform, adapter := range am.adapters {
		// 为每个平台创建独立的请求
		platformReq := *req
		platformReq.Account = nil // 需要从账号池获取对应平台的账号
		
		var resp *SearchResponse
		var err error
		
		switch req.SearchType {
		case "code":
			resp, err = adapter.SearchCode(ctx, &platformReq)
		case "repository":
			resp, err = adapter.SearchRepositories(ctx, &platformReq)
		case "both":
			// 先搜索代码，再搜索仓库
			resp, err = adapter.SearchCode(ctx, &platformReq)
			// TODO: 合并仓库搜索结果
		default:
			resp, err = adapter.SearchCode(ctx, &platformReq)
		}
		
		if err != nil {
			// 记录错误但继续其他平台的搜索
			resp = &SearchResponse{
				Platform:    platform,
				TotalCount:  0,
				Items:       []SearchItem{},
				RateLimit:   nil,
			}
		}
		
		results[platform] = resp
	}
	
	return results
}
