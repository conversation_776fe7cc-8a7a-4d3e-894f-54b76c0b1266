package adapters

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// GiteeAdapter Gitee平台适配器
type GiteeAdapter struct {
	baseURL    string
	httpClient *http.Client
}

// NewGiteeAdapter 创建Gitee适配器
func NewGiteeAdapter() *GiteeAdapter {
	return &GiteeAdapter{
		baseURL: "https://gitee.com/api/v5",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetPlatformName 获取平台名称
func (g *GiteeAdapter) GetPlatformName() string {
	return "gitee"
}

// ValidateAccount 验证账号有效性
func (g *GiteeAdapter) ValidateAccount(ctx context.Context, account *Account) error {
	req, err := http.NewRequestWithContext(ctx, "GET", g.baseURL+"/user", nil)
	if err != nil {
		return err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("invalid account: status %d", resp.StatusCode)
	}
	
	return nil
}

// GetRateLimit 获取API限制信息
func (g *GiteeAdapter) GetRateLimit(ctx context.Context, account *Account) (*RateLimit, error) {
	// Gitee API 限制信息通常在响应头中
	req, err := http.NewRequestWithContext(ctx, "GET", g.baseURL+"/user", nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	return g.parseRateLimitHeaders(resp.Header), nil
}

// SearchCode 搜索代码
func (g *GiteeAdapter) SearchCode(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	// Gitee 搜索API
	query := g.buildSearchQuery(req)
	
	params := url.Values{}
	params.Set("q", query)
	params.Set("per_page", strconv.Itoa(req.PerPage))
	params.Set("page", strconv.Itoa(req.Page))
	
	if req.Sort != "" {
		params.Set("sort", g.convertSortField(req.Sort))
		params.Set("order", g.convertSortOrder(req.Order))
	}
	
	// 添加语言过滤
	if req.Language != "" {
		params.Set("language", req.Language)
	}
	
	searchURL := fmt.Sprintf("%s/search/repositories?%s", g.baseURL, params.Encode())
	
	httpReq, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(httpReq, req.Account)
	
	resp, err := g.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("search failed: status %d", resp.StatusCode)
	}
	
	var searchResp struct {
		TotalCount int `json:"total_count"`
		Items      []struct {
			ID          int    `json:"id"`
			Name        string `json:"name"`
			FullName    string `json:"full_name"`
			Description string `json:"description"`
			Private     bool   `json:"private"`
			Fork        bool   `json:"fork"`
			HTMLURL     string `json:"html_url"`
			CloneURL    string `json:"clone_url"`
			Language    string `json:"language"`
			Size        int64  `json:"size"`
			StargazersCount int `json:"stargazers_count"`
			WatchersCount   int `json:"watchers_count"`
			ForksCount      int `json:"forks_count"`
			CreatedAt       string `json:"created_at"`
			UpdatedAt       string `json:"updated_at"`
			PushedAt        string `json:"pushed_at"`
			Owner           struct {
				ID        int    `json:"id"`
				Login     string `json:"login"`
				Name      string `json:"name"`
				Type      string `json:"type"`
				HTMLURL   string `json:"html_url"`
				AvatarURL string `json:"avatar_url"`
			} `json:"owner"`
		} `json:"items"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, err
	}
	
	// 获取速率限制信息
	rateLimit := g.parseRateLimitHeaders(resp.Header)
	
	// 转换为统一格式
	items := make([]SearchItem, len(searchResp.Items))
	for i, item := range searchResp.Items {
		createdAt, _ := time.Parse(time.RFC3339, item.CreatedAt)
		updatedAt, _ := time.Parse(time.RFC3339, item.UpdatedAt)
		pushedAt, _ := time.Parse(time.RFC3339, item.PushedAt)
		
		// 对于仓库搜索，我们需要进一步搜索代码
		// 这里先返回仓库信息，实际的代码搜索需要额外的API调用
		items[i] = SearchItem{
			Type:       "repository",
			PlatformID: strconv.Itoa(item.ID),
			Score:      1.0, // Gitee 不提供评分
			Repository: &Repository{
				ID:          int64(item.ID),
				Name:        item.Name,
				FullName:    item.FullName,
				Owner: &User{
					ID:        int64(item.Owner.ID),
					Login:     item.Owner.Login,
					Type:      item.Owner.Type,
					HTMLURL:   item.Owner.HTMLURL,
					AvatarURL: item.Owner.AvatarURL,
				},
				Description: item.Description,
				HTMLURL:     item.HTMLURL,
				CloneURL:    item.CloneURL,
				Language:    item.Language,
				Size:        item.Size,
				StarCount:   item.StargazersCount,
				ForkCount:   item.ForksCount,
				IsPrivate:   item.Private,
				IsFork:      item.Fork,
				IsArchived:  false, // Gitee API 需要额外判断
				CreatedAt:   createdAt,
				UpdatedAt:   updatedAt,
				PushedAt:    &pushedAt,
			},
			Matches: []Match{}, // 仓库搜索不包含具体匹配
		}
	}
	
	return &SearchResponse{
		Platform:          "gitee",
		TotalCount:        searchResp.TotalCount,
		IncompleteResults: false,
		Items:             items,
		RateLimit:         rateLimit,
		HasMore:           len(items) == req.PerPage,
		NextPage:          req.Page + 1,
	}, nil
}

// SearchRepositories 搜索仓库
func (g *GiteeAdapter) SearchRepositories(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	// Gitee 的仓库搜索
	return g.SearchCode(ctx, req)
}

// GetFileContent 获取文件内容
func (g *GiteeAdapter) GetFileContent(ctx context.Context, account *Account, fileURL string) (*FileContent, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", fileURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get file content failed: status %d", resp.StatusCode)
	}
	
	var fileResp struct {
		Name        string `json:"name"`
		Path        string `json:"path"`
		SHA         string `json:"sha"`
		Size        int64  `json:"size"`
		URL         string `json:"url"`
		HTMLURL     string `json:"html_url"`
		DownloadURL string `json:"download_url"`
		Type        string `json:"type"`
		Content     string `json:"content"`
		Encoding    string `json:"encoding"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&fileResp); err != nil {
		return nil, err
	}
	
	return &FileContent{
		Name:        fileResp.Name,
		Path:        fileResp.Path,
		SHA:         fileResp.SHA,
		Size:        fileResp.Size,
		URL:         fileResp.URL,
		HTMLURL:     fileResp.HTMLURL,
		DownloadURL: fileResp.DownloadURL,
		Type:        fileResp.Type,
		Content:     fileResp.Content,
		Encoding:    fileResp.Encoding,
	}, nil
}

// SearchCodeInRepository 在指定仓库中搜索代码
func (g *GiteeAdapter) SearchCodeInRepository(ctx context.Context, owner, repo string, query string, account *Account) (*SearchResponse, error) {
	// Gitee 的仓库内代码搜索
	params := url.Values{}
	params.Set("q", query)
	params.Set("per_page", "100")
	params.Set("page", "1")
	
	searchURL := fmt.Sprintf("%s/repos/%s/%s/search/code?%s", g.baseURL, owner, repo, params.Encode())
	
	req, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("code search failed: status %d", resp.StatusCode)
	}
	
	// TODO: 解析代码搜索结果
	// Gitee 的代码搜索API响应格式需要根据实际API文档调整
	
	return &SearchResponse{
		Platform:   "gitee",
		TotalCount: 0,
		Items:      []SearchItem{},
		RateLimit:  g.parseRateLimitHeaders(resp.Header),
	}, nil
}

// 辅助方法

func (g *GiteeAdapter) setAuthHeaders(req *http.Request, account *Account) {
	if account != nil && account.Token != "" {
		// Gitee 使用 access_token 参数或 Authorization 头
		if account.AccountType == "token" {
			req.Header.Set("Authorization", "token "+account.Token)
		} else {
			// 也可以作为查询参数
			q := req.URL.Query()
			q.Set("access_token", account.Token)
			req.URL.RawQuery = q.Encode()
		}
	}
	req.Header.Set("User-Agent", "GodEye/1.0")
}

func (g *GiteeAdapter) buildSearchQuery(req *SearchRequest) string {
	var parts []string
	
	// 添加关键词
	if len(req.Keywords) > 0 {
		keywordQuery := strings.Join(req.Keywords, " ")
		parts = append(parts, keywordQuery)
	}
	
	// 添加语言过滤
	if req.Language != "" {
		parts = append(parts, "language:"+req.Language)
	}
	
	// 添加排除条件
	for _, exclude := range req.ExcludeUsers {
		parts = append(parts, "-user:"+exclude)
	}
	
	for _, exclude := range req.ExcludeRepos {
		parts = append(parts, "-repo:"+exclude)
	}
	
	return strings.Join(parts, " ")
}

func (g *GiteeAdapter) convertSortField(sort string) string {
	switch sort {
	case "indexed":
		return "updated"
	case "updated":
		return "updated"
	case "created":
		return "created"
	default:
		return "updated"
	}
}

func (g *GiteeAdapter) convertSortOrder(order string) string {
	switch order {
	case "desc":
		return "desc"
	case "asc":
		return "asc"
	default:
		return "desc"
	}
}

func (g *GiteeAdapter) parseRateLimitHeaders(headers http.Header) *RateLimit {
	// Gitee 的速率限制头（需要根据实际API调整）
	limit, _ := strconv.Atoi(headers.Get("X-RateLimit-Limit"))
	remaining, _ := strconv.Atoi(headers.Get("X-RateLimit-Remaining"))
	reset, _ := strconv.ParseInt(headers.Get("X-RateLimit-Reset"), 10, 64)
	
	var resetTime *time.Time
	if reset > 0 {
		t := time.Unix(reset, 0)
		resetTime = &t
	}
	
	return &RateLimit{
		Limit:     limit,
		Remaining: remaining,
		ResetAt:   resetTime,
		Used:      limit - remaining,
	}
}

// GetRepositories 获取仓库列表
func (g *GiteeAdapter) GetRepositories(ctx context.Context, account *Account, params map[string]string) ([]interface{}, error) {
	urlParams := url.Values{}
	for key, value := range params {
		urlParams.Set(key, value)
	}
	
	reposURL := fmt.Sprintf("%s/user/repos?%s", g.baseURL, urlParams.Encode())
	
	req, err := http.NewRequestWithContext(ctx, "GET", reposURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get repositories failed: status %d", resp.StatusCode)
	}
	
	var repos []interface{}
	if err := json.NewDecoder(resp.Body).Decode(&repos); err != nil {
		return nil, err
	}
	
	return repos, nil
}

// GetOrganizations 获取组织列表
func (g *GiteeAdapter) GetOrganizations(ctx context.Context, account *Account) ([]interface{}, error) {
	orgsURL := fmt.Sprintf("%s/user/orgs", g.baseURL)
	
	req, err := http.NewRequestWithContext(ctx, "GET", orgsURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get organizations failed: status %d", resp.StatusCode)
	}
	
	var orgs []interface{}
	if err := json.NewDecoder(resp.Body).Decode(&orgs); err != nil {
		return nil, err
	}
	
	return orgs, nil
}

// GetUserInfo 获取用户信息
func (g *GiteeAdapter) GetUserInfo(ctx context.Context, account *Account) (map[string]interface{}, error) {
	userURL := fmt.Sprintf("%s/user", g.baseURL)
	
	req, err := http.NewRequestWithContext(ctx, "GET", userURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get user info failed: status %d", resp.StatusCode)
	}
	
	var userInfo map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, err
	}
	
	return userInfo, nil
}
