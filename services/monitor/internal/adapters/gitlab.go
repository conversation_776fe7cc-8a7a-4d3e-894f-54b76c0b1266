package adapters

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// GitLabAdapter GitLab平台适配器
type GitLabAdapter struct {
	baseURL    string
	httpClient *http.Client
}

// NewGitLabAdapter 创建GitLab适配器
func NewGitLabAdapter() *GitLabAdapter {
	return &GitLabAdapter{
		baseURL: "https://gitlab.com/api/v4",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetPlatformName 获取平台名称
func (g *GitLabAdapter) GetPlatformName() string {
	return "gitlab"
}

// ValidateAccount 验证账号有效性
func (g *GitLabAdapter) ValidateAccount(ctx context.Context, account *Account) error {
	req, err := http.NewRequestWithContext(ctx, "GET", g.baseURL+"/user", nil)
	if err != nil {
		return err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("invalid account: status %d", resp.StatusCode)
	}
	
	return nil
}

// GetRateLimit 获取API限制信息
func (g *GitLabAdapter) GetRateLimit(ctx context.Context, account *Account) (*RateLimit, error) {
	// GitLab API 限制信息通常在响应头中
	req, err := http.NewRequestWithContext(ctx, "GET", g.baseURL+"/user", nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	return g.parseRateLimitHeaders(resp.Header), nil
}

// SearchCode 搜索代码
func (g *GitLabAdapter) SearchCode(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	// GitLab 使用不同的搜索API
	query := g.buildSearchQuery(req)
	
	params := url.Values{}
	params.Set("search", query)
	params.Set("scope", "blobs")
	params.Set("per_page", strconv.Itoa(req.PerPage))
	params.Set("page", strconv.Itoa(req.Page))
	
	if req.Sort != "" {
		params.Set("order_by", g.convertSortField(req.Sort))
		params.Set("sort", g.convertSortOrder(req.Order))
	}
	
	searchURL := fmt.Sprintf("%s/search?%s", g.baseURL, params.Encode())
	
	httpReq, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(httpReq, req.Account)
	
	resp, err := g.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("search failed: status %d", resp.StatusCode)
	}
	
	var searchResp []struct {
		ID       int    `json:"id"`
		Filename string `json:"filename"`
		Path     string `json:"path"`
		Ref      string `json:"ref"`
		Startline int   `json:"startline"`
		Data     string `json:"data"`
		ProjectID int   `json:"project_id"`
		Project  struct {
			ID                int    `json:"id"`
			Name              string `json:"name"`
			NameWithNamespace string `json:"name_with_namespace"`
			Path              string `json:"path"`
			PathWithNamespace string `json:"path_with_namespace"`
			WebURL            string `json:"web_url"`
			Description       string `json:"description"`
			DefaultBranch     string `json:"default_branch"`
			Visibility        string `json:"visibility"`
			ForksCount        int    `json:"forks_count"`
			StarCount         int    `json:"star_count"`
			CreatedAt         string `json:"created_at"`
			LastActivityAt    string `json:"last_activity_at"`
			Namespace         struct {
				ID       int    `json:"id"`
				Name     string `json:"name"`
				Path     string `json:"path"`
				Kind     string `json:"kind"`
				FullPath string `json:"full_path"`
				WebURL   string `json:"web_url"`
			} `json:"namespace"`
		} `json:"project"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, err
	}
	
	// 获取速率限制信息
	rateLimit := g.parseRateLimitHeaders(resp.Header)
	
	// 转换为统一格式
	items := make([]SearchItem, len(searchResp))
	for i, item := range searchResp {
		createdAt, _ := time.Parse(time.RFC3339, item.Project.CreatedAt)
		lastActivityAt, _ := time.Parse(time.RFC3339, item.Project.LastActivityAt)
		
		items[i] = SearchItem{
			Type:       "code",
			PlatformID: strconv.Itoa(item.Project.ID),
			Score:      1.0, // GitLab 不提供评分
			Repository: &Repository{
				ID:          int64(item.Project.ID),
				Name:        item.Project.Name,
				FullName:    item.Project.PathWithNamespace,
				Owner: &User{
					ID:      int64(item.Project.Namespace.ID),
					Login:   item.Project.Namespace.Path,
					Type:    item.Project.Namespace.Kind,
					HTMLURL: item.Project.Namespace.WebURL,
				},
				Description: item.Project.Description,
				HTMLURL:     item.Project.WebURL,
				CloneURL:    item.Project.WebURL + ".git",
				Language:    "", // GitLab API 需要额外请求获取
				Size:        0,  // GitLab API 需要额外请求获取
				StarCount:   item.Project.StarCount,
				ForkCount:   item.Project.ForksCount,
				IsPrivate:   item.Project.Visibility != "public",
				IsFork:      false, // 需要额外判断
				IsArchived:  false, // 需要额外判断
				CreatedAt:   createdAt,
				UpdatedAt:   lastActivityAt,
				PushedAt:    &lastActivityAt,
			},
			File: &File{
				Name:    item.Filename,
				Path:    item.Path,
				SHA:     item.Ref,
				URL:     fmt.Sprintf("%s/api/v4/projects/%d/repository/files/%s/raw?ref=%s", g.baseURL, item.ProjectID, url.QueryEscape(item.Path), item.Ref),
				HTMLURL: fmt.Sprintf("%s/-/blob/%s/%s", item.Project.WebURL, item.Ref, item.Path),
			},
			Matches: g.convertMatches(item.Data, item.Startline),
		}
	}
	
	return &SearchResponse{
		Platform:          "gitlab",
		TotalCount:        len(items), // GitLab 不提供总数
		IncompleteResults: false,
		Items:             items,
		RateLimit:         rateLimit,
		HasMore:           len(items) == req.PerPage,
		NextPage:          req.Page + 1,
	}, nil
}

// SearchRepositories 搜索仓库
func (g *GitLabAdapter) SearchRepositories(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	query := g.buildRepositorySearchQuery(req)
	
	params := url.Values{}
	params.Set("search", query)
	params.Set("scope", "projects")
	params.Set("per_page", strconv.Itoa(req.PerPage))
	params.Set("page", strconv.Itoa(req.Page))
	
	if req.Sort != "" {
		params.Set("order_by", g.convertSortField(req.Sort))
		params.Set("sort", g.convertSortOrder(req.Order))
	}
	
	searchURL := fmt.Sprintf("%s/search?%s", g.baseURL, params.Encode())
	
	httpReq, err := http.NewRequestWithContext(ctx, "GET", searchURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(httpReq, req.Account)
	
	resp, err := g.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("repository search failed: status %d", resp.StatusCode)
	}
	
	// TODO: 实现仓库搜索响应解析
	// 类似于代码搜索，但返回项目信息
	
	return &SearchResponse{
		Platform:   "gitlab",
		TotalCount: 0,
		Items:      []SearchItem{},
		RateLimit:  g.parseRateLimitHeaders(resp.Header),
	}, nil
}

// GetFileContent 获取文件内容
func (g *GitLabAdapter) GetFileContent(ctx context.Context, account *Account, fileURL string) (*FileContent, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", fileURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get file content failed: status %d", resp.StatusCode)
	}
	
	var fileResp struct {
		FileName     string `json:"file_name"`
		FilePath     string `json:"file_path"`
		Size         int64  `json:"size"`
		Encoding     string `json:"encoding"`
		Content      string `json:"content"`
		ContentSha256 string `json:"content_sha256"`
		Ref          string `json:"ref"`
		BlobID       string `json:"blob_id"`
		CommitID     string `json:"commit_id"`
		LastCommitID string `json:"last_commit_id"`
	}
	
	if err := json.NewDecoder(resp.Body).Decode(&fileResp); err != nil {
		return nil, err
	}
	
	return &FileContent{
		Name:        fileResp.FileName,
		Path:        fileResp.FilePath,
		SHA:         fileResp.BlobID,
		Size:        fileResp.Size,
		URL:         fileURL,
		HTMLURL:     "", // 需要构建
		DownloadURL: fileURL,
		Type:        "file",
		Content:     fileResp.Content,
		Encoding:    fileResp.Encoding,
	}, nil
}

// 辅助方法

func (g *GitLabAdapter) setAuthHeaders(req *http.Request, account *Account) {
	if account != nil && account.Token != "" {
		req.Header.Set("Authorization", "Bearer "+account.Token)
	}
	req.Header.Set("User-Agent", "GodEye/1.0")
}

func (g *GitLabAdapter) buildSearchQuery(req *SearchRequest) string {
	var parts []string
	
	// 添加关键词
	if len(req.Keywords) > 0 {
		keywordQuery := strings.Join(req.Keywords, " ")
		parts = append(parts, keywordQuery)
	}
	
	// GitLab 搜索语法与 GitHub 不同，需要适配
	return strings.Join(parts, " ")
}

func (g *GitLabAdapter) buildRepositorySearchQuery(req *SearchRequest) string {
	return g.buildSearchQuery(req)
}

func (g *GitLabAdapter) convertSortField(sort string) string {
	switch sort {
	case "indexed":
		return "updated_at"
	case "updated":
		return "updated_at"
	case "created":
		return "created_at"
	default:
		return "updated_at"
	}
}

func (g *GitLabAdapter) convertSortOrder(order string) string {
	switch order {
	case "desc":
		return "desc"
	case "asc":
		return "asc"
	default:
		return "desc"
	}
}

func (g *GitLabAdapter) parseRateLimitHeaders(headers http.Header) *RateLimit {
	// GitLab 使用不同的速率限制头
	limit, _ := strconv.Atoi(headers.Get("RateLimit-Limit"))
	remaining, _ := strconv.Atoi(headers.Get("RateLimit-Remaining"))
	reset, _ := strconv.ParseInt(headers.Get("RateLimit-Reset"), 10, 64)
	
	var resetTime *time.Time
	if reset > 0 {
		t := time.Unix(reset, 0)
		resetTime = &t
	}
	
	return &RateLimit{
		Limit:     limit,
		Remaining: remaining,
		ResetAt:   resetTime,
		Used:      limit - remaining,
	}
}

func (g *GitLabAdapter) convertMatches(data string, startLine int) []Match {
	if data == "" {
		return []Match{}
	}
	
	return []Match{
		{
			Text:       data,
			LineNumber: startLine,
			Fragment:   data,
			Matches: []TextMatch{
				{
					Fragment: data,
				},
			},
		},
	}
}

// SetBaseURL 设置自定义GitLab实例URL
func (g *GitLabAdapter) SetBaseURL(baseURL string) {
	g.baseURL = strings.TrimSuffix(baseURL, "/") + "/api/v4"
}

// GetProjects 获取项目列表
func (g *GitLabAdapter) GetProjects(ctx context.Context, account *Account, params map[string]string) ([]interface{}, error) {
	urlParams := url.Values{}
	for key, value := range params {
		urlParams.Set(key, value)
	}
	
	projectsURL := fmt.Sprintf("%s/projects?%s", g.baseURL, urlParams.Encode())
	
	req, err := http.NewRequestWithContext(ctx, "GET", projectsURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get projects failed: status %d", resp.StatusCode)
	}
	
	var projects []interface{}
	if err := json.NewDecoder(resp.Body).Decode(&projects); err != nil {
		return nil, err
	}
	
	return projects, nil
}

// GetGroups 获取组织列表
func (g *GitLabAdapter) GetGroups(ctx context.Context, account *Account, params map[string]string) ([]interface{}, error) {
	urlParams := url.Values{}
	for key, value := range params {
		urlParams.Set(key, value)
	}
	
	groupsURL := fmt.Sprintf("%s/groups?%s", g.baseURL, urlParams.Encode())
	
	req, err := http.NewRequestWithContext(ctx, "GET", groupsURL, nil)
	if err != nil {
		return nil, err
	}
	
	g.setAuthHeaders(req, account)
	
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get groups failed: status %d", resp.StatusCode)
	}
	
	var groups []interface{}
	if err := json.NewDecoder(resp.Body).Decode(&groups); err != nil {
		return nil, err
	}
	
	return groups, nil
}
