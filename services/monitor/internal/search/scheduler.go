package search

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/adapters"
	"github.com/godeye/monitor/internal/models"
)

// SearchScheduler 搜索调度器
type SearchScheduler struct {
	db             *gorm.DB
	adapterManager *adapters.AdapterManager
	accountPool    *AccountPool
	taskQueue      chan *SearchTask
	workers        int
	stopCh         chan struct{}
	wg             sync.WaitGroup
	mu             sync.RWMutex
	running        bool
}

// SearchTask 搜索任务
type SearchTask struct {
	ID          uuid.UUID
	Type        string // "global_search", "repository_monitor"
	TaskID      uuid.UUID
	UserID      uuid.UUID
	Config      map[string]interface{}
	Priority    int
	CreatedAt   time.Time
	Retries     int
	MaxRetries  int
}

// NewSearchScheduler 创建搜索调度器
func NewSearchScheduler(db *gorm.DB, adapterManager *adapters.AdapterManager, workers int) *SearchScheduler {
	return &SearchScheduler{
		db:             db,
		adapterManager: adapterManager,
		accountPool:    NewAccountPool(db),
		taskQueue:      make(chan *SearchTask, 1000),
		workers:        workers,
		stopCh:         make(chan struct{}),
		running:        false,
	}
}

// Start 启动调度器
func (s *SearchScheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("scheduler is already running")
	}

	// 加载账号池
	if err := s.accountPool.LoadAccounts(ctx); err != nil {
		return fmt.Errorf("failed to load accounts: %w", err)
	}

	s.running = true

	// 启动工作协程
	for i := 0; i < s.workers; i++ {
		s.wg.Add(1)
		go s.worker(ctx, i)
	}

	// 启动任务调度协程
	s.wg.Add(1)
	go s.taskScheduler(ctx)

	log.Printf("Search scheduler started with %d workers", s.workers)
	return nil
}

// Stop 停止调度器
func (s *SearchScheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if !s.running {
		return fmt.Errorf("scheduler is not running")
	}
	
	close(s.stopCh)
	s.wg.Wait()
	s.running = false
	
	log.Println("Search scheduler stopped")
	return nil
}

// SubmitGlobalSearchTask 提交全量搜索任务
func (s *SearchScheduler) SubmitGlobalSearchTask(taskID, userID uuid.UUID, priority int) error {
	task := &SearchTask{
		ID:         uuid.New(),
		Type:       "global_search",
		TaskID:     taskID,
		UserID:     userID,
		Priority:   priority,
		CreatedAt:  time.Now(),
		MaxRetries: 3,
	}
	
	select {
	case s.taskQueue <- task:
		return nil
	default:
		return fmt.Errorf("task queue is full")
	}
}

// SubmitRepositoryMonitorTask 提交仓库监控任务
func (s *SearchScheduler) SubmitRepositoryMonitorTask(taskID, userID uuid.UUID, priority int) error {
	task := &SearchTask{
		ID:         uuid.New(),
		Type:       "repository_monitor",
		TaskID:     taskID,
		UserID:     userID,
		Priority:   priority,
		CreatedAt:  time.Now(),
		MaxRetries: 3,
	}
	
	select {
	case s.taskQueue <- task:
		return nil
	default:
		return fmt.Errorf("task queue is full")
	}
}

// worker 工作协程
func (s *SearchScheduler) worker(ctx context.Context, workerID int) {
	defer s.wg.Done()
	
	log.Printf("Worker %d started", workerID)
	
	for {
		select {
		case <-ctx.Done():
			log.Printf("Worker %d stopped due to context cancellation", workerID)
			return
		case <-s.stopCh:
			log.Printf("Worker %d stopped", workerID)
			return
		case task := <-s.taskQueue:
			s.processTask(ctx, workerID, task)
		}
	}
}

// processTask 处理任务
func (s *SearchScheduler) processTask(ctx context.Context, workerID int, task *SearchTask) {
	log.Printf("Worker %d processing task %s (type: %s)", workerID, task.ID, task.Type)
	
	var err error
	switch task.Type {
	case "global_search":
		err = s.processGlobalSearchTask(ctx, task)
	case "repository_monitor":
		err = s.processRepositoryMonitorTask(ctx, task)
	default:
		err = fmt.Errorf("unknown task type: %s", task.Type)
	}
	
	if err != nil {
		log.Printf("Worker %d failed to process task %s: %v", workerID, task.ID, err)
		
		// 重试逻辑
		if task.Retries < task.MaxRetries {
			task.Retries++
			log.Printf("Retrying task %s (attempt %d/%d)", task.ID, task.Retries, task.MaxRetries)
			
			// 延迟重试
			go func() {
				time.Sleep(time.Duration(task.Retries) * time.Minute)
				select {
				case s.taskQueue <- task:
				default:
					log.Printf("Failed to requeue task %s: queue full", task.ID)
				}
			}()
		} else {
			log.Printf("Task %s failed after %d retries", task.ID, task.MaxRetries)
		}
	} else {
		log.Printf("Worker %d completed task %s", workerID, task.ID)
	}
}

// processGlobalSearchTask 处理全量搜索任务
func (s *SearchScheduler) processGlobalSearchTask(ctx context.Context, task *SearchTask) error {
	// 获取任务配置
	var globalTask models.GlobalSearchTask
	if err := s.db.First(&globalTask, "id = ?", task.TaskID).Error; err != nil {
		return fmt.Errorf("failed to get global search task: %w", err)
	}
	
	// 创建搜索运行记录
	searchRun := &models.GlobalSearchRun{
		TaskID:    globalTask.ID,
		UserID:    globalTask.UserID,
		Status:    "running",
		StartedAt: time.Now(),
		Config:    models.JSONB{
			"platforms":     globalTask.Platforms,
			"keywords":      globalTask.Keywords,
			"search_type":   globalTask.SearchType,
			"file_types":    globalTask.FileTypes,
			"exclude_rules": globalTask.ExcludeRules,
		},
	}
	
	if err := s.db.Create(searchRun).Error; err != nil {
		return fmt.Errorf("failed to create search run: %w", err)
	}
	
	// 更新任务状态
	if err := s.db.Model(&globalTask).Updates(map[string]interface{}{
		"status":      "running",
		"last_run_at": time.Now(),
		"last_run_id": searchRun.ID,
	}).Error; err != nil {
		return fmt.Errorf("failed to update task status: %w", err)
	}
	
	// 执行搜索
	executor := NewGlobalSearchExecutor(s.db, s.adapterManager, s.accountPool)
	err := executor.Execute(ctx, &globalTask, searchRun)
	
	// 更新运行状态
	completedAt := time.Now()
	duration := completedAt.Sub(searchRun.StartedAt).Seconds()
	
	updates := map[string]interface{}{
		"completed_at": completedAt,
		"duration":     int64(duration),
	}
	
	if err != nil {
		updates["status"] = "failed"
		updates["error_message"] = err.Error()
		
		// 更新任务失败次数
		s.db.Model(&globalTask).UpdateColumn("failed_runs", gorm.Expr("failed_runs + 1"))
	} else {
		updates["status"] = "completed"
		
		// 更新任务成功次数
		s.db.Model(&globalTask).UpdateColumn("success_runs", gorm.Expr("success_runs + 1"))
	}
	
	// 更新总运行次数
	s.db.Model(&globalTask).UpdateColumn("total_runs", gorm.Expr("total_runs + 1"))
	
	// 更新运行记录
	if err := s.db.Model(searchRun).Updates(updates).Error; err != nil {
		log.Printf("Failed to update search run status: %v", err)
	}
	
	// 更新任务状态
	taskStatus := "completed"
	if err != nil {
		taskStatus = "failed"
	}
	
	if err := s.db.Model(&globalTask).Update("status", taskStatus).Error; err != nil {
		log.Printf("Failed to update task status: %v", err)
	}
	
	return err
}

// processRepositoryMonitorTask 处理仓库监控任务
func (s *SearchScheduler) processRepositoryMonitorTask(ctx context.Context, task *SearchTask) error {
	// 获取任务配置
	var monitorTask models.MonitorTask
	if err := s.db.First(&monitorTask, "id = ?", task.TaskID).Error; err != nil {
		return fmt.Errorf("failed to get monitor task: %w", err)
	}
	
	// 创建扫描运行记录
	scanRun := &models.ScanRun{
		TaskID:    monitorTask.ID,
		UserID:    monitorTask.UserID,
		Status:    "running",
		StartedAt: time.Now(),
		Config:    models.JSONB{
			"target_type":   monitorTask.TargetType,
			"target_value":  monitorTask.TargetValue,
			"keywords":      monitorTask.Keywords,
			"patterns":      monitorTask.Patterns,
			"exclude_rules": monitorTask.ExcludeRules,
		},
	}
	
	if err := s.db.Create(scanRun).Error; err != nil {
		return fmt.Errorf("failed to create scan run: %w", err)
	}
	
	// 更新任务状态
	if err := s.db.Model(&monitorTask).Updates(map[string]interface{}{
		"status":      "running",
		"last_run_at": time.Now(),
		"last_run_id": scanRun.ID,
	}).Error; err != nil {
		return fmt.Errorf("failed to update task status: %w", err)
	}
	
	// 执行监控扫描
	executor := NewRepositoryMonitorExecutor(s.db, s.adapterManager, s.accountPool)
	err := executor.Execute(ctx, &monitorTask, scanRun)
	
	// 更新运行状态
	completedAt := time.Now()
	duration := completedAt.Sub(scanRun.StartedAt).Seconds()
	
	updates := map[string]interface{}{
		"completed_at": completedAt,
		"duration":     int64(duration),
	}
	
	if err != nil {
		updates["status"] = "failed"
		updates["error_message"] = err.Error()
		
		// 更新任务失败次数
		s.db.Model(&monitorTask).UpdateColumn("failed_runs", gorm.Expr("failed_runs + 1"))
	} else {
		updates["status"] = "completed"
		
		// 更新任务成功次数
		s.db.Model(&monitorTask).UpdateColumn("success_runs", gorm.Expr("success_runs + 1"))
	}
	
	// 更新总运行次数
	s.db.Model(&monitorTask).UpdateColumn("total_runs", gorm.Expr("total_runs + 1"))
	
	// 更新运行记录
	if err := s.db.Model(scanRun).Updates(updates).Error; err != nil {
		log.Printf("Failed to update scan run status: %v", err)
	}
	
	// 更新任务状态
	taskStatus := "completed"
	if err != nil {
		taskStatus = "failed"
	}
	
	if err := s.db.Model(&monitorTask).Update("status", taskStatus).Error; err != nil {
		log.Printf("Failed to update task status: %v", err)
	}
	
	return err
}

// taskScheduler 任务调度协程
func (s *SearchScheduler) taskScheduler(ctx context.Context) {
	defer s.wg.Done()
	
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopCh:
			return
		case <-ticker.C:
			s.scheduleActiveTasks(ctx)
		}
	}
}

// scheduleActiveTasks 调度活跃任务
func (s *SearchScheduler) scheduleActiveTasks(ctx context.Context) {
	now := time.Now()
	
	// 调度全量搜索任务
	var globalTasks []models.GlobalSearchTask
	if err := s.db.Where("is_active = ? AND status != ? AND (next_run_at IS NULL OR next_run_at <= ?)",
		true, "running", now).Find(&globalTasks).Error; err != nil {
		log.Printf("Failed to query global search tasks: %v", err)
		return
	}
	
	for _, task := range globalTasks {
		if err := s.SubmitGlobalSearchTask(task.ID, task.UserID, 1); err != nil {
			log.Printf("Failed to submit global search task %s: %v", task.ID, err)
		}
	}
	
	// 调度仓库监控任务
	var monitorTasks []models.MonitorTask
	if err := s.db.Where("is_active = ? AND status != ? AND (next_run_at IS NULL OR next_run_at <= ?)",
		true, "running", now).Find(&monitorTasks).Error; err != nil {
		log.Printf("Failed to query monitor tasks: %v", err)
		return
	}
	
	for _, task := range monitorTasks {
		if err := s.SubmitRepositoryMonitorTask(task.ID, task.UserID, 1); err != nil {
			log.Printf("Failed to submit repository monitor task %s: %v", task.ID, err)
		}
	}
}
