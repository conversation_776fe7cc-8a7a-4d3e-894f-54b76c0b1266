package search

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/adapters"
	"github.com/godeye/monitor/internal/models"
)

// RepositoryMonitorExecutor 仓库监控执行器
type RepositoryMonitorExecutor struct {
	db             *gorm.DB
	adapterManager *adapters.AdapterManager
	accountPool    *AccountPool
	riskAssessor   *RiskAssessor
}

// NewRepositoryMonitorExecutor 创建仓库监控执行器
func NewRepositoryMonitorExecutor(db *gorm.DB, adapterManager *adapters.AdapterManager, accountPool *AccountPool) *RepositoryMonitorExecutor {
	return &RepositoryMonitorExecutor{
		db:             db,
		adapterManager: adapterManager,
		accountPool:    accountPool,
		riskAssessor:   NewRiskAssessor(),
	}
}

// Execute 执行仓库监控
func (e *RepositoryMonitorExecutor) Execute(ctx context.Context, task *models.MonitorTask, run *models.ScanRun) error {
	log.Printf("Starting repository monitor execution for task %s", task.ID)
	
	// 解析监控配置
	keywords, err := e.parseKeywords(task.Keywords)
	if err != nil {
		return fmt.Errorf("failed to parse keywords: %w", err)
	}
	
	patterns, err := e.parsePatterns(task.Patterns)
	if err != nil {
		return fmt.Errorf("failed to parse patterns: %w", err)
	}
	
	excludeRules, err := e.parseExcludeRules(task.ExcludeRules)
	if err != nil {
		return fmt.Errorf("failed to parse exclude rules: %w", err)
	}
	
	// 根据目标类型执行不同的监控策略
	var results []*adapters.SearchItem
	var scanStats map[string]interface{}
	
	switch task.TargetType {
	case "repository":
		results, scanStats, err = e.scanRepository(ctx, task.TargetValue, keywords, patterns, excludeRules, task)
	case "user":
		results, scanStats, err = e.scanUserRepositories(ctx, task.TargetValue, keywords, patterns, excludeRules, task)
	case "organization":
		results, scanStats, err = e.scanOrganizationRepositories(ctx, task.TargetValue, keywords, patterns, excludeRules, task)
	case "topic":
		results, scanStats, err = e.scanTopicRepositories(ctx, task.TargetValue, keywords, patterns, excludeRules, task)
	case "platform":
		results, scanStats, err = e.scanPlatform(ctx, keywords, patterns, excludeRules, task)
	default:
		return fmt.Errorf("unsupported target type: %s", task.TargetType)
	}
	
	if err != nil {
		return fmt.Errorf("scan failed: %w", err)
	}
	
	// 保存扫描结果
	if err := e.saveScanResults(ctx, run, results, task); err != nil {
		return fmt.Errorf("failed to save scan results: %w", err)
	}
	
	// 更新运行统计
	updates := map[string]interface{}{
		"scan_stats":    models.JSONB(scanStats),
		"total_results": len(results),
	}
	
	if err := e.db.Model(run).Updates(updates).Error; err != nil {
		log.Printf("Failed to update run statistics: %v", err)
	}
	
	log.Printf("Repository monitor completed: %d results found", len(results))
	return nil
}

// scanRepository 扫描单个仓库
func (e *RepositoryMonitorExecutor) scanRepository(ctx context.Context, repoFullName string, 
	keywords []string, patterns []string, excludeRules map[string][]string, task *models.MonitorTask) ([]*adapters.SearchItem, map[string]interface{}, error) {
	
	// 解析仓库信息
	parts := strings.Split(repoFullName, "/")
	if len(parts) != 2 {
		return nil, nil, fmt.Errorf("invalid repository format: %s", repoFullName)
	}
	
	// 确定平台（默认GitHub）
	platform := "github"
	if task.Platform != "" {
		platform = task.Platform
	}
	
	adapter, exists := e.adapterManager.GetAdapter(platform)
	if !exists {
		return nil, nil, fmt.Errorf("adapter not found for platform: %s", platform)
	}
	
	// 获取账号
	account, err := e.accountPool.GetAvailableAccount(platform)
	if err != nil {
		return nil, nil, fmt.Errorf("no available account for platform %s: %w", platform, err)
	}
	defer e.accountPool.ReleaseAccount(account.ID)
	
	var allResults []*adapters.SearchItem
	stats := map[string]interface{}{
		"platform":         platform,
		"repository":       repoFullName,
		"keywords_scanned": 0,
		"patterns_scanned": 0,
		"total_queries":    0,
		"successful_queries": 0,
		"failed_queries":   0,
	}
	
	// 扫描关键词
	for _, keyword := range keywords {
		query := fmt.Sprintf("%s repo:%s", keyword, repoFullName)
		
		searchReq := &adapters.SearchRequest{
			Query:       query,
			Keywords:    []string{keyword},
			SearchType:  "code",
			ExcludeUsers: excludeRules["users"],
			ExcludeRepos: excludeRules["repos"],
			ExcludeTerms: excludeRules["terms"],
			Page:        1,
			PerPage:     100,
			Sort:        "indexed",
			Order:       "desc",
			Account:     account,
		}
		
		stats["total_queries"] = stats["total_queries"].(int) + 1
		
		resp, err := adapter.SearchCode(ctx, searchReq)
		if err != nil {
			log.Printf("Keyword search failed for %s in %s: %v", keyword, repoFullName, err)
			stats["failed_queries"] = stats["failed_queries"].(int) + 1
			e.accountPool.UpdateAccountUsage(account.ID, nil, false)
			continue
		}
		
		stats["successful_queries"] = stats["successful_queries"].(int) + 1
		e.accountPool.UpdateAccountUsage(account.ID, resp.RateLimit, true)
		
		for _, item := range resp.Items {
			allResults = append(allResults, &item)
		}
		
		// 速率限制保护
		if resp.RateLimit != nil && resp.RateLimit.Remaining <= 1 {
			time.Sleep(30 * time.Second)
		} else {
			time.Sleep(1 * time.Second)
		}
	}
	
	stats["keywords_scanned"] = len(keywords)
	
	// TODO: 实现模式匹配扫描
	// 这需要获取文件内容并进行正则表达式匹配
	stats["patterns_scanned"] = len(patterns)
	
	return allResults, stats, nil
}

// scanUserRepositories 扫描用户的所有仓库
func (e *RepositoryMonitorExecutor) scanUserRepositories(ctx context.Context, username string, 
	keywords []string, patterns []string, excludeRules map[string][]string, task *models.MonitorTask) ([]*adapters.SearchItem, map[string]interface{}, error) {
	
	platform := "github"
	if task.Platform != "" {
		platform = task.Platform
	}
	
	adapter, exists := e.adapterManager.GetAdapter(platform)
	if !exists {
		return nil, nil, fmt.Errorf("adapter not found for platform: %s", platform)
	}
	
	account, err := e.accountPool.GetAvailableAccount(platform)
	if err != nil {
		return nil, nil, fmt.Errorf("no available account for platform %s: %w", platform, err)
	}
	defer e.accountPool.ReleaseAccount(account.ID)
	
	var allResults []*adapters.SearchItem
	stats := map[string]interface{}{
		"platform":           platform,
		"username":           username,
		"repositories_found": 0,
		"keywords_scanned":   0,
		"total_queries":      0,
		"successful_queries": 0,
		"failed_queries":     0,
	}
	
	// 搜索用户的仓库
	for _, keyword := range keywords {
		query := fmt.Sprintf("%s user:%s", keyword, username)
		
		searchReq := &adapters.SearchRequest{
			Query:       query,
			Keywords:    []string{keyword},
			SearchType:  "code",
			ExcludeUsers: excludeRules["users"],
			ExcludeRepos: excludeRules["repos"],
			ExcludeTerms: excludeRules["terms"],
			Page:        1,
			PerPage:     100,
			Sort:        "indexed",
			Order:       "desc",
			Account:     account,
		}
		
		stats["total_queries"] = stats["total_queries"].(int) + 1
		
		resp, err := adapter.SearchCode(ctx, searchReq)
		if err != nil {
			log.Printf("User search failed for %s in %s: %v", keyword, username, err)
			stats["failed_queries"] = stats["failed_queries"].(int) + 1
			continue
		}
		
		stats["successful_queries"] = stats["successful_queries"].(int) + 1
		
		for _, item := range resp.Items {
			allResults = append(allResults, &item)
		}
		
		time.Sleep(1 * time.Second)
	}
	
	stats["keywords_scanned"] = len(keywords)
	
	return allResults, stats, nil
}

// scanOrganizationRepositories 扫描组织的所有仓库
func (e *RepositoryMonitorExecutor) scanOrganizationRepositories(ctx context.Context, orgName string, 
	keywords []string, patterns []string, excludeRules map[string][]string, task *models.MonitorTask) ([]*adapters.SearchItem, map[string]interface{}, error) {
	
	platform := "github"
	if task.Platform != "" {
		platform = task.Platform
	}
	
	adapter, exists := e.adapterManager.GetAdapter(platform)
	if !exists {
		return nil, nil, fmt.Errorf("adapter not found for platform: %s", platform)
	}
	
	account, err := e.accountPool.GetAvailableAccount(platform)
	if err != nil {
		return nil, nil, fmt.Errorf("no available account for platform %s: %w", platform, err)
	}
	defer e.accountPool.ReleaseAccount(account.ID)
	
	var allResults []*adapters.SearchItem
	stats := map[string]interface{}{
		"platform":         platform,
		"organization":     orgName,
		"keywords_scanned": 0,
		"total_queries":    0,
		"successful_queries": 0,
		"failed_queries":   0,
	}
	
	// 搜索组织的仓库
	for _, keyword := range keywords {
		query := fmt.Sprintf("%s org:%s", keyword, orgName)
		
		searchReq := &adapters.SearchRequest{
			Query:       query,
			Keywords:    []string{keyword},
			SearchType:  "code",
			ExcludeUsers: excludeRules["users"],
			ExcludeRepos: excludeRules["repos"],
			ExcludeTerms: excludeRules["terms"],
			Page:        1,
			PerPage:     100,
			Sort:        "indexed",
			Order:       "desc",
			Account:     account,
		}
		
		stats["total_queries"] = stats["total_queries"].(int) + 1
		
		resp, err := adapter.SearchCode(ctx, searchReq)
		if err != nil {
			log.Printf("Organization search failed for %s in %s: %v", keyword, orgName, err)
			stats["failed_queries"] = stats["failed_queries"].(int) + 1
			continue
		}
		
		stats["successful_queries"] = stats["successful_queries"].(int) + 1
		
		for _, item := range resp.Items {
			allResults = append(allResults, &item)
		}
		
		time.Sleep(1 * time.Second)
	}
	
	stats["keywords_scanned"] = len(keywords)
	
	return allResults, stats, nil
}

// scanTopicRepositories 扫描特定主题的仓库
func (e *RepositoryMonitorExecutor) scanTopicRepositories(ctx context.Context, topic string, 
	keywords []string, patterns []string, excludeRules map[string][]string, task *models.MonitorTask) ([]*adapters.SearchItem, map[string]interface{}, error) {
	
	platform := "github"
	if task.Platform != "" {
		platform = task.Platform
	}
	
	adapter, exists := e.adapterManager.GetAdapter(platform)
	if !exists {
		return nil, nil, fmt.Errorf("adapter not found for platform: %s", platform)
	}
	
	account, err := e.accountPool.GetAvailableAccount(platform)
	if err != nil {
		return nil, nil, fmt.Errorf("no available account for platform %s: %w", platform, err)
	}
	defer e.accountPool.ReleaseAccount(account.ID)
	
	var allResults []*adapters.SearchItem
	stats := map[string]interface{}{
		"platform":         platform,
		"topic":            topic,
		"keywords_scanned": 0,
		"total_queries":    0,
		"successful_queries": 0,
		"failed_queries":   0,
	}
	
	// 搜索主题相关的仓库
	for _, keyword := range keywords {
		query := fmt.Sprintf("%s topic:%s", keyword, topic)
		
		searchReq := &adapters.SearchRequest{
			Query:       query,
			Keywords:    []string{keyword},
			SearchType:  "code",
			ExcludeUsers: excludeRules["users"],
			ExcludeRepos: excludeRules["repos"],
			ExcludeTerms: excludeRules["terms"],
			Page:        1,
			PerPage:     100,
			Sort:        "indexed",
			Order:       "desc",
			Account:     account,
		}
		
		stats["total_queries"] = stats["total_queries"].(int) + 1
		
		resp, err := adapter.SearchCode(ctx, searchReq)
		if err != nil {
			log.Printf("Topic search failed for %s in %s: %v", keyword, topic, err)
			stats["failed_queries"] = stats["failed_queries"].(int) + 1
			continue
		}
		
		stats["successful_queries"] = stats["successful_queries"].(int) + 1
		
		for _, item := range resp.Items {
			allResults = append(allResults, &item)
		}
		
		time.Sleep(1 * time.Second)
	}
	
	stats["keywords_scanned"] = len(keywords)
	
	return allResults, stats, nil
}

// scanPlatform 扫描整个平台
func (e *RepositoryMonitorExecutor) scanPlatform(ctx context.Context,
	keywords []string, patterns []string, excludeRules map[string][]string, task *models.MonitorTask) ([]*adapters.SearchItem, map[string]interface{}, error) {

	// 确定平台（默认GitHub）
	platform := "github"
	if task.Platform != "" {
		platform = task.Platform
	}

	adapter, exists := e.adapterManager.GetAdapter(platform)
	if !exists {
		return nil, nil, fmt.Errorf("adapter not found for platform: %s", platform)
	}

	account, err := e.accountPool.GetAvailableAccount(platform)
	if err != nil {
		return nil, nil, fmt.Errorf("no available account for platform %s: %w", platform, err)
	}

	var allResults []*adapters.SearchItem
	stats := map[string]interface{}{
		"total_queries":  0,
		"failed_queries": 0,
		"total_results":  0,
		"platform":       platform,
		"search_type":    "platform",
	}

	// 扫描关键词 - 全平台搜索，不限制特定仓库/用户/组织
	for _, keyword := range keywords {
		// 直接搜索关键词，不添加repo:、user:、org:等限制
		query := keyword

		searchReq := &adapters.SearchRequest{
			Query:       query,
			Keywords:    []string{keyword},
			SearchType:  "code",
			ExcludeUsers: excludeRules["users"],
			ExcludeRepos: excludeRules["repos"],
			ExcludeTerms: excludeRules["terms"],
			Page:        1,
			PerPage:     100,
			Sort:        "indexed",
			Order:       "desc",
			Account:     account,
		}

		stats["total_queries"] = stats["total_queries"].(int) + 1

		resp, err := adapter.SearchCode(ctx, searchReq)
		if err != nil {
			log.Printf("Platform search failed for keyword %s: %v", keyword, err)
			stats["failed_queries"] = stats["failed_queries"].(int) + 1
			e.accountPool.UpdateAccountUsage(account.ID, nil, false)
			continue
		}

		// 更新账号使用情况
		e.accountPool.UpdateAccountUsage(account.ID, resp.RateLimit, true)

		// 收集结果
		for _, item := range resp.Items {
			allResults = append(allResults, &item)
		}

		stats["total_results"] = stats["total_results"].(int) + len(resp.Items)

		log.Printf("Platform search for keyword '%s' found %d results", keyword, len(resp.Items))
	}

	log.Printf("Platform search completed: %d total results from %d queries",
		stats["total_results"], stats["total_queries"])

	return allResults, stats, nil
}

// saveScanResults 保存扫描结果
func (e *RepositoryMonitorExecutor) saveScanResults(ctx context.Context, run *models.ScanRun, 
	results []*adapters.SearchItem, task *models.MonitorTask) error {
	
	if len(results) == 0 {
		return nil
	}
	
	var scanResults []models.ScanResult
	
	for _, item := range results {
		// 风险评估
		riskAssessment := e.riskAssessor.AssessRisk(item)
		
		// 提取匹配信息
		matchContent, lineNumber := e.extractMatchInfo(item)
		
		scanResult := models.ScanResult{
			RunID:        run.ID,
			TaskID:       task.ID,
			UserID:       task.UserID,
			RepoOwner:    item.Repository.Owner.Login,
			RepoName:     item.Repository.Name,
			RepoURL:      item.Repository.HTMLURL,
			RepoFullName: item.Repository.FullName,
			FilePath:     item.File.Path,
			FileName:     item.File.Name,
			FileSize:     item.File.Size,
			FileURL:      item.File.URL,
			MatchType:    "keyword",
			MatchRule:    e.extractMatchRule(item),
			MatchContent: matchContent,
			LineNumber:   lineNumber,
			Context:      e.extractContext(item),
			RiskLevel:    riskAssessment.RiskLevel,
			Confidence:   riskAssessment.Confidence,
			Severity:     e.convertSeverityToScore(riskAssessment.Severity),
			Status:       "new",
		}
		
		scanResults = append(scanResults, scanResult)
	}
	
	// 批量插入
	batchSize := 100
	if err := e.db.CreateInBatches(scanResults, batchSize).Error; err != nil {
		return err
	}
	
	return nil
}

// 辅助方法

func (e *RepositoryMonitorExecutor) parseKeywords(keywordsJSON models.JSONB) ([]string, error) {
	if keywordsJSON == nil {
		return []string{}, nil
	}
	
	keywords, ok := keywordsJSON["keywords"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid keywords format")
	}
	
	result := make([]string, len(keywords))
	for i, k := range keywords {
		keyword, ok := k.(string)
		if !ok {
			return nil, fmt.Errorf("invalid keyword type")
		}
		result[i] = keyword
	}
	
	return result, nil
}

func (e *RepositoryMonitorExecutor) parsePatterns(patternsJSON models.JSONB) ([]string, error) {
	if patternsJSON == nil {
		return []string{}, nil
	}
	
	patterns, ok := patternsJSON["patterns"].([]interface{})
	if !ok {
		return []string{}, nil
	}
	
	result := make([]string, len(patterns))
	for i, p := range patterns {
		pattern, ok := p.(string)
		if !ok {
			continue
		}
		result[i] = pattern
	}
	
	return result, nil
}

func (e *RepositoryMonitorExecutor) parseExcludeRules(excludeRulesJSON models.JSONB) (map[string][]string, error) {
	result := map[string][]string{
		"users": {},
		"repos": {},
		"terms": {},
	}
	
	if excludeRulesJSON == nil {
		return result, nil
	}
	
	for key, value := range excludeRulesJSON {
		if rules, ok := value.([]interface{}); ok {
			stringRules := make([]string, 0, len(rules))
			for _, rule := range rules {
				if strRule, ok := rule.(string); ok {
					stringRules = append(stringRules, strRule)
				}
			}
			result[key] = stringRules
		}
	}
	
	return result, nil
}

func (e *RepositoryMonitorExecutor) extractMatchRule(item *adapters.SearchItem) string {
	if len(item.Matches) > 0 {
		return item.Matches[0].Text
	}
	return ""
}

func (e *RepositoryMonitorExecutor) extractMatchInfo(item *adapters.SearchItem) (string, int) {
	if len(item.Matches) > 0 {
		match := item.Matches[0]
		return match.Fragment, match.LineNumber
	}
	return "", 0
}

func (e *RepositoryMonitorExecutor) extractContext(item *adapters.SearchItem) string {
	if len(item.Matches) > 0 {
		return item.Matches[0].Fragment
	}
	return ""
}

// convertSeverityToScore 将严重程度字符串转换为数值评分
func (e *RepositoryMonitorExecutor) convertSeverityToScore(severity string) int {
	switch severity {
	case "critical":
		return 10
	case "high":
		return 8
	case "medium":
		return 5
	case "low":
		return 3
	case "info":
		return 1
	default:
		return 5 // 默认中等
	}
}
