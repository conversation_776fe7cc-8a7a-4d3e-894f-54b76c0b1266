package search

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/adapters"
	"github.com/godeye/monitor/internal/models"
)

// AccountPool 账号池管理器
type AccountPool struct {
	db       *gorm.DB
	accounts map[string][]*AccountInfo // platform -> accounts
	usage    map[uuid.UUID]*UsageInfo  // account_id -> usage
	mu       sync.RWMutex
}

// AccountInfo 账号信息
type AccountInfo struct {
	Account     *models.PlatformAccount
	AdapterAccount *adapters.Account
	LastUsed    time.Time
	InUse       bool
	ErrorCount  int
}

// UsageInfo 使用信息
type UsageInfo struct {
	RequestCount int
	LastRequest  time.Time
	RateLimit    *adapters.RateLimit
}

// NewAccountPool 创建账号池
func NewAccountPool(db *gorm.DB) *AccountPool {
	return &AccountPool{
		db:       db,
		accounts: make(map[string][]*AccountInfo),
		usage:    make(map[uuid.UUID]*UsageInfo),
	}
}

// LoadAccounts 加载账号
func (ap *AccountPool) LoadAccounts(ctx context.Context) error {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	
	var accounts []models.PlatformAccount
	if err := ap.db.Where("status = ?", "active").Find(&accounts).Error; err != nil {
		return fmt.Errorf("failed to load accounts: %w", err)
	}
	
	// 清空现有账号
	ap.accounts = make(map[string][]*AccountInfo)
	ap.usage = make(map[uuid.UUID]*UsageInfo)
	
	// 按平台分组账号
	for _, account := range accounts {
		// 设置默认的LastUsed时间
		lastUsed := time.Now()
		if account.LastUsedAt != nil {
			lastUsed = *account.LastUsedAt
		}

		accountInfo := &AccountInfo{
			Account: &account,
			AdapterAccount: &adapters.Account{
				ID:           account.ID,
				Platform:     account.Platform,
				AccountType:  account.AccountType,
				Username:     account.Username,
				Token:        account.Token,
				RefreshToken: account.RefreshToken,
				ExpiresAt:    account.ExpiresAt,
			},
			LastUsed:   lastUsed,
			InUse:      false,
			ErrorCount: 0,
		}
		
		ap.accounts[account.Platform] = append(ap.accounts[account.Platform], accountInfo)
		ap.usage[account.ID] = &UsageInfo{
			RequestCount: account.TotalRequests,
			LastRequest:  accountInfo.LastUsed,
			RateLimit: &adapters.RateLimit{
				Limit:     account.RateLimit,
				Remaining: account.RateRemaining,
				ResetAt:   account.RateResetAt,
			},
		}
	}
	
	return nil
}

// GetAvailableAccount 获取可用账号
func (ap *AccountPool) GetAvailableAccount(platform string) (*adapters.Account, error) {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	
	accounts, exists := ap.accounts[platform]
	if !exists || len(accounts) == 0 {
		return nil, fmt.Errorf("no accounts available for platform: %s", platform)
	}
	
	// 查找最佳账号
	var bestAccount *AccountInfo
	var bestScore float64 = -1
	
	now := time.Now()
	
	for _, account := range accounts {
		// 跳过正在使用的账号
		if account.InUse {
			continue
		}
		
		// 跳过有错误的账号（短时间内）
		if account.ErrorCount > 0 && now.Sub(account.LastUsed) < time.Duration(account.ErrorCount)*time.Minute {
			continue
		}
		
		// 检查账号是否过期
		if account.Account.ExpiresAt != nil && now.After(*account.Account.ExpiresAt) {
			continue
		}
		
		usage := ap.usage[account.Account.ID]
		
		// 检查速率限制
		if usage.RateLimit != nil && usage.RateLimit.Remaining <= 0 {
			if usage.RateLimit.ResetAt != nil && now.Before(*usage.RateLimit.ResetAt) {
				continue // 还在限制期内
			}
		}
		
		// 计算账号得分（越高越好）
		score := ap.calculateAccountScore(account, usage, now)
		
		if score > bestScore {
			bestScore = score
			bestAccount = account
		}
	}
	
	if bestAccount == nil {
		return nil, fmt.Errorf("no available accounts for platform: %s", platform)
	}
	
	// 标记账号为使用中
	bestAccount.InUse = true
	bestAccount.LastUsed = now
	
	return bestAccount.AdapterAccount, nil
}

// ReleaseAccount 释放账号
func (ap *AccountPool) ReleaseAccount(accountID uuid.UUID) {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	
	// 查找并释放账号
	for _, accounts := range ap.accounts {
		for _, account := range accounts {
			if account.Account.ID == accountID {
				account.InUse = false
				return
			}
		}
	}
}

// UpdateAccountUsage 更新账号使用情况
func (ap *AccountPool) UpdateAccountUsage(accountID uuid.UUID, rateLimit *adapters.RateLimit, success bool) {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	
	usage, exists := ap.usage[accountID]
	if !exists {
		usage = &UsageInfo{}
		ap.usage[accountID] = usage
	}
	
	usage.RequestCount++
	usage.LastRequest = time.Now()
	
	if rateLimit != nil {
		usage.RateLimit = rateLimit
	}
	
	// 查找账号并更新错误计数
	for _, accounts := range ap.accounts {
		for _, account := range accounts {
			if account.Account.ID == accountID {
				if success {
					account.ErrorCount = 0
				} else {
					account.ErrorCount++
				}
				
				// 更新数据库
				go ap.updateAccountInDB(account, usage)
				return
			}
		}
	}
}

// MarkAccountError 标记账号错误
func (ap *AccountPool) MarkAccountError(accountID uuid.UUID, errorMsg string) {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	
	for _, accounts := range ap.accounts {
		for _, account := range accounts {
			if account.Account.ID == accountID {
				account.ErrorCount++
				account.InUse = false
				
				// 如果错误次数过多，暂时禁用账号
				if account.ErrorCount >= 5 {
					account.Account.Status = "suspended"
					
					// 更新数据库状态
					go func() {
						ap.db.Model(&models.PlatformAccount{}).
							Where("id = ?", accountID).
							Updates(map[string]interface{}{
								"status":     "suspended",
								"last_error": errorMsg,
							})
					}()
				}
				return
			}
		}
	}
}

// GetAccountStats 获取账号统计
func (ap *AccountPool) GetAccountStats(platform string) map[string]interface{} {
	ap.mu.RLock()
	defer ap.mu.RUnlock()
	
	accounts, exists := ap.accounts[platform]
	if !exists {
		return map[string]interface{}{
			"total":     0,
			"active":    0,
			"in_use":    0,
			"available": 0,
			"suspended": 0,
		}
	}
	
	stats := map[string]interface{}{
		"total":     len(accounts),
		"active":    0,
		"in_use":    0,
		"available": 0,
		"suspended": 0,
	}
	
	now := time.Now()
	
	for _, account := range accounts {
		switch account.Account.Status {
		case "active":
			stats["active"] = stats["active"].(int) + 1
			
			if account.InUse {
				stats["in_use"] = stats["in_use"].(int) + 1
			} else {
				// 检查是否真正可用
				if ap.isAccountAvailable(account, now) {
					stats["available"] = stats["available"].(int) + 1
				}
			}
		case "suspended":
			stats["suspended"] = stats["suspended"].(int) + 1
		}
	}
	
	return stats
}

// 辅助方法

func (ap *AccountPool) calculateAccountScore(account *AccountInfo, usage *UsageInfo, now time.Time) float64 {
	score := 100.0
	
	// 根据最后使用时间调整得分（越久未使用得分越高）
	timeSinceLastUse := now.Sub(account.LastUsed).Hours()
	score += timeSinceLastUse * 0.1
	
	// 根据错误次数调整得分
	score -= float64(account.ErrorCount) * 10
	
	// 根据速率限制调整得分
	if usage.RateLimit != nil {
		if usage.RateLimit.Limit > 0 {
			remainingRatio := float64(usage.RateLimit.Remaining) / float64(usage.RateLimit.Limit)
			score += remainingRatio * 50
		}
	}
	
	// 根据请求频率调整得分
	if usage.RequestCount > 0 {
		requestsPerHour := float64(usage.RequestCount) / now.Sub(usage.LastRequest).Hours()
		if requestsPerHour > 100 {
			score -= (requestsPerHour - 100) * 0.1
		}
	}
	
	return score
}

func (ap *AccountPool) isAccountAvailable(account *AccountInfo, now time.Time) bool {
	// 检查是否过期
	if account.Account.ExpiresAt != nil && now.After(*account.Account.ExpiresAt) {
		return false
	}
	
	// 检查错误冷却时间
	if account.ErrorCount > 0 && now.Sub(account.LastUsed) < time.Duration(account.ErrorCount)*time.Minute {
		return false
	}
	
	// 检查速率限制
	usage := ap.usage[account.Account.ID]
	if usage != nil && usage.RateLimit != nil && usage.RateLimit.Remaining <= 0 {
		if usage.RateLimit.ResetAt != nil && now.Before(*usage.RateLimit.ResetAt) {
			return false
		}
	}
	
	return true
}

func (ap *AccountPool) updateAccountInDB(account *AccountInfo, usage *UsageInfo) {
	updates := map[string]interface{}{
		"last_used_at":     account.LastUsed,
		"total_requests":   usage.RequestCount,
		"success_requests": usage.RequestCount - account.ErrorCount,
		"failed_requests":  account.ErrorCount,
	}
	
	if usage.RateLimit != nil {
		updates["rate_limit"] = usage.RateLimit.Limit
		updates["rate_remaining"] = usage.RateLimit.Remaining
		updates["rate_reset_at"] = usage.RateLimit.ResetAt
	}
	
	ap.db.Model(&models.PlatformAccount{}).
		Where("id = ?", account.Account.ID).
		Updates(updates)
}

// RefreshAccounts 刷新账号池
func (ap *AccountPool) RefreshAccounts(ctx context.Context) error {
	return ap.LoadAccounts(ctx)
}

// AddAccount 添加账号
func (ap *AccountPool) AddAccount(account *models.PlatformAccount) error {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	
	accountInfo := &AccountInfo{
		Account: account,
		AdapterAccount: &adapters.Account{
			ID:           account.ID,
			Platform:     account.Platform,
			AccountType:  account.AccountType,
			Username:     account.Username,
			Token:        account.Token,
			RefreshToken: account.RefreshToken,
			ExpiresAt:    account.ExpiresAt,
		},
		LastUsed:   time.Now(),
		InUse:      false,
		ErrorCount: 0,
	}
	
	ap.accounts[account.Platform] = append(ap.accounts[account.Platform], accountInfo)
	ap.usage[account.ID] = &UsageInfo{
		RequestCount: 0,
		LastRequest:  time.Now(),
		RateLimit:    nil,
	}
	
	return nil
}

// RemoveAccount 移除账号
func (ap *AccountPool) RemoveAccount(accountID uuid.UUID) error {
	ap.mu.Lock()
	defer ap.mu.Unlock()
	
	for platform, accounts := range ap.accounts {
		for i, account := range accounts {
			if account.Account.ID == accountID {
				// 从切片中移除
				ap.accounts[platform] = append(accounts[:i], accounts[i+1:]...)
				delete(ap.usage, accountID)
				return nil
			}
		}
	}
	
	return fmt.Errorf("account not found: %s", accountID)
}
