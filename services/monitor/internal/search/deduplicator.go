package search

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/godeye/monitor/internal/adapters"
)

// ResultDeduplicator 结果去重器
type ResultDeduplicator struct {
	seenHashes    map[string]bool
	similarHashes map[string][]*adapters.SearchItem
}

// NewResultDeduplicator 创建结果去重器
func NewResultDeduplicator() *ResultDeduplicator {
	return &ResultDeduplicator{
		seenHashes:    make(map[string]bool),
		similarHashes: make(map[string][]*adapters.SearchItem),
	}
}

// Deduplicate 去重处理
func (d *ResultDeduplicator) Deduplicate(results []*adapters.SearchItem) (int, int) {
	if len(results) == 0 {
		return 0, 0
	}
	
	uniqueResults := 0
	duplicateResults := 0
	
	for _, result := range results {
		// 计算精确哈希
		exactHash := d.calculateExactHash(result)
		
		// 检查是否为精确重复
		if d.seenHashes[exactHash] {
			duplicateResults++
			continue
		}
		
		// 计算相似度哈希
		similarHash := d.calculateSimilarityHash(result)
		
		// 检查是否为相似重复
		if d.isSimilarDuplicate(result, similarHash) {
			duplicateResults++
			continue
		}
		
		// 标记为已见过
		d.seenHashes[exactHash] = true
		d.similarHashes[similarHash] = append(d.similarHashes[similarHash], result)
		uniqueResults++
	}
	
	return uniqueResults, duplicateResults
}

// calculateExactHash 计算精确哈希
func (d *ResultDeduplicator) calculateExactHash(item *adapters.SearchItem) string {
	// 基于仓库全名、文件路径和文件SHA计算精确哈希
	content := fmt.Sprintf("%s:%s:%s", 
		item.Repository.FullName, 
		item.File.Path, 
		item.File.SHA)
	
	hash := sha256.Sum256([]byte(content))
	return hex.EncodeToString(hash[:])
}

// calculateSimilarityHash 计算相似度哈希
func (d *ResultDeduplicator) calculateSimilarityHash(item *adapters.SearchItem) string {
	// 基于仓库名、文件名和匹配内容计算相似度哈希
	repoName := d.normalizeRepoName(item.Repository.Name)
	fileName := d.normalizeFileName(item.File.Name)
	matchContent := d.normalizeMatchContent(item)
	
	content := fmt.Sprintf("%s:%s:%s", repoName, fileName, matchContent)
	
	hash := sha256.Sum256([]byte(content))
	return hex.EncodeToString(hash[:8]) // 使用较短的哈希用于相似度检测
}

// isSimilarDuplicate 检查是否为相似重复
func (d *ResultDeduplicator) isSimilarDuplicate(item *adapters.SearchItem, similarHash string) bool {
	existingItems, exists := d.similarHashes[similarHash]
	if !exists {
		return false
	}
	
	for _, existing := range existingItems {
		if d.calculateSimilarity(item, existing) > 0.8 {
			return true
		}
	}
	
	return false
}

// calculateSimilarity 计算相似度
func (d *ResultDeduplicator) calculateSimilarity(item1, item2 *adapters.SearchItem) float64 {
	// 仓库相似度
	repoSimilarity := d.calculateStringSimilarity(
		d.normalizeRepoName(item1.Repository.Name),
		d.normalizeRepoName(item2.Repository.Name),
	)
	
	// 文件相似度
	fileSimilarity := d.calculateStringSimilarity(
		d.normalizeFileName(item1.File.Name),
		d.normalizeFileName(item2.File.Name),
	)
	
	// 路径相似度
	pathSimilarity := d.calculateStringSimilarity(
		d.normalizeFilePath(item1.File.Path),
		d.normalizeFilePath(item2.File.Path),
	)
	
	// 匹配内容相似度
	contentSimilarity := d.calculateStringSimilarity(
		d.normalizeMatchContent(item1),
		d.normalizeMatchContent(item2),
	)
	
	// 加权平均
	similarity := (repoSimilarity*0.3 + fileSimilarity*0.2 + pathSimilarity*0.2 + contentSimilarity*0.3)
	
	return similarity
}

// calculateStringSimilarity 计算字符串相似度（Jaccard相似度）
func (d *ResultDeduplicator) calculateStringSimilarity(str1, str2 string) float64 {
	if str1 == str2 {
		return 1.0
	}
	
	if len(str1) == 0 || len(str2) == 0 {
		return 0.0
	}
	
	// 转换为n-gram集合
	ngrams1 := d.generateNGrams(str1, 3)
	ngrams2 := d.generateNGrams(str2, 3)
	
	// 计算交集和并集
	intersection := 0
	union := make(map[string]bool)
	
	// 添加第一个字符串的n-grams到并集
	for ngram := range ngrams1 {
		union[ngram] = true
	}
	
	// 添加第二个字符串的n-grams到并集，并计算交集
	for ngram := range ngrams2 {
		if ngrams1[ngram] {
			intersection++
		}
		union[ngram] = true
	}
	
	// Jaccard相似度 = |交集| / |并集|
	if len(union) == 0 {
		return 0.0
	}
	
	return float64(intersection) / float64(len(union))
}

// generateNGrams 生成n-gram
func (d *ResultDeduplicator) generateNGrams(text string, n int) map[string]bool {
	ngrams := make(map[string]bool)
	
	if len(text) < n {
		ngrams[text] = true
		return ngrams
	}
	
	for i := 0; i <= len(text)-n; i++ {
		ngram := text[i : i+n]
		ngrams[ngram] = true
	}
	
	return ngrams
}

// 标准化方法

func (d *ResultDeduplicator) normalizeRepoName(name string) string {
	// 移除常见的前缀和后缀
	name = strings.ToLower(name)
	name = strings.TrimSuffix(name, "-fork")
	name = strings.TrimSuffix(name, "-copy")
	name = strings.TrimSuffix(name, "-clone")
	name = strings.TrimSuffix(name, "_fork")
	name = strings.TrimSuffix(name, "_copy")
	name = strings.TrimSuffix(name, "_clone")
	
	// 移除版本号
	name = d.removeVersionNumbers(name)
	
	return name
}

func (d *ResultDeduplicator) normalizeFileName(name string) string {
	name = strings.ToLower(name)
	
	// 移除常见的测试文件前缀/后缀
	name = strings.TrimPrefix(name, "test_")
	name = strings.TrimPrefix(name, "test-")
	name = strings.TrimSuffix(name, "_test")
	name = strings.TrimSuffix(name, "-test")
	name = strings.TrimSuffix(name, ".test")
	
	// 移除备份文件后缀
	name = strings.TrimSuffix(name, ".bak")
	name = strings.TrimSuffix(name, ".backup")
	name = strings.TrimSuffix(name, ".old")
	
	return name
}

func (d *ResultDeduplicator) normalizeFilePath(path string) string {
	path = strings.ToLower(path)
	
	// 标准化路径分隔符
	path = strings.ReplaceAll(path, "\\", "/")
	
	// 移除常见的目录前缀
	commonPrefixes := []string{
		"src/",
		"lib/",
		"app/",
		"source/",
		"sources/",
		"code/",
	}
	
	for _, prefix := range commonPrefixes {
		if strings.HasPrefix(path, prefix) {
			path = strings.TrimPrefix(path, prefix)
			break
		}
	}
	
	return path
}

func (d *ResultDeduplicator) normalizeMatchContent(item *adapters.SearchItem) string {
	if len(item.Matches) == 0 {
		return ""
	}
	
	content := item.Matches[0].Fragment
	content = strings.ToLower(content)
	
	// 移除多余的空白字符
	content = strings.TrimSpace(content)
	content = d.normalizeWhitespace(content)
	
	// 移除注释符号
	content = strings.ReplaceAll(content, "//", "")
	content = strings.ReplaceAll(content, "#", "")
	content = strings.ReplaceAll(content, "/*", "")
	content = strings.ReplaceAll(content, "*/", "")
	
	// 移除引号
	content = strings.ReplaceAll(content, "\"", "")
	content = strings.ReplaceAll(content, "'", "")
	content = strings.ReplaceAll(content, "`", "")
	
	return strings.TrimSpace(content)
}

func (d *ResultDeduplicator) removeVersionNumbers(text string) string {
	// 简单的版本号移除（v1.0, v2.3.4等）
	patterns := []string{
		"v1", "v2", "v3", "v4", "v5",
		"1.0", "2.0", "3.0",
		"_v1", "_v2", "_v3",
		"-v1", "-v2", "-v3",
	}
	
	for _, pattern := range patterns {
		text = strings.ReplaceAll(text, pattern, "")
	}
	
	return text
}

func (d *ResultDeduplicator) normalizeWhitespace(text string) string {
	// 将多个连续的空白字符替换为单个空格
	words := strings.Fields(text)
	return strings.Join(words, " ")
}

// GetDuplicationStats 获取去重统计
func (d *ResultDeduplicator) GetDuplicationStats() map[string]interface{} {
	return map[string]interface{}{
		"exact_hashes":   len(d.seenHashes),
		"similar_groups": len(d.similarHashes),
	}
}

// Reset 重置去重器
func (d *ResultDeduplicator) Reset() {
	d.seenHashes = make(map[string]bool)
	d.similarHashes = make(map[string][]*adapters.SearchItem)
}

// FindSimilarResults 查找相似结果
func (d *ResultDeduplicator) FindSimilarResults(item *adapters.SearchItem, threshold float64) []*adapters.SearchItem {
	var similarResults []*adapters.SearchItem
	
	similarHash := d.calculateSimilarityHash(item)
	existingItems, exists := d.similarHashes[similarHash]
	
	if !exists {
		return similarResults
	}
	
	for _, existing := range existingItems {
		similarity := d.calculateSimilarity(item, existing)
		if similarity >= threshold {
			similarResults = append(similarResults, existing)
		}
	}
	
	return similarResults
}

// MarkAsDuplicate 标记为重复
func (d *ResultDeduplicator) MarkAsDuplicate(item *adapters.SearchItem) {
	exactHash := d.calculateExactHash(item)
	d.seenHashes[exactHash] = true
}

// IsExactDuplicate 检查是否为精确重复
func (d *ResultDeduplicator) IsExactDuplicate(item *adapters.SearchItem) bool {
	exactHash := d.calculateExactHash(item)
	return d.seenHashes[exactHash]
}
