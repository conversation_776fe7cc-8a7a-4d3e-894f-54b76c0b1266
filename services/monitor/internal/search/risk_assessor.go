package search

import (
	"regexp"
	"strings"

	"github.com/godeye/monitor/internal/adapters"
)

// RiskAssessment 风险评估结果
type RiskAssessment struct {
	RiskLevel  string  `json:"risk_level"`  // "critical", "high", "medium", "low"
	Confidence float64 `json:"confidence"`  // 0.0 - 1.0
	Severity   string  `json:"severity"`    // "critical", "high", "medium", "low", "info"
	Reasons    []string `json:"reasons"`    // 风险原因列表
	Score      int     `json:"score"`      // 风险评分 0-100
}

// RiskAssessor 风险评估器
type RiskAssessor struct {
	patterns map[string]*RiskPattern
}

// RiskPattern 风险模式
type RiskPattern struct {
	Name        string
	Pattern     *regexp.Regexp
	RiskLevel   string
	Severity    string
	Score       int
	Confidence  float64
	Description string
}

// NewRiskAssessor 创建风险评估器
func NewRiskAssessor() *RiskAssessor {
	assessor := &RiskAssessor{
		patterns: make(map[string]*RiskPattern),
	}
	
	assessor.initializePatterns()
	return assessor
}

// AssessRisk 评估风险
func (r *RiskAssessor) AssessRisk(item *adapters.SearchItem) *RiskAssessment {
	assessment := &RiskAssessment{
		RiskLevel:  "low",
		Confidence: 0.0,
		Severity:   "info",
		Reasons:    []string{},
		Score:      0,
	}
	
	// 获取匹配内容
	matchContent := r.extractMatchContent(item)
	if matchContent == "" {
		return assessment
	}
	
	// 检查文件路径风险
	pathRisk := r.assessPathRisk(item.File.Path)
	assessment.Score += pathRisk.Score
	assessment.Reasons = append(assessment.Reasons, pathRisk.Reasons...)
	
	// 检查文件名风险
	fileRisk := r.assessFileNameRisk(item.File.Name)
	assessment.Score += fileRisk.Score
	assessment.Reasons = append(assessment.Reasons, fileRisk.Reasons...)
	
	// 检查仓库风险
	repoRisk := r.assessRepositoryRisk(item.Repository)
	assessment.Score += repoRisk.Score
	assessment.Reasons = append(assessment.Reasons, repoRisk.Reasons...)
	
	// 检查内容模式匹配
	contentRisk := r.assessContentRisk(matchContent)
	assessment.Score += contentRisk.Score
	assessment.Reasons = append(assessment.Reasons, contentRisk.Reasons...)
	
	// 检查上下文风险
	contextRisk := r.assessContextRisk(item)
	assessment.Score += contextRisk.Score
	assessment.Reasons = append(assessment.Reasons, contextRisk.Reasons...)
	
	// 计算最终风险等级
	assessment.RiskLevel = r.calculateRiskLevel(assessment.Score)
	assessment.Severity = r.calculateSeverity(assessment.Score)
	assessment.Confidence = r.calculateConfidence(assessment.Score, len(assessment.Reasons))
	
	return assessment
}

// initializePatterns 初始化风险模式
func (r *RiskAssessor) initializePatterns() {
	patterns := []*RiskPattern{
		// 密钥和令牌
		{
			Name:        "aws_access_key",
			Pattern:     regexp.MustCompile(`(?i)AKIA[0-9A-Z]{16}`),
			RiskLevel:   "critical",
			Severity:    "critical",
			Score:       90,
			Confidence:  0.95,
			Description: "AWS Access Key detected",
		},
		{
			Name:        "aws_secret_key",
			Pattern:     regexp.MustCompile(`(?i)[A-Za-z0-9/+=]{40}`),
			RiskLevel:   "critical",
			Severity:    "critical",
			Score:       85,
			Confidence:  0.8,
			Description: "Potential AWS Secret Key detected",
		},
		{
			Name:        "github_token",
			Pattern:     regexp.MustCompile(`(?i)ghp_[A-Za-z0-9]{36}`),
			RiskLevel:   "critical",
			Severity:    "critical",
			Score:       95,
			Confidence:  0.98,
			Description: "GitHub Personal Access Token detected",
		},
		{
			Name:        "jwt_token",
			Pattern:     regexp.MustCompile(`(?i)eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*`),
			RiskLevel:   "high",
			Severity:    "high",
			Score:       70,
			Confidence:  0.9,
			Description: "JWT Token detected",
		},
		{
			Name:        "private_key",
			Pattern:     regexp.MustCompile(`(?i)-----BEGIN (RSA |EC |DSA )?PRIVATE KEY-----`),
			RiskLevel:   "critical",
			Severity:    "critical",
			Score:       95,
			Confidence:  0.99,
			Description: "Private Key detected",
		},
		{
			Name:        "api_key",
			Pattern:     regexp.MustCompile(`(?i)(api[_-]?key|apikey)\s*[:=]\s*['""]?[A-Za-z0-9_-]{16,}['""]?`),
			RiskLevel:   "high",
			Severity:    "high",
			Score:       75,
			Confidence:  0.85,
			Description: "API Key detected",
		},
		
		// 数据库连接
		{
			Name:        "database_url",
			Pattern:     regexp.MustCompile(`(?i)(mysql|postgresql|mongodb|redis)://[^\s]+`),
			RiskLevel:   "high",
			Severity:    "high",
			Score:       80,
			Confidence:  0.9,
			Description: "Database connection string detected",
		},
		{
			Name:        "connection_string",
			Pattern:     regexp.MustCompile(`(?i)(server|host|hostname)\s*[:=]\s*['""]?[^'"\s]+['""]?.*password`),
			RiskLevel:   "high",
			Severity:    "high",
			Score:       75,
			Confidence:  0.8,
			Description: "Database connection with password detected",
		},
		
		// 密码
		{
			Name:        "hardcoded_password",
			Pattern:     regexp.MustCompile(`(?i)(password|passwd|pwd)\s*[:=]\s*['""][^'"\s]{6,}['""]`),
			RiskLevel:   "medium",
			Severity:    "medium",
			Score:       60,
			Confidence:  0.7,
			Description: "Hardcoded password detected",
		},
		{
			Name:        "default_password",
			Pattern:     regexp.MustCompile(`(?i)(password|passwd|pwd)\s*[:=]\s*['""](admin|password|123456|root|default)['""]`),
			RiskLevel:   "high",
			Severity:    "high",
			Score:       80,
			Confidence:  0.9,
			Description: "Default password detected",
		},
		
		// 邮箱和个人信息
		{
			Name:        "email_address",
			Pattern:     regexp.MustCompile(`[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`),
			RiskLevel:   "low",
			Severity:    "info",
			Score:       20,
			Confidence:  0.8,
			Description: "Email address detected",
		},
		{
			Name:        "phone_number",
			Pattern:     regexp.MustCompile(`(?i)(\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}`),
			RiskLevel:   "low",
			Severity:    "info",
			Score:       15,
			Confidence:  0.6,
			Description: "Phone number detected",
		},
		
		// IP地址和URL
		{
			Name:        "ip_address",
			Pattern:     regexp.MustCompile(`\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b`),
			RiskLevel:   "low",
			Severity:    "info",
			Score:       10,
			Confidence:  0.7,
			Description: "IP address detected",
		},
		{
			Name:        "internal_url",
			Pattern:     regexp.MustCompile(`(?i)https?://(localhost|127\.0\.0\.1|192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[01])\.)`),
			RiskLevel:   "medium",
			Severity:    "medium",
			Score:       40,
			Confidence:  0.8,
			Description: "Internal URL detected",
		},
	}
	
	for _, pattern := range patterns {
		r.patterns[pattern.Name] = pattern
	}
}

// assessPathRisk 评估文件路径风险
func (r *RiskAssessor) assessPathRisk(path string) *RiskAssessment {
	assessment := &RiskAssessment{Score: 0, Reasons: []string{}}
	
	path = strings.ToLower(path)
	
	// 高风险路径
	highRiskPaths := []string{
		".env", "config", "secret", "credential", "key", "password",
		"private", "internal", "admin", "backup", "dump",
	}
	
	for _, riskPath := range highRiskPaths {
		if strings.Contains(path, riskPath) {
			assessment.Score += 30
			assessment.Reasons = append(assessment.Reasons, "High-risk path: "+riskPath)
		}
	}
	
	// 配置文件
	configExtensions := []string{".env", ".config", ".conf", ".ini", ".yaml", ".yml", ".json"}
	for _, ext := range configExtensions {
		if strings.HasSuffix(path, ext) {
			assessment.Score += 20
			assessment.Reasons = append(assessment.Reasons, "Configuration file: "+ext)
		}
	}
	
	return assessment
}

// assessFileNameRisk 评估文件名风险
func (r *RiskAssessor) assessFileNameRisk(fileName string) *RiskAssessment {
	assessment := &RiskAssessment{Score: 0, Reasons: []string{}}
	
	fileName = strings.ToLower(fileName)
	
	// 高风险文件名
	highRiskNames := []string{
		"secret", "credential", "password", "key", "token",
		"private", "backup", "dump", "config", "env",
	}
	
	for _, riskName := range highRiskNames {
		if strings.Contains(fileName, riskName) {
			assessment.Score += 25
			assessment.Reasons = append(assessment.Reasons, "High-risk filename: "+riskName)
		}
	}
	
	return assessment
}

// assessRepositoryRisk 评估仓库风险
func (r *RiskAssessor) assessRepositoryRisk(repo *adapters.Repository) *RiskAssessment {
	assessment := &RiskAssessment{Score: 0, Reasons: []string{}}
	
	// 检查仓库是否为私有
	if !repo.IsPrivate {
		assessment.Score += 40
		assessment.Reasons = append(assessment.Reasons, "Public repository")
	}
	
	// 检查仓库是否为fork
	if repo.IsFork {
		assessment.Score += 20
		assessment.Reasons = append(assessment.Reasons, "Forked repository")
	}
	
	// 检查仓库名称
	repoName := strings.ToLower(repo.Name)
	riskKeywords := []string{"test", "demo", "example", "sample", "backup", "old"}
	for _, keyword := range riskKeywords {
		if strings.Contains(repoName, keyword) {
			assessment.Score += 15
			assessment.Reasons = append(assessment.Reasons, "Risk keyword in repo name: "+keyword)
		}
	}
	
	return assessment
}

// assessContentRisk 评估内容风险
func (r *RiskAssessor) assessContentRisk(content string) *RiskAssessment {
	assessment := &RiskAssessment{Score: 0, Reasons: []string{}}
	
	// 检查所有风险模式
	for _, pattern := range r.patterns {
		if pattern.Pattern.MatchString(content) {
			assessment.Score += pattern.Score
			assessment.Reasons = append(assessment.Reasons, pattern.Description)
		}
	}
	
	return assessment
}

// assessContextRisk 评估上下文风险
func (r *RiskAssessor) assessContextRisk(item *adapters.SearchItem) *RiskAssessment {
	assessment := &RiskAssessment{Score: 0, Reasons: []string{}}
	
	// 检查匹配数量
	if len(item.Matches) > 3 {
		assessment.Score += 20
		assessment.Reasons = append(assessment.Reasons, "Multiple matches in same file")
	}
	
	// 检查文件大小
	if item.File.Size > 1024*1024 { // 大于1MB
		assessment.Score += 10
		assessment.Reasons = append(assessment.Reasons, "Large file size")
	}
	
	return assessment
}

// 辅助方法

func (r *RiskAssessor) extractMatchContent(item *adapters.SearchItem) string {
	if len(item.Matches) == 0 {
		return ""
	}
	
	var content strings.Builder
	for _, match := range item.Matches {
		content.WriteString(match.Fragment)
		content.WriteString(" ")
	}
	
	return content.String()
}

func (r *RiskAssessor) calculateRiskLevel(score int) string {
	switch {
	case score >= 80:
		return "critical"
	case score >= 60:
		return "high"
	case score >= 30:
		return "medium"
	default:
		return "low"
	}
}

func (r *RiskAssessor) calculateSeverity(score int) string {
	switch {
	case score >= 90:
		return "critical"
	case score >= 70:
		return "high"
	case score >= 40:
		return "medium"
	case score >= 20:
		return "low"
	default:
		return "info"
	}
}

func (r *RiskAssessor) calculateConfidence(score int, reasonCount int) float64 {
	// 基础置信度基于评分
	baseConfidence := float64(score) / 100.0
	
	// 根据原因数量调整置信度
	reasonBonus := float64(reasonCount) * 0.1
	if reasonBonus > 0.3 {
		reasonBonus = 0.3
	}
	
	confidence := baseConfidence + reasonBonus
	if confidence > 1.0 {
		confidence = 1.0
	}
	
	return confidence
}

// AddCustomPattern 添加自定义风险模式
func (r *RiskAssessor) AddCustomPattern(name, pattern, riskLevel, severity string, score int, confidence float64) error {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return err
	}
	
	r.patterns[name] = &RiskPattern{
		Name:       name,
		Pattern:    regex,
		RiskLevel:  riskLevel,
		Severity:   severity,
		Score:      score,
		Confidence: confidence,
	}
	
	return nil
}

// GetPatterns 获取所有风险模式
func (r *RiskAssessor) GetPatterns() map[string]*RiskPattern {
	return r.patterns
}

// RemovePattern 移除风险模式
func (r *RiskAssessor) RemovePattern(name string) {
	delete(r.patterns, name)
}
