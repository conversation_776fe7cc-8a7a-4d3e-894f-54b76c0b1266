package search

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"

	"github.com/godeye/monitor/internal/adapters"
	"github.com/godeye/monitor/internal/models"
)

const (
	batchSize = 100 // 批量插入大小
)

// GlobalSearchExecutor 全量搜索执行器
type GlobalSearchExecutor struct {
	db             *gorm.DB
	adapterManager *adapters.AdapterManager
	accountPool    *AccountPool
	deduplicator   *ResultDeduplicator
	riskAssessor   *RiskAssessor
}

// NewGlobalSearchExecutor 创建全量搜索执行器
func NewGlobalSearchExecutor(db *gorm.DB, adapterManager *adapters.AdapterManager, accountPool *AccountPool) *GlobalSearchExecutor {
	return &GlobalSearchExecutor{
		db:             db,
		adapterManager: adapterManager,
		accountPool:    accountPool,
		deduplicator:   NewResultDeduplicator(),
		riskAssessor:   NewRiskAssessor(),
	}
}

// Execute 执行全量搜索
func (e *GlobalSearchExecutor) Execute(ctx context.Context, task *models.GlobalSearchTask, run *models.GlobalSearchRun) error {
	log.Printf("Starting global search execution for task %s", task.ID)
	
	// 解析搜索配置
	platforms, err := e.parsePlatforms(task.Platforms)
	if err != nil {
		return fmt.Errorf("failed to parse platforms: %w", err)
	}
	
	keywords, err := e.parseKeywords(task.Keywords)
	if err != nil {
		return fmt.Errorf("failed to parse keywords: %w", err)
	}
	
	fileTypes, err := e.parseFileTypes(task.FileTypes)
	if err != nil {
		return fmt.Errorf("failed to parse file types: %w", err)
	}
	
	excludeRules, err := e.parseExcludeRules(task.ExcludeRules)
	if err != nil {
		return fmt.Errorf("failed to parse exclude rules: %w", err)
	}
	
	// 初始化统计信息
	platformStats := make(map[string]interface{})
	totalResults := 0
	uniqueResults := 0
	duplicateResults := 0
	
	// 并发搜索各平台
	var wg sync.WaitGroup
	resultsChan := make(chan *PlatformSearchResult, len(platforms))
	errorsChan := make(chan error, len(platforms))
	
	// 限制并发数
	semaphore := make(chan struct{}, task.ConcurrentLimit)
	
	for _, platform := range platforms {
		wg.Add(1)
		go func(platform string) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			result, err := e.searchPlatform(ctx, platform, keywords, fileTypes, excludeRules, task)
			if err != nil {
				log.Printf("Platform %s search failed: %v", platform, err)
				errorsChan <- fmt.Errorf("platform %s: %w", platform, err)
				return
			}
			
			resultsChan <- result
		}(platform)
	}
	
	// 等待所有搜索完成
	go func() {
		wg.Wait()
		close(resultsChan)
		close(errorsChan)
	}()
	
	// 收集结果
	var allResults []*adapters.SearchItem
	var searchErrors []error
	
	for {
		select {
		case result, ok := <-resultsChan:
			if !ok {
				goto ProcessResults
			}
			
			platformStats[result.Platform] = map[string]interface{}{
				"total_queries":    result.TotalQueries,
				"completed_queries": result.CompletedQueries,
				"total_results":    result.TotalResults,
				"error_count":      result.ErrorCount,
				"rate_limit_hits":  result.RateLimitHits,
			}
			
			allResults = append(allResults, result.Results...)
			totalResults += result.TotalResults
			
		case err, ok := <-errorsChan:
			if !ok {
				continue
			}
			searchErrors = append(searchErrors, err)
		}
	}
	
ProcessResults:
	// 去重处理
	uniqueResults, duplicateResults = e.deduplicateResults(allResults)
	
	// 保存搜索结果
	if err := e.saveSearchResults(ctx, run, allResults, task); err != nil {
		return fmt.Errorf("failed to save search results: %w", err)
	}
	
	// 更新运行统计
	updates := map[string]interface{}{
		"platform_stats":    models.JSONB(platformStats),
		"total_results":     totalResults,
		"unique_results":    uniqueResults,
		"duplicate_results": duplicateResults,
	}
	
	if len(searchErrors) > 0 {
		errorDetails := make([]string, len(searchErrors))
		for i, err := range searchErrors {
			errorDetails[i] = err.Error()
		}
		updates["error_details"] = models.JSONB{
			"errors": errorDetails,
		}
	}
	
	if err := e.db.Model(run).Updates(updates).Error; err != nil {
		log.Printf("Failed to update run statistics: %v", err)
	}
	
	log.Printf("Global search completed: %d total results, %d unique, %d duplicates", 
		totalResults, uniqueResults, duplicateResults)
	
	return nil
}

// PlatformSearchResult 平台搜索结果
type PlatformSearchResult struct {
	Platform         string
	TotalQueries     int
	CompletedQueries int
	TotalResults     int
	Results          []*adapters.SearchItem
	ErrorCount       int
	RateLimitHits    int
}

// searchPlatform 搜索单个平台
func (e *GlobalSearchExecutor) searchPlatform(ctx context.Context, platform string, keywords []string, 
	fileTypes []string, excludeRules map[string][]string, task *models.GlobalSearchTask) (*PlatformSearchResult, error) {
	
	adapter, exists := e.adapterManager.GetAdapter(platform)
	if !exists {
		return nil, fmt.Errorf("adapter not found for platform: %s", platform)
	}
	
	result := &PlatformSearchResult{
		Platform: platform,
		Results:  []*adapters.SearchItem{},
	}
	
	// 获取平台账号
	account, err := e.accountPool.GetAvailableAccount(platform)
	if err != nil {
		return nil, fmt.Errorf("no available account for platform %s: %w", platform, err)
	}
	defer e.accountPool.ReleaseAccount(account.ID)
	
	// 构建搜索查询
	queries := e.buildSearchQueries(keywords, fileTypes, excludeRules)
	result.TotalQueries = len(queries)
	
	for _, query := range queries {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return result, ctx.Err()
		default:
		}
		
		// 执行搜索
		searchReq := &adapters.SearchRequest{
			Query:       query,
			Keywords:    keywords,
			SearchType:  task.SearchType,
			FileTypes:   fileTypes,
			ExcludeUsers: excludeRules["users"],
			ExcludeRepos: excludeRules["repos"],
			ExcludeTerms: excludeRules["terms"],
			Page:        1,
			PerPage:     100,
			Sort:        "indexed",
			Order:       "desc",
			Account:     account,
		}
		
		// 分页获取所有结果
		maxPages := task.SearchDepth
		if maxPages <= 0 {
			maxPages = 10
		}
		
		for page := 1; page <= maxPages; page++ {
			searchReq.Page = page
			
			resp, err := adapter.SearchCode(ctx, searchReq)
			if err != nil {
				log.Printf("Search failed for platform %s, query %s, page %d: %v", 
					platform, query, page, err)
				result.ErrorCount++
				
				// 更新账号使用情况
				e.accountPool.UpdateAccountUsage(account.ID, nil, false)
				
				// 如果是速率限制错误，等待一段时间
				if strings.Contains(err.Error(), "rate limit") {
					result.RateLimitHits++
					time.Sleep(1 * time.Minute)
				}
				
				break
			}
			
			// 更新账号使用情况
			e.accountPool.UpdateAccountUsage(account.ID, resp.RateLimit, true)
			
			// 添加结果
			for _, item := range resp.Items {
				result.Results = append(result.Results, &item)
			}
			
			result.TotalResults += len(resp.Items)
			
			// 检查是否还有更多结果
			if !resp.HasMore || len(resp.Items) == 0 {
				break
			}
			
			// 检查是否达到最大结果数
			if result.TotalResults >= task.MaxResults {
				break
			}
			
			// 速率限制保护
			if resp.RateLimit != nil && resp.RateLimit.Remaining <= 1 {
				log.Printf("Rate limit approaching for platform %s, pausing", platform)
				time.Sleep(30 * time.Second)
			}
		}
		
		result.CompletedQueries++
		
		// 查询间隔，避免过于频繁的请求
		time.Sleep(1 * time.Second)
	}
	
	return result, nil
}

// buildSearchQueries 构建搜索查询
func (e *GlobalSearchExecutor) buildSearchQueries(keywords []string, fileTypes []string, excludeRules map[string][]string) []string {
	var queries []string
	
	// 为每个关键词构建查询
	for _, keyword := range keywords {
		query := keyword
		
		// 添加文件类型过滤
		if len(fileTypes) > 0 {
			for _, fileType := range fileTypes {
				extQuery := fmt.Sprintf("%s extension:%s", keyword, fileType)
				queries = append(queries, extQuery)
			}
		} else {
			queries = append(queries, query)
		}
	}
	
	// 组合关键词查询（如果关键词数量适中）
	if len(keywords) > 1 && len(keywords) <= 5 {
		combinedQuery := strings.Join(keywords, " OR ")
		queries = append(queries, combinedQuery)
	}
	
	return queries
}

// saveSearchResults 保存搜索结果
func (e *GlobalSearchExecutor) saveSearchResults(ctx context.Context, run *models.GlobalSearchRun, 
	results []*adapters.SearchItem, task *models.GlobalSearchTask) error {
	
	if len(results) == 0 {
		return nil
	}
	
	// 批量保存结果
	batchSize := 100
	for i := 0; i < len(results); i += batchSize {
		end := i + batchSize
		if end > len(results) {
			end = len(results)
		}
		
		batch := results[i:end]
		if err := e.saveBatchResults(ctx, run, batch, task); err != nil {
			return fmt.Errorf("failed to save batch %d-%d: %w", i, end, err)
		}
	}
	
	return nil
}

// saveBatchResults 批量保存结果
func (e *GlobalSearchExecutor) saveBatchResults(ctx context.Context, run *models.GlobalSearchRun, 
	batch []*adapters.SearchItem, task *models.GlobalSearchTask) error {
	
	var searchResults []models.GlobalSearchResult
	
	for _, item := range batch {
		// 计算内容哈希
		contentHash := e.calculateContentHash(item)
		similarityHash := e.calculateSimilarityHash(item)
		
		// 风险评估
		riskAssessment := e.riskAssessor.AssessRisk(item)
		
		// 提取匹配信息
		matchContent, lineNumber := e.extractMatchInfo(item)
		
		searchResult := models.GlobalSearchResult{
			RunID:        run.ID,
			TaskID:       task.ID,
			UserID:       task.UserID,
			Platform:     item.Repository.Owner.Login, // 需要从适配器获取平台信息
			PlatformID:   item.PlatformID,
			RepoOwner:    item.Repository.Owner.Login,
			RepoName:     item.Repository.Name,
			RepoURL:      item.Repository.HTMLURL,
			RepoFullName: item.Repository.FullName,
			FilePath:     item.File.Path,
			FileName:     item.File.Name,
			FileSize:     item.File.Size,
			FileURL:      item.File.URL,
			MatchType:    "keyword",
			MatchRule:    e.extractMatchRule(item),
			MatchContent: matchContent,
			LineNumber:   lineNumber,
			Context:      e.extractContext(item),
			RiskLevel:    riskAssessment.RiskLevel,
			Confidence:   riskAssessment.Confidence,
			Severity:     e.convertSeverityToScore(riskAssessment.Severity),
			Status:       "new",
			ContentHash:  contentHash,
			SimilarityHash: similarityHash,
		}
		
		searchResults = append(searchResults, searchResult)
	}
	
	// 批量插入
	if err := e.db.CreateInBatches(searchResults, batchSize).Error; err != nil {
		return err
	}
	
	return nil
}

// 辅助方法

func (e *GlobalSearchExecutor) parsePlatforms(platformsJSON models.JSONB) ([]string, error) {
	if platformsJSON == nil {
		return []string{"github"}, nil
	}

	// 尝试两种格式：{"platforms": [...]} 和 {"list": [...]}
	var platforms []interface{}
	var ok bool

	if platforms, ok = platformsJSON["platforms"].([]interface{}); !ok {
		if platforms, ok = platformsJSON["list"].([]interface{}); !ok {
			return nil, fmt.Errorf("invalid platforms format: expected 'platforms' or 'list' field")
		}
	}

	result := make([]string, len(platforms))
	for i, p := range platforms {
		platform, ok := p.(string)
		if !ok {
			return nil, fmt.Errorf("invalid platform type")
		}
		result[i] = platform
	}

	return result, nil
}

func (e *GlobalSearchExecutor) parseKeywords(keywordsJSON models.JSONB) ([]string, error) {
	if keywordsJSON == nil {
		return []string{}, nil
	}

	// 尝试两种格式：{"keywords": [...]} 和 {"list": [...]}
	var keywords []interface{}
	var ok bool

	if keywords, ok = keywordsJSON["keywords"].([]interface{}); !ok {
		if keywords, ok = keywordsJSON["list"].([]interface{}); !ok {
			return nil, fmt.Errorf("invalid keywords format: expected 'keywords' or 'list' field")
		}
	}

	result := make([]string, len(keywords))
	for i, k := range keywords {
		keyword, ok := k.(string)
		if !ok {
			return nil, fmt.Errorf("invalid keyword type")
		}
		result[i] = keyword
	}

	return result, nil
}

func (e *GlobalSearchExecutor) parseFileTypes(fileTypesJSON models.JSONB) ([]string, error) {
	if fileTypesJSON == nil {
		return []string{}, nil
	}
	
	fileTypes, ok := fileTypesJSON["file_types"].([]interface{})
	if !ok {
		return []string{}, nil
	}
	
	result := make([]string, len(fileTypes))
	for i, ft := range fileTypes {
		fileType, ok := ft.(string)
		if !ok {
			continue
		}
		result[i] = fileType
	}
	
	return result, nil
}

func (e *GlobalSearchExecutor) parseExcludeRules(excludeRulesJSON models.JSONB) (map[string][]string, error) {
	result := map[string][]string{
		"users": {},
		"repos": {},
		"terms": {},
	}
	
	if excludeRulesJSON == nil {
		return result, nil
	}
	
	for key, value := range excludeRulesJSON {
		if rules, ok := value.([]interface{}); ok {
			stringRules := make([]string, 0, len(rules))
			for _, rule := range rules {
				if strRule, ok := rule.(string); ok {
					stringRules = append(stringRules, strRule)
				}
			}
			result[key] = stringRules
		}
	}
	
	return result, nil
}

func (e *GlobalSearchExecutor) calculateContentHash(item *adapters.SearchItem) string {
	content := fmt.Sprintf("%s:%s:%s", 
		item.Repository.FullName, 
		item.File.Path, 
		item.File.SHA)
	
	hash := sha256.Sum256([]byte(content))
	return hex.EncodeToString(hash[:])
}

func (e *GlobalSearchExecutor) calculateSimilarityHash(item *adapters.SearchItem) string {
	// 基于文件路径和仓库名计算相似度哈希
	content := fmt.Sprintf("%s:%s", 
		item.Repository.Name, 
		item.File.Name)
	
	hash := sha256.Sum256([]byte(content))
	return hex.EncodeToString(hash[:8]) // 使用较短的哈希用于相似度检测
}

func (e *GlobalSearchExecutor) extractMatchRule(item *adapters.SearchItem) string {
	if len(item.Matches) > 0 {
		return item.Matches[0].Text
	}
	return ""
}

func (e *GlobalSearchExecutor) extractMatchInfo(item *adapters.SearchItem) (string, int) {
	if len(item.Matches) > 0 {
		match := item.Matches[0]
		return match.Fragment, match.LineNumber
	}
	return "", 0
}

func (e *GlobalSearchExecutor) extractContext(item *adapters.SearchItem) string {
	if len(item.Matches) > 0 {
		return item.Matches[0].Fragment
	}
	return ""
}

func (e *GlobalSearchExecutor) deduplicateResults(results []*adapters.SearchItem) (int, int) {
	return e.deduplicator.Deduplicate(results)
}

// convertSeverityToScore 将严重程度字符串转换为数值评分
func (e *GlobalSearchExecutor) convertSeverityToScore(severity string) int {
	switch severity {
	case "critical":
		return 10
	case "high":
		return 8
	case "medium":
		return 5
	case "low":
		return 3
	case "info":
		return 1
	default:
		return 5 // 默认中等
	}
}
