package scanner

import (
	"bufio"
	"context"
	"crypto/sha256"
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/google/go-github/v56/github"
	githubclient "github.com/godeye/monitor/internal/github"
	"github.com/godeye/monitor/internal/models"
)



// Engine 扫描引擎
type Engine struct {
	githubClient   *githubclient.Client
	keywords       []Keyword
	patterns       []Pattern
	excludeRules   []ExcludeRule
	config         Config
	mu             sync.RWMutex
}

// Config 扫描引擎配置
type Config struct {
	MaxFileSize     int64
	MaxFiles        int
	ScanTimeout     time.Duration
	WorkerCount     int
	BatchSize       int
	ExcludePatterns []string
	IncludeExts     []string
}

// Keyword 关键词规则
type Keyword struct {
	ID          string
	Value       string
	Category    string
	RiskLevel   string
	Description string
	CaseSensitive bool
}

// Pattern 正则表达式规则
type Pattern struct {
	ID          string
	Regex       *regexp.Regexp
	Category    string
	RiskLevel   string
	Description string
}

// ExcludeRule 排除规则
type ExcludeRule struct {
	Type    string // path, extension, pattern
	Value   string
	Pattern *regexp.Regexp
}

// ScanRequest 扫描请求
type ScanRequest struct {
	TaskID      string
	UserID      string
	TargetType  string // repository, organization, user
	TargetValue string
	Keywords    []string
	Patterns    []string
	ExcludeRules []string
	ScanDepth   int
	MaxFileSize int64
	IncludeForks bool
}

// ScanResult 扫描结果
type ScanResult struct {
	TaskID       string
	RunID        string
	RepoOwner    string
	RepoName     string
	RepoURL      string
	FilePath     string
	FileName     string
	FileSize     int64
	FileURL      string
	MatchType    string
	MatchRule    string
	MatchContent string
	LineNumber   int
	ColumnNumber int
	Context      string
	RiskLevel    string
	Confidence   float64
	ContentHash  string
}

// ScanStats 扫描统计
type ScanStats struct {
	TotalRepos   int
	ScannedRepos int
	TotalFiles   int
	ScannedFiles int
	MatchedFiles int
	TotalMatches int
	StartTime    time.Time
	EndTime      time.Time
}

// NewEngine 创建扫描引擎
func NewEngine(githubClient *githubclient.Client, config Config) *Engine {
	return &Engine{
		githubClient: githubClient,
		config:       config,
		keywords:     make([]Keyword, 0),
		patterns:     make([]Pattern, 0),
		excludeRules: make([]ExcludeRule, 0),
	}
}

// LoadKeywords 加载关键词规则
func (e *Engine) LoadKeywords(keywords []models.ScanKeyword) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.keywords = make([]Keyword, 0, len(keywords))
	for _, kw := range keywords {
		e.keywords = append(e.keywords, Keyword{
			ID:            kw.ID.String(),
			Value:         kw.Keyword,
			Category:      kw.Category,
			RiskLevel:     kw.RiskLevel,
			Description:   kw.Description,
			CaseSensitive: true, // 默认区分大小写
		})
	}

	return nil
}

// LoadPatterns 加载正则表达式规则
func (e *Engine) LoadPatterns(keywords []models.ScanKeyword) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.patterns = make([]Pattern, 0)
	for _, kw := range keywords {
		if kw.Pattern != "" {
			regex, err := regexp.Compile(kw.Pattern)
			if err != nil {
				continue // 跳过无效的正则表达式
			}
			
			e.patterns = append(e.patterns, Pattern{
				ID:          kw.ID.String(),
				Regex:       regex,
				Category:    kw.Category,
				RiskLevel:   kw.RiskLevel,
				Description: kw.Description,
			})
		}
	}

	return nil
}

// LoadExcludeRules 加载排除规则
func (e *Engine) LoadExcludeRules(rules []string) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.excludeRules = make([]ExcludeRule, 0, len(rules))
	for _, rule := range rules {
		if strings.HasPrefix(rule, "regex:") {
			pattern := strings.TrimPrefix(rule, "regex:")
			regex, err := regexp.Compile(pattern)
			if err != nil {
				continue
			}
			e.excludeRules = append(e.excludeRules, ExcludeRule{
				Type:    "pattern",
				Value:   pattern,
				Pattern: regex,
			})
		} else if strings.Contains(rule, "*") {
			// 通配符模式
			pattern := strings.ReplaceAll(rule, "*", ".*")
			regex, err := regexp.Compile(pattern)
			if err != nil {
				continue
			}
			e.excludeRules = append(e.excludeRules, ExcludeRule{
				Type:    "pattern",
				Value:   rule,
				Pattern: regex,
			})
		} else {
			// 精确匹配
			e.excludeRules = append(e.excludeRules, ExcludeRule{
				Type:  "path",
				Value: rule,
			})
		}
	}

	return nil
}

// Scan 执行扫描
func (e *Engine) Scan(ctx context.Context, request ScanRequest) (*ScanStats, <-chan ScanResult, <-chan error) {
	resultChan := make(chan ScanResult, 100)
	errorChan := make(chan error, 10)
	
	stats := &ScanStats{
		StartTime: time.Now(),
	}

	go func() {
		defer close(resultChan)
		defer close(errorChan)
		
		err := e.performScan(ctx, request, stats, resultChan, errorChan)
		if err != nil {
			errorChan <- err
		}
		
		stats.EndTime = time.Now()
	}()

	return stats, resultChan, errorChan
}

// performScan 执行实际扫描
func (e *Engine) performScan(ctx context.Context, request ScanRequest, stats *ScanStats, resultChan chan<- ScanResult, errorChan chan<- error) error {
	// 根据目标类型获取仓库列表
	repos, err := e.getRepositories(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to get repositories: %w", err)
	}

	stats.TotalRepos = len(repos)

	// 创建工作池
	semaphore := make(chan struct{}, e.config.WorkerCount)
	var wg sync.WaitGroup

	for _, repo := range repos {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case semaphore <- struct{}{}:
			wg.Add(1)
			go func(repo github.Repository) {
				defer func() {
					<-semaphore
					wg.Done()
				}()

				err := e.scanRepository(ctx, repo, request, stats, resultChan)
				if err != nil {
					select {
					case errorChan <- err:
					case <-ctx.Done():
					}
				}
			}(repo)
		}
	}

	wg.Wait()
	return nil
}

// getRepositories 获取要扫描的仓库列表
func (e *Engine) getRepositories(ctx context.Context, request ScanRequest) ([]github.Repository, error) {
	var repos []github.Repository

	switch request.TargetType {
	case "repository":
		// 单个仓库
		parts := strings.Split(request.TargetValue, "/")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid repository format: %s", request.TargetValue)
		}

		repo, err := e.githubClient.GetRepository(ctx, parts[0], parts[1])
		if err != nil {
			return nil, err
		}

		repos = append(repos, *repo)

	case "organization", "user":
		// 组织或用户的所有仓库
		githubRepos, err := e.githubClient.ListRepositories(ctx, request.TargetValue, nil)
		if err != nil {
			return nil, err
		}

		for _, repo := range githubRepos {
			// 检查是否包含fork
			if !request.IncludeForks && repo.GetFork() {
				continue
			}

			repos = append(repos, *repo)
		}

	default:
		return nil, fmt.Errorf("unsupported target type: %s", request.TargetType)
	}

	return repos, nil
}

// scanRepository 扫描单个仓库
func (e *Engine) scanRepository(ctx context.Context, repo github.Repository, request ScanRequest, stats *ScanStats, resultChan chan<- ScanResult) error {
	// 获取仓库文件列表
	files, err := e.githubClient.ListRepositoryFiles(ctx, repo.Owner.GetLogin(), repo.GetName(), "", request.ScanDepth)
	if err != nil {
		return fmt.Errorf("failed to list files for %s: %w", repo.GetFullName(), err)
	}

	stats.TotalFiles += len(files)
	stats.ScannedRepos++

	// 过滤文件
	filteredFiles := e.filterFiles(files, request.MaxFileSize)
	
	// 扫描文件
	for _, file := range filteredFiles {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		results, err := e.scanFile(ctx, repo, file, request)
		if err != nil {
			continue // 跳过错误文件
		}

		stats.ScannedFiles++
		if len(results) > 0 {
			stats.MatchedFiles++
			stats.TotalMatches += len(results)
		}

		for _, result := range results {
			select {
			case resultChan <- result:
			case <-ctx.Done():
				return ctx.Err()
			}
		}
	}

	return nil
}

// filterFiles 过滤文件
func (e *Engine) filterFiles(files []githubclient.FileInfo, maxFileSize int64) []githubclient.FileInfo {
	var filtered []githubclient.FileInfo

	for _, file := range files {
		// 检查文件大小
		if maxFileSize > 0 && int64(file.Size) > maxFileSize {
			continue
		}

		// 检查文件扩展名
		if len(e.config.IncludeExts) > 0 {
			ext := strings.ToLower(filepath.Ext(file.Name))
			found := false
			for _, allowedExt := range e.config.IncludeExts {
				if ext == strings.ToLower(allowedExt) {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		// 检查排除规则
		if e.shouldExcludeFile(file.Path) {
			continue
		}

		filtered = append(filtered, file)
	}

	return filtered
}

// shouldExcludeFile 检查是否应该排除文件
func (e *Engine) shouldExcludeFile(filePath string) bool {
	e.mu.RLock()
	defer e.mu.RUnlock()

	for _, rule := range e.excludeRules {
		switch rule.Type {
		case "path":
			if strings.Contains(filePath, rule.Value) {
				return true
			}
		case "pattern":
			if rule.Pattern.MatchString(filePath) {
				return true
			}
		}
	}

	return false
}

// scanFile 扫描单个文件
func (e *Engine) scanFile(ctx context.Context, repo github.Repository, file githubclient.FileInfo, request ScanRequest) ([]ScanResult, error) {
	// 获取文件内容
	content, err := e.githubClient.GetFileContent(ctx, repo.Owner.GetLogin(), repo.GetName(), file.Path, nil)
	if err != nil {
		return nil, err
	}

	var results []ScanResult

	// 按行扫描
	scanner := bufio.NewScanner(strings.NewReader(content))
	lineNumber := 0

	for scanner.Scan() {
		lineNumber++
		line := scanner.Text()

		// 扫描关键词
		keywordResults := e.scanLineForKeywords(line, lineNumber, repo, file, request)
		results = append(results, keywordResults...)

		// 扫描正则表达式
		patternResults := e.scanLineForPatterns(line, lineNumber, repo, file, request)
		results = append(results, patternResults...)
	}

	return results, nil
}

// scanLineForKeywords 扫描行中的关键词
func (e *Engine) scanLineForKeywords(line string, lineNumber int, repo github.Repository, file githubclient.FileInfo, request ScanRequest) []ScanResult {
	e.mu.RLock()
	defer e.mu.RUnlock()

	var results []ScanResult

	for _, keyword := range e.keywords {
		searchLine := line
		searchKeyword := keyword.Value

		if !keyword.CaseSensitive {
			searchLine = strings.ToLower(line)
			searchKeyword = strings.ToLower(keyword.Value)
		}

		if strings.Contains(searchLine, searchKeyword) {
			// 计算列位置
			columnNumber := strings.Index(searchLine, searchKeyword) + 1

			// 生成内容哈希
			contentHash := e.generateContentHash(repo.GetFullName(), file.Path, lineNumber, keyword.Value)

			result := ScanResult{
				TaskID:       request.TaskID,
				RunID:        request.UserID, // 临时使用UserID作为RunID
				RepoOwner:    repo.Owner.GetLogin(),
				RepoName:     repo.GetName(),
				RepoURL:      repo.GetHTMLURL(),
				FilePath:     file.Path,
				FileName:     file.Name,
				FileSize:     int64(file.Size),
				FileURL:      file.DownloadURL,
				MatchType:    "keyword",
				MatchRule:    keyword.Value,
				MatchContent: strings.TrimSpace(line),
				LineNumber:   lineNumber,
				ColumnNumber: columnNumber,
				Context:      e.getContext(line, 50),
				RiskLevel:    keyword.RiskLevel,
				Confidence:   0.8, // 关键词匹配置信度
				ContentHash:  contentHash,
			}

			results = append(results, result)
		}
	}

	return results
}

// scanLineForPatterns 扫描行中的正则表达式
func (e *Engine) scanLineForPatterns(line string, lineNumber int, repo github.Repository, file githubclient.FileInfo, request ScanRequest) []ScanResult {
	e.mu.RLock()
	defer e.mu.RUnlock()

	var results []ScanResult

	for _, pattern := range e.patterns {
		matches := pattern.Regex.FindAllStringSubmatch(line, -1)
		for _, match := range matches {
			if len(match) > 0 {
				matchedText := match[0]
				columnNumber := strings.Index(line, matchedText) + 1

				// 生成内容哈希
				contentHash := e.generateContentHash(repo.GetFullName(), file.Path, lineNumber, matchedText)

				result := ScanResult{
					TaskID:       request.TaskID,
					RunID:        request.UserID,
					RepoOwner:    repo.Owner.GetLogin(),
					RepoName:     repo.GetName(),
					RepoURL:      repo.GetHTMLURL(),
					FilePath:     file.Path,
					FileName:     file.Name,
					FileSize:     int64(file.Size),
					FileURL:      file.DownloadURL,
					MatchType:    "pattern",
					MatchRule:    pattern.Regex.String(),
					MatchContent: strings.TrimSpace(line),
					LineNumber:   lineNumber,
					ColumnNumber: columnNumber,
					Context:      e.getContext(line, 50),
					RiskLevel:    pattern.RiskLevel,
					Confidence:   0.9, // 正则表达式匹配置信度更高
					ContentHash:  contentHash,
				}

				results = append(results, result)
			}
		}
	}

	return results
}

// getContext 获取上下文内容
func (e *Engine) getContext(line string, maxLength int) string {
	if len(line) <= maxLength {
		return line
	}
	return line[:maxLength] + "..."
}

// generateContentHash 生成内容哈希用于去重
func (e *Engine) generateContentHash(repoFullName, filePath string, lineNumber int, matchContent string) string {
	data := fmt.Sprintf("%s:%s:%d:%s", repoFullName, filePath, lineNumber, matchContent)
	hash := sha256.Sum256([]byte(data))
	return fmt.Sprintf("%x", hash)
}
