package services

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/godeye/auth/internal/config"
	"github.com/godeye/auth/internal/models"
	"github.com/godeye/auth/pkg/errors"
	"github.com/godeye/auth/pkg/logger"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct {
	db      *gorm.DB
	redis   *redis.Client
	jwtCfg  config.JWTConfig
	logger  logger.Logger
}

// NewAuthService 创建认证服务
func NewAuthService(db *gorm.DB, redis *redis.Client, jwtCfg config.JWTConfig, logger logger.Logger) *AuthService {
	return &AuthService{
		db:     db,
		redis:  redis,
		jwtCfg: jwtCfg,
		logger: logger,
	}
}

// Login 用户登录
func (s *AuthService) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
	// 获取用户
	var user models.User
	if err := s.db.Preload("UserRoles.Role").Where("email = ?", req.Email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewValidationError("邮箱或密码错误")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 检查用户状态
	if !user.IsActive {
		return nil, errors.NewValidationError("账号已被禁用")
	}

	// 检查账号是否被锁定
	if user.IsLocked() {
		return nil, errors.NewValidationError("账号已被锁定，请稍后再试")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		// 增加登录失败次数
		s.incrementLoginAttempts(ctx, user.ID)
		return nil, errors.NewValidationError("邮箱或密码错误")
	}

	// 重置登录失败次数
	s.resetLoginAttempts(ctx, user.ID)

	// 生成 Token
	accessToken, err := s.generateAccessToken(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, err := s.generateRefreshToken(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// 创建会话
	session, err := s.createSession(ctx, &user, refreshToken, req.DeviceInfo, req.IPAddress, req.UserAgent)
	if err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	// 更新最后登录时间
	now := time.Now()
	s.db.Model(&user).Update("last_login_at", now)

	// 获取用户权限
	permissions := s.getUserPermissions(&user)

	s.logger.Info("User logged in successfully", "user_id", user.ID, "email", user.Email)

	return &LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int(s.jwtCfg.AccessTokenDuration.Seconds()),
		User: UserInfo{
			ID:          user.ID,
			Email:       user.Email,
			Username:    user.Username,
			FirstName:   user.FirstName,
			LastName:    user.LastName,
			AvatarURL:   user.AvatarURL,
			Role:        user.Role,
			Permissions: permissions,
			IsVerified:  user.IsVerified,
		},
		SessionID: session.ID,
	}, nil
}

// RefreshToken 刷新 Token
func (s *AuthService) RefreshToken(ctx context.Context, refreshToken string) (*RefreshTokenResponse, error) {
	// 验证 refresh token
	claims, err := s.validateRefreshToken(refreshToken)
	if err != nil {
		return nil, errors.NewUnauthorizedError("无效的刷新令牌")
	}

	userID, err := uuid.Parse(claims.Subject)
	if err != nil {
		return nil, errors.NewUnauthorizedError("无效的用户ID")
	}

	// 检查会话是否存在且有效
	tokenHash := s.hashToken(refreshToken)
	var session models.UserSession
	if err := s.db.Where("user_id = ? AND token_hash = ? AND is_active = ? AND expires_at > ?",
		userID, tokenHash, true, time.Now()).First(&session).Error; err != nil {
		return nil, errors.NewUnauthorizedError("会话已过期或无效")
	}

	// 获取用户信息
	var user models.User
	if err := s.db.Preload("UserRoles.Role").Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, errors.NewUnauthorizedError("用户不存在")
	}

	// 检查用户状态
	if !user.IsActive {
		return nil, errors.NewUnauthorizedError("账号已被禁用")
	}

	// 生成新的 access token
	accessToken, err := s.generateAccessToken(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// 更新会话最后访问时间
	s.db.Model(&session).Update("last_accessed_at", time.Now())

	s.logger.Info("Token refreshed successfully", "user_id", userID)

	return &RefreshTokenResponse{
		AccessToken: accessToken,
		ExpiresIn:   int(s.jwtCfg.AccessTokenDuration.Seconds()),
	}, nil
}

// Logout 用户登出
func (s *AuthService) Logout(ctx context.Context, userID uuid.UUID, sessionID *uuid.UUID) error {
	query := s.db.Model(&models.UserSession{}).Where("user_id = ? AND is_active = ?", userID, true)
	
	if sessionID != nil {
		// 登出指定会话
		query = query.Where("id = ?", *sessionID)
	}

	// 将会话标记为非活跃
	if err := query.Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to logout: %w", err)
	}

	s.logger.Info("User logged out successfully", "user_id", userID, "session_id", sessionID)
	return nil
}

// ValidateToken 验证 access token
func (s *AuthService) ValidateToken(tokenString string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.jwtCfg.SecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*TokenClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// GetUserSessions 获取用户会话列表
func (s *AuthService) GetUserSessions(ctx context.Context, userID uuid.UUID) ([]models.UserSession, error) {
	var sessions []models.UserSession
	if err := s.db.Where("user_id = ? AND is_active = ?", userID, true).
		Order("last_accessed_at DESC").
		Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get user sessions: %w", err)
	}

	return sessions, nil
}

// RevokeSession 撤销会话
func (s *AuthService) RevokeSession(ctx context.Context, userID, sessionID uuid.UUID) error {
	if err := s.db.Model(&models.UserSession{}).
		Where("id = ? AND user_id = ?", sessionID, userID).
		Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to revoke session: %w", err)
	}

	s.logger.Info("Session revoked successfully", "user_id", userID, "session_id", sessionID)
	return nil
}

// generateAccessToken 生成访问令牌
func (s *AuthService) generateAccessToken(user *models.User) (string, error) {
	now := time.Now()
	claims := &TokenClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Subject:   user.ID.String(),
			Issuer:    s.jwtCfg.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(s.jwtCfg.AccessTokenDuration)),
		},
		UserID:      user.ID,
		Email:       user.Email,
		Username:    user.Username,
		Role:        user.Role,
		Permissions: s.getUserPermissions(user),
		TokenType:   "access",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.jwtCfg.SecretKey))
}

// generateRefreshToken 生成刷新令牌
func (s *AuthService) generateRefreshToken(user *models.User) (string, error) {
	now := time.Now()
	claims := &jwt.RegisteredClaims{
		Subject:   user.ID.String(),
		Issuer:    s.jwtCfg.Issuer,
		IssuedAt:  jwt.NewNumericDate(now),
		ExpiresAt: jwt.NewNumericDate(now.Add(s.jwtCfg.RefreshTokenDuration)),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.jwtCfg.SecretKey))
}

// validateRefreshToken 验证刷新令牌
func (s *AuthService) validateRefreshToken(tokenString string) (*jwt.RegisteredClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.jwtCfg.SecretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*jwt.RegisteredClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid refresh token")
}

// createSession 创建用户会话
func (s *AuthService) createSession(ctx context.Context, user *models.User, refreshToken string, deviceInfo models.JSONMap, ipAddress, userAgent string) (*models.UserSession, error) {
	tokenHash := s.hashToken(refreshToken)
	
	session := &models.UserSession{
		UserID:         user.ID,
		TokenHash:      tokenHash,
		DeviceInfo:     deviceInfo,
		IPAddress:      ipAddress,
		UserAgent:      userAgent,
		IsActive:       true,
		ExpiresAt:      time.Now().Add(s.jwtCfg.RefreshTokenDuration),
		LastAccessedAt: time.Now(),
	}

	if err := s.db.Create(session).Error; err != nil {
		return nil, err
	}

	return session, nil
}

// hashToken 对令牌进行哈希
func (s *AuthService) hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// getUserPermissions 获取用户权限
func (s *AuthService) getUserPermissions(user *models.User) []string {
	var permissions []string
	
	for _, userRole := range user.UserRoles {
		if userRole.ExpiresAt != nil && time.Now().After(*userRole.ExpiresAt) {
			continue // 角色已过期
		}
		
		role := userRole.Role
		if all, ok := role.Permissions["all"].(bool); ok && all {
			return []string{"all"}
		}
		
		// 提取具体权限
		for resource, perms := range role.Permissions {
			switch p := perms.(type) {
			case []interface{}:
				for _, perm := range p {
					if permStr, ok := perm.(string); ok {
						permissions = append(permissions, fmt.Sprintf("%s:%s", resource, permStr))
					}
				}
			case []string:
				for _, perm := range p {
					permissions = append(permissions, fmt.Sprintf("%s:%s", resource, perm))
				}
			case string:
				permissions = append(permissions, fmt.Sprintf("%s:%s", resource, p))
			}
		}
	}
	
	return permissions
}

// incrementLoginAttempts 增加登录失败次数
func (s *AuthService) incrementLoginAttempts(ctx context.Context, userID uuid.UUID) {
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return
	}

	user.LoginAttempts++
	
	// 如果失败次数达到限制，锁定账号
	if user.LoginAttempts >= 5 {
		lockUntil := time.Now().Add(30 * time.Minute)
		user.LockedUntil = &lockUntil
	}

	s.db.Save(&user)
}

// resetLoginAttempts 重置登录失败次数
func (s *AuthService) resetLoginAttempts(ctx context.Context, userID uuid.UUID) {
	s.db.Model(&models.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"login_attempts": 0,
		"locked_until":   nil,
	})
}

// 请求和响应结构体
type LoginRequest struct {
	Email      string          `json:"email" validate:"required,email"`
	Password   string          `json:"password" validate:"required"`
	RememberMe bool            `json:"remember_me"`
	DeviceInfo models.JSONMap  `json:"device_info"`
	IPAddress  string          `json:"ip_address"`
	UserAgent  string          `json:"user_agent"`
}

type LoginResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresIn    int       `json:"expires_in"`
	User         UserInfo  `json:"user"`
	SessionID    uuid.UUID `json:"session_id"`
}

type RefreshTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

type UserInfo struct {
	ID          uuid.UUID `json:"id"`
	Email       string    `json:"email"`
	Username    string    `json:"username"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	AvatarURL   string    `json:"avatar_url"`
	Role        string    `json:"role"`
	Permissions []string  `json:"permissions"`
	IsVerified  bool      `json:"is_verified"`
}

type TokenClaims struct {
	jwt.RegisteredClaims
	UserID      uuid.UUID `json:"user_id"`
	Email       string    `json:"email"`
	Username    string    `json:"username"`
	Role        string    `json:"role"`
	Permissions []string  `json:"permissions"`
	TokenType   string    `json:"token_type"`
}
