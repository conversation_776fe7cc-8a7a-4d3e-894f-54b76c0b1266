package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/godeye/auth/internal/models"
	"github.com/godeye/auth/pkg/errors"
	"github.com/godeye/auth/pkg/logger"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger logger.Logger
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB, redis *redis.Client, logger logger.Logger) *UserService {
	return &UserService{
		db:     db,
		redis:  redis,
		logger: logger,
	}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*models.User, error) {
	// 检查邮箱是否已存在
	var existingUser models.User
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.NewValidationError("邮箱已被注册")
	}

	// 检查用户名是否已存在
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.NewValidationError("用户名已被使用")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 创建用户
	user := &models.User{
		Email:        req.Email,
		Username:     req.Username,
		PasswordHash: string(hashedPassword),
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		Role:         "user", // 默认角色
		IsActive:     true,
		IsVerified:   false,
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// 分配默认角色
	if err := s.assignDefaultRole(ctx, user.ID); err != nil {
		s.logger.Error("Failed to assign default role", "user_id", user.ID, "error", err)
	}

	// 清除缓存
	s.clearUserCache(ctx, user.ID)

	s.logger.Info("User created successfully", "user_id", user.ID, "email", user.Email)
	return user, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(ctx context.Context, userID uuid.UUID) (*models.User, error) {
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("user:%s", userID.String())
	cached, err := s.redis.Get(ctx, cacheKey).Result()
	if err == nil {
		var user models.User
		if err := json.Unmarshal([]byte(cached), &user); err == nil {
			return &user, nil
		}
	}

	// 从数据库获取
	var user models.User
	if err := s.db.Preload("UserRoles.Role").Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("用户不存在")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 缓存用户信息
	userJSON, _ := json.Marshal(user)
	s.redis.Set(ctx, cacheKey, userJSON, 10*time.Minute)

	return &user, nil
}

// GetUserByEmail 根据邮箱获取用户
func (s *UserService) GetUserByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("UserRoles.Role").Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("用户不存在")
		}
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	return &user, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(ctx context.Context, userID uuid.UUID, req *UpdateUserRequest) (*models.User, error) {
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.FirstName != nil {
		user.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		user.LastName = *req.LastName
	}
	if req.AvatarURL != nil {
		user.AvatarURL = *req.AvatarURL
	}

	if err := s.db.Save(user).Error; err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// 清除缓存
	s.clearUserCache(ctx, userID)

	s.logger.Info("User updated successfully", "user_id", userID)
	return user, nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(ctx context.Context, userID uuid.UUID, oldPassword, newPassword string) error {
	user, err := s.GetUserByID(ctx, userID)
	if err != nil {
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(oldPassword)); err != nil {
		return errors.NewValidationError("当前密码不正确")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// 更新密码
	if err := s.db.Model(user).Update("password_hash", string(hashedPassword)).Error; err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	// 清除缓存
	s.clearUserCache(ctx, userID)

	s.logger.Info("Password changed successfully", "user_id", userID)
	return nil
}

// ListUsers 获取用户列表
func (s *UserService) ListUsers(ctx context.Context, req *ListUsersRequest) (*ListUsersResponse, error) {
	var users []models.User
	var total int64

	query := s.db.Model(&models.User{})

	// 搜索条件
	if req.Search != "" {
		query = query.Where("email ILIKE ? OR username ILIKE ? OR first_name ILIKE ? OR last_name ILIKE ?",
			"%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// 角色过滤
	if req.Role != "" {
		query = query.Where("role = ?", req.Role)
	}

	// 状态过滤
	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Limit
	if err := query.Preload("UserRoles.Role").
		Offset(offset).
		Limit(req.Limit).
		Order("created_at DESC").
		Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	return &ListUsersResponse{
		Users: users,
		Pagination: PaginationResponse{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
		},
	}, nil
}

// AssignRole 分配角色
func (s *UserService) AssignRole(ctx context.Context, userID, roleID uuid.UUID, assignedBy uuid.UUID) error {
	// 检查用户是否存在
	if _, err := s.GetUserByID(ctx, userID); err != nil {
		return err
	}

	// 检查角色是否存在
	var role models.Role
	if err := s.db.Where("id = ?", roleID).First(&role).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.NewNotFoundError("角色不存在")
		}
		return fmt.Errorf("failed to get role: %w", err)
	}

	// 检查是否已分配该角色
	var existingUserRole models.UserRole
	if err := s.db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&existingUserRole).Error; err == nil {
		return errors.NewValidationError("用户已拥有该角色")
	}

	// 创建用户角色关联
	userRole := &models.UserRole{
		UserID:     userID,
		RoleID:     roleID,
		AssignedBy: &assignedBy,
		AssignedAt: time.Now(),
	}

	if err := s.db.Create(userRole).Error; err != nil {
		return fmt.Errorf("failed to assign role: %w", err)
	}

	// 清除缓存
	s.clearUserCache(ctx, userID)

	s.logger.Info("Role assigned successfully", "user_id", userID, "role_id", roleID, "assigned_by", assignedBy)
	return nil
}

// RevokeRole 撤销角色
func (s *UserService) RevokeRole(ctx context.Context, userID, roleID uuid.UUID) error {
	if err := s.db.Where("user_id = ? AND role_id = ?", userID, roleID).Delete(&models.UserRole{}).Error; err != nil {
		return fmt.Errorf("failed to revoke role: %w", err)
	}

	// 清除缓存
	s.clearUserCache(ctx, userID)

	s.logger.Info("Role revoked successfully", "user_id", userID, "role_id", roleID)
	return nil
}

// assignDefaultRole 分配默认角色
func (s *UserService) assignDefaultRole(ctx context.Context, userID uuid.UUID) error {
	var defaultRole models.Role
	if err := s.db.Where("name = ?", "user").First(&defaultRole).Error; err != nil {
		return fmt.Errorf("failed to get default role: %w", err)
	}

	userRole := &models.UserRole{
		UserID:     userID,
		RoleID:     defaultRole.ID,
		AssignedAt: time.Now(),
	}

	return s.db.Create(userRole).Error
}

// clearUserCache 清除用户缓存
func (s *UserService) clearUserCache(ctx context.Context, userID uuid.UUID) {
	cacheKey := fmt.Sprintf("user:%s", userID.String())
	s.redis.Del(ctx, cacheKey)
}

// 请求和响应结构体
type CreateUserRequest struct {
	Email     string `json:"email" validate:"required,email"`
	Username  string `json:"username" validate:"required,min=3,max=50"`
	Password  string `json:"password" validate:"required,min=8"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

type UpdateUserRequest struct {
	FirstName *string `json:"first_name"`
	LastName  *string `json:"last_name"`
	AvatarURL *string `json:"avatar_url"`
}

type ListUsersRequest struct {
	Page     int    `json:"page" validate:"min=1"`
	Limit    int    `json:"limit" validate:"min=1,max=100"`
	Search   string `json:"search"`
	Role     string `json:"role"`
	IsActive *bool  `json:"is_active"`
}

type ListUsersResponse struct {
	Users      []models.User      `json:"users"`
	Pagination PaginationResponse `json:"pagination"`
}

type PaginationResponse struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int64 `json:"total_pages"`
}
