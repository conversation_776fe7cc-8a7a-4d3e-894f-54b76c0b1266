package services

import (
	"context"
	"fmt"

	"github.com/godeye/auth/internal/models"
	"github.com/godeye/auth/pkg/errors"
	"github.com/godeye/auth/pkg/logger"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// RoleService 角色服务
type RoleService struct {
	db     *gorm.DB
	redis  *redis.Client
	logger logger.Logger
}

// NewRoleService 创建角色服务
func NewRoleService(db *gorm.DB, redis *redis.Client, logger logger.Logger) *RoleService {
	return &RoleService{
		db:     db,
		redis:  redis,
		logger: logger,
	}
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(ctx context.Context, req *CreateRoleRequest) (*models.Role, error) {
	// 检查角色名是否已存在
	var existingRole models.Role
	if err := s.db.Where("name = ?", req.Name).First(&existingRole).Error; err == nil {
		return nil, errors.NewValidationError("角色名已存在")
	}

	role := &models.Role{
		Name:        req.Name,
		Description: req.Description,
		Permissions: req.Permissions,
		IsSystem:    false, // 用户创建的角色不是系统角色
	}

	if err := s.db.Create(role).Error; err != nil {
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	s.logger.Info("Role created successfully", "role_id", role.ID, "name", role.Name)
	return role, nil
}

// GetRoleByID 根据ID获取角色
func (s *RoleService) GetRoleByID(ctx context.Context, roleID uuid.UUID) (*models.Role, error) {
	var role models.Role
	if err := s.db.Where("id = ?", roleID).First(&role).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.NewNotFoundError("角色不存在")
		}
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	return &role, nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(ctx context.Context, roleID uuid.UUID, req *UpdateRoleRequest) (*models.Role, error) {
	role, err := s.GetRoleByID(ctx, roleID)
	if err != nil {
		return nil, err
	}

	// 系统角色不允许修改
	if role.IsSystem {
		return nil, errors.NewValidationError("系统角色不允许修改")
	}

	// 更新字段
	if req.Name != nil {
		// 检查新名称是否已存在
		var existingRole models.Role
		if err := s.db.Where("name = ? AND id != ?", *req.Name, roleID).First(&existingRole).Error; err == nil {
			return nil, errors.NewValidationError("角色名已存在")
		}
		role.Name = *req.Name
	}

	if req.Description != nil {
		role.Description = *req.Description
	}

	if req.Permissions != nil {
		role.Permissions = req.Permissions
	}

	if err := s.db.Save(role).Error; err != nil {
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	s.logger.Info("Role updated successfully", "role_id", roleID)
	return role, nil
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(ctx context.Context, roleID uuid.UUID) error {
	role, err := s.GetRoleByID(ctx, roleID)
	if err != nil {
		return err
	}

	// 系统角色不允许删除
	if role.IsSystem {
		return errors.NewValidationError("系统角色不允许删除")
	}

	// 检查是否有用户使用该角色
	var userRoleCount int64
	if err := s.db.Model(&models.UserRole{}).Where("role_id = ?", roleID).Count(&userRoleCount).Error; err != nil {
		return fmt.Errorf("failed to check role usage: %w", err)
	}

	if userRoleCount > 0 {
		return errors.NewValidationError("该角色正在被使用，无法删除")
	}

	if err := s.db.Delete(role).Error; err != nil {
		return fmt.Errorf("failed to delete role: %w", err)
	}

	s.logger.Info("Role deleted successfully", "role_id", roleID)
	return nil
}

// ListRoles 获取角色列表
func (s *RoleService) ListRoles(ctx context.Context, req *ListRolesRequest) (*ListRolesResponse, error) {
	var roles []models.Role
	var total int64

	query := s.db.Model(&models.Role{})

	// 搜索条件
	if req.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// 系统角色过滤
	if req.IsSystem != nil {
		query = query.Where("is_system = ?", *req.IsSystem)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count roles: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Limit
	if err := query.Offset(offset).
		Limit(req.Limit).
		Order("created_at DESC").
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to list roles: %w", err)
	}

	return &ListRolesResponse{
		Roles: roles,
		Pagination: PaginationResponse{
			Page:       req.Page,
			Limit:      req.Limit,
			Total:      total,
			TotalPages: (total + int64(req.Limit) - 1) / int64(req.Limit),
		},
	}, nil
}

// GetRolePermissions 获取角色权限
func (s *RoleService) GetRolePermissions(ctx context.Context, roleID uuid.UUID) (map[string]interface{}, error) {
	role, err := s.GetRoleByID(ctx, roleID)
	if err != nil {
		return nil, err
	}

	return role.Permissions, nil
}

// CheckPermission 检查角色权限
func (s *RoleService) CheckPermission(ctx context.Context, roleID uuid.UUID, resource, action string) (bool, error) {
	role, err := s.GetRoleByID(ctx, roleID)
	if err != nil {
		return false, err
	}

	return role.HasPermission(resource, action), nil
}

// GetUserRoles 获取用户角色
func (s *RoleService) GetUserRoles(ctx context.Context, userID uuid.UUID) ([]models.Role, error) {
	var roles []models.Role
	
	if err := s.db.Table("roles").
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND (user_roles.expires_at IS NULL OR user_roles.expires_at > NOW())", userID).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	return roles, nil
}

// CheckUserPermission 检查用户权限
func (s *RoleService) CheckUserPermission(ctx context.Context, userID uuid.UUID, resource, action string) (bool, error) {
	roles, err := s.GetUserRoles(ctx, userID)
	if err != nil {
		return false, err
	}

	for _, role := range roles {
		if role.HasPermission(resource, action) {
			return true, nil
		}
	}

	return false, nil
}

// 请求和响应结构体
type CreateRoleRequest struct {
	Name        string                 `json:"name" validate:"required,min=2,max=50"`
	Description string                 `json:"description"`
	Permissions map[string]interface{} `json:"permissions" validate:"required"`
}

type UpdateRoleRequest struct {
	Name        *string                 `json:"name" validate:"omitempty,min=2,max=50"`
	Description *string                 `json:"description"`
	Permissions map[string]interface{}  `json:"permissions"`
}

type ListRolesRequest struct {
	Page     int    `json:"page" validate:"min=1"`
	Limit    int    `json:"limit" validate:"min=1,max=100"`
	Search   string `json:"search"`
	IsSystem *bool  `json:"is_system"`
}

type ListRolesResponse struct {
	Roles      []models.Role      `json:"roles"`
	Pagination PaginationResponse `json:"pagination"`
}
