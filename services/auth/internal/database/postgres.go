package database

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/godeye/auth/internal/config"
	"github.com/godeye/auth/internal/models"
	_ "github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// NewPostgresDB 创建 PostgreSQL 数据库连接
func NewPostgresDB(cfg config.DatabaseConfig) (*gorm.DB, error) {
	// 创建原始数据库连接用于配置连接池
	sqlDB, err := sql.Open("postgres", cfg.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// 创建 GORM 数据库连接
	gormDB, err := gorm.Open(postgres.New(postgres.Config{
		Conn: sqlDB,
	}), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})

	if err != nil {
		return nil, fmt.Errorf("failed to create GORM connection: %w", err)
	}

	return gormDB, nil
}

// Migrate 运行数据库迁移
func Migrate(db *gorm.DB) error {
	// 使用原生SQL创建表，避免GORM兼容性问题
	if err := createTablesWithSQL(db); err != nil {
		return fmt.Errorf("failed to create tables: %w", err)
	}

	// 创建默认角色
	if err := createDefaultRoles(db); err != nil {
		return fmt.Errorf("failed to create default roles: %w", err)
	}

	return nil
}

// createTablesWithSQL 使用原生SQL创建表
func createTablesWithSQL(db *gorm.DB) error {
	// 创建用户表
	userTableSQL := `
	CREATE TABLE IF NOT EXISTS users (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		username VARCHAR(50) UNIQUE NOT NULL,
		email VARCHAR(100) UNIQUE NOT NULL,
		password_hash VARCHAR(255) NOT NULL,
		full_name VARCHAR(100),
		avatar_url VARCHAR(255),
		phone VARCHAR(20),
		is_active BOOLEAN DEFAULT true,
		is_verified BOOLEAN DEFAULT false,
		last_login_at TIMESTAMPTZ,
		created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
		deleted_at TIMESTAMPTZ
	);`

	if err := db.Exec(userTableSQL).Error; err != nil {
		return fmt.Errorf("failed to create users table: %w", err)
	}

	// 创建角色表
	roleTableSQL := `
	CREATE TABLE IF NOT EXISTS roles (
		id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
		name VARCHAR(50) UNIQUE NOT NULL,
		description TEXT,
		permissions JSONB NOT NULL DEFAULT '{}',
		is_system BOOLEAN DEFAULT false,
		created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
		deleted_at TIMESTAMPTZ
	);`

	if err := db.Exec(roleTableSQL).Error; err != nil {
		return fmt.Errorf("failed to create roles table: %w", err)
	}

	return nil
}

// createDefaultRoles 创建默认角色
func createDefaultRoles(db *gorm.DB) error {
	defaultRoles := []models.Role{
		{
			Name:        "admin",
			Description: "系统管理员",
			Permissions: models.JSONMap{
				"all": true,
			},
			IsSystem: true,
		},
		{
			Name:        "manager",
			Description: "管理员",
			Permissions: models.JSONMap{
				"users":         []string{"read", "create", "update"},
				"tasks":         []string{"all"},
				"results":       []string{"all"},
				"notifications": []string{"all"},
			},
			IsSystem: true,
		},
		{
			Name:        "user",
			Description: "普通用户",
			Permissions: models.JSONMap{
				"tasks":         []string{"read", "create", "update"},
				"results":       []string{"read"},
				"notifications": []string{"read", "create"},
			},
			IsSystem: true,
		},
	}

	for _, role := range defaultRoles {
		var existingRole models.Role
		result := db.Where("name = ?", role.Name).First(&existingRole)
		
		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				// 角色不存在，创建新角色
				if err := db.Create(&role).Error; err != nil {
					return fmt.Errorf("failed to create role %s: %w", role.Name, err)
				}
			} else {
				return fmt.Errorf("failed to check role %s: %w", role.Name, result.Error)
			}
		} else {
			// 角色已存在，更新权限
			existingRole.Permissions = role.Permissions
			existingRole.Description = role.Description
			if err := db.Save(&existingRole).Error; err != nil {
				return fmt.Errorf("failed to update role %s: %w", role.Name, err)
			}
		}
	}

	return nil
}
