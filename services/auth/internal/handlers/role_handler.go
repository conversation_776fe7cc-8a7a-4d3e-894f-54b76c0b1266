package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/godeye/auth/internal/middleware"
	"github.com/godeye/auth/internal/services"
	"github.com/godeye/auth/pkg/errors"
	"github.com/godeye/auth/pkg/logger"
	"github.com/godeye/auth/pkg/validator"
	"github.com/google/uuid"
)

// RoleHandler 角色处理器
type RoleHandler struct {
	roleService *services.RoleService
	logger      logger.Logger
	validator   validator.Validator
}

// NewRoleHandler 创建角色处理器
func NewRoleHandler(roleService *services.RoleService, logger logger.Logger) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
		logger:      logger,
		validator:   validator.New(),
	}
}

// ListRoles 获取角色列表
func (h *RoleHandler) ListRoles(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON><PERSON><PERSON><PERSON><PERSON>("limit", "20"))
	search := c.Query("search")
	
	var isSystem *bool
	if isSystemStr := c.Query("is_system"); isSystemStr != "" {
		if system, err := strconv.ParseBool(isSystemStr); err == nil {
			isSystem = &system
		}
	}

	req := &services.ListRolesRequest{
		Page:     page,
		Limit:    limit,
		Search:   search,
		IsSystem: isSystem,
	}

	if err := h.validator.Validate(req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	resp, err := h.roleService.ListRoles(c.Request.Context(), req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resp,
	})
}

// GetRole 获取角色详情
func (h *RoleHandler) GetRole(c *gin.Context) {
	roleIDStr := c.Param("role_id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的角色ID"))
		return
	}

	role, err := h.roleService.GetRoleByID(c.Request.Context(), roleID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    role,
	})
}

// CreateRole 创建角色
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req services.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	role, err := h.roleService.CreateRole(c.Request.Context(), &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	currentUserID, _ := middleware.GetCurrentUserID(c)
	h.logger.Info("Role created", "role_id", role.ID, "name", role.Name, "created_by", currentUserID)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "角色创建成功",
		"data":    role,
	})
}

// UpdateRole 更新角色
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	roleIDStr := c.Param("role_id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的角色ID"))
		return
	}

	var req services.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	role, err := h.roleService.UpdateRole(c.Request.Context(), roleID, &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	currentUserID, _ := middleware.GetCurrentUserID(c)
	h.logger.Info("Role updated", "role_id", roleID, "updated_by", currentUserID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "角色更新成功",
		"data":    role,
	})
}

// DeleteRole 删除角色
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	roleIDStr := c.Param("role_id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的角色ID"))
		return
	}

	if err := h.roleService.DeleteRole(c.Request.Context(), roleID); err != nil {
		h.handleError(c, err)
		return
	}

	currentUserID, _ := middleware.GetCurrentUserID(c)
	h.logger.Info("Role deleted", "role_id", roleID, "deleted_by", currentUserID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "角色删除成功",
	})
}

// handleError 处理错误
func (h *RoleHandler) handleError(c *gin.Context, err error) {
	if appErr := errors.GetAppError(err); appErr != nil {
		c.JSON(appErr.StatusCode, gin.H{
			"success": false,
			"error": gin.H{
				"code":    appErr.Code,
				"message": appErr.Message,
				"details": appErr.Details,
			},
		})
		return
	}

	// 未知错误
	h.logger.Error("Unknown error occurred", "error", err)
	c.JSON(http.StatusInternalServerError, gin.H{
		"success": false,
		"error": gin.H{
			"code":    "INTERNAL_ERROR",
			"message": "服务器内部错误",
		},
	})
}

// handleValidationError 处理验证错误
func (h *RoleHandler) handleValidationError(c *gin.Context, err error) {
	if validationErrs := validator.GetValidationErrors(err); validationErrs != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "输入数据验证失败",
				"details": validationErrs,
			},
		})
		return
	}

	h.handleError(c, err)
}
