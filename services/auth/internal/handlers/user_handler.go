package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/godeye/auth/internal/middleware"
	"github.com/godeye/auth/internal/services"
	"github.com/godeye/auth/pkg/errors"
	"github.com/godeye/auth/pkg/logger"
	"github.com/godeye/auth/pkg/validator"
	"github.com/google/uuid"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService *services.UserService
	authService *services.AuthService
	logger      logger.Logger
	validator   validator.Validator
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService *services.UserService, logger logger.Logger) *UserHandler {
	return &UserHandler{
		userService: userService,
		logger:      logger,
		validator:   validator.New(),
	}
}

// GetProfile 获取用户资料
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		h.handleError(c, err)
		return
	}

	user, err := h.userService.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"id":           user.ID,
			"email":        user.Email,
			"username":     user.Username,
			"first_name":   user.FirstName,
			"last_name":    user.LastName,
			"avatar_url":   user.AvatarURL,
			"role":         user.Role,
			"is_active":    user.IsActive,
			"is_verified":  user.IsVerified,
			"last_login_at": user.LastLoginAt,
			"created_at":   user.CreatedAt,
		},
	})
}

// UpdateProfile 更新用户资料
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		h.handleError(c, err)
		return
	}

	var req services.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	user, err := h.userService.UpdateUser(c.Request.Context(), userID, &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info("User profile updated", "user_id", userID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户信息更新成功",
		"data": gin.H{
			"id":         user.ID,
			"first_name": user.FirstName,
			"last_name":  user.LastName,
			"avatar_url": user.AvatarURL,
		},
	})
}

// ChangePassword 修改密码
func (h *UserHandler) ChangePassword(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		h.handleError(c, err)
		return
	}

	var req struct {
		CurrentPassword string `json:"current_password" validate:"required"`
		NewPassword     string `json:"new_password" validate:"required,min=8"`
		ConfirmPassword string `json:"confirm_password" validate:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	if req.NewPassword != req.ConfirmPassword {
		h.handleError(c, errors.NewValidationError("两次输入的密码不一致"))
		return
	}

	if err := h.userService.ChangePassword(c.Request.Context(), userID, req.CurrentPassword, req.NewPassword); err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info("Password changed successfully", "user_id", userID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码修改成功",
	})
}

// GetSessions 获取用户会话列表
func (h *UserHandler) GetSessions(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		h.handleError(c, err)
		return
	}

	sessions, err := h.authService.GetUserSessions(c.Request.Context(), userID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    sessions,
	})
}

// RevokeSession 撤销会话
func (h *UserHandler) RevokeSession(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		h.handleError(c, err)
		return
	}

	sessionIDStr := c.Param("session_id")
	sessionID, err := uuid.Parse(sessionIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的会话ID"))
		return
	}

	if err := h.authService.RevokeSession(c.Request.Context(), userID, sessionID); err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info("Session revoked", "user_id", userID, "session_id", sessionID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "会话已撤销",
	})
}

// ListUsers 获取用户列表（管理员功能）
func (h *UserHandler) ListUsers(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	search := c.Query("search")
	role := c.Query("role")
	
	var isActive *bool
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if active, err := strconv.ParseBool(isActiveStr); err == nil {
			isActive = &active
		}
	}

	req := &services.ListUsersRequest{
		Page:     page,
		Limit:    limit,
		Search:   search,
		Role:     role,
		IsActive: isActive,
	}

	if err := h.validator.Validate(req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	resp, err := h.userService.ListUsers(c.Request.Context(), req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resp,
	})
}

// GetUser 获取用户信息（管理员功能）
func (h *UserHandler) GetUser(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的用户ID"))
		return
	}

	user, err := h.userService.GetUserByID(c.Request.Context(), userID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    user,
	})
}

// UpdateUser 更新用户信息（管理员功能）
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的用户ID"))
		return
	}

	var req services.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	user, err := h.userService.UpdateUser(c.Request.Context(), userID, &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	currentUserID, _ := middleware.GetCurrentUserID(c)
	h.logger.Info("User updated by admin", "user_id", userID, "admin_id", currentUserID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户信息更新成功",
		"data":    user,
	})
}

// DeleteUser 删除用户（管理员功能）
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的用户ID"))
		return
	}

	// TODO: 实现删除用户逻辑
	currentUserID, _ := middleware.GetCurrentUserID(c)
	h.logger.Info("User deletion requested", "user_id", userID, "admin_id", currentUserID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户删除成功",
	})
}

// AssignRole 分配角色（管理员功能）
func (h *UserHandler) AssignRole(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的用户ID"))
		return
	}

	var req struct {
		RoleID uuid.UUID `json:"role_id" validate:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	currentUserID, _ := middleware.GetCurrentUserID(c)
	if err := h.userService.AssignRole(c.Request.Context(), userID, req.RoleID, currentUserID); err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info("Role assigned", "user_id", userID, "role_id", req.RoleID, "assigned_by", currentUserID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "角色分配成功",
	})
}

// RevokeRole 撤销角色（管理员功能）
func (h *UserHandler) RevokeRole(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的用户ID"))
		return
	}

	roleIDStr := c.Param("role_id")
	roleID, err := uuid.Parse(roleIDStr)
	if err != nil {
		h.handleError(c, errors.NewValidationError("无效的角色ID"))
		return
	}

	if err := h.userService.RevokeRole(c.Request.Context(), userID, roleID); err != nil {
		h.handleError(c, err)
		return
	}

	currentUserID, _ := middleware.GetCurrentUserID(c)
	h.logger.Info("Role revoked", "user_id", userID, "role_id", roleID, "revoked_by", currentUserID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "角色撤销成功",
	})
}

// GetAuditLogs 获取审计日志（管理员功能）
func (h *UserHandler) GetAuditLogs(c *gin.Context) {
	// TODO: 实现审计日志查询逻辑
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"logs":       []interface{}{},
			"pagination": gin.H{
				"page":        1,
				"limit":       20,
				"total":       0,
				"total_pages": 0,
			},
		},
	})
}

// handleError 处理错误
func (h *UserHandler) handleError(c *gin.Context, err error) {
	if appErr := errors.GetAppError(err); appErr != nil {
		c.JSON(appErr.StatusCode, gin.H{
			"success": false,
			"error": gin.H{
				"code":    appErr.Code,
				"message": appErr.Message,
				"details": appErr.Details,
			},
		})
		return
	}

	// 未知错误
	h.logger.Error("Unknown error occurred", "error", err)
	c.JSON(http.StatusInternalServerError, gin.H{
		"success": false,
		"error": gin.H{
			"code":    "INTERNAL_ERROR",
			"message": "服务器内部错误",
		},
	})
}

// handleValidationError 处理验证错误
func (h *UserHandler) handleValidationError(c *gin.Context, err error) {
	if validationErrs := validator.GetValidationErrors(err); validationErrs != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "输入数据验证失败",
				"details": validationErrs,
			},
		})
		return
	}

	h.handleError(c, err)
}
