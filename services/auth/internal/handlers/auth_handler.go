package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/godeye/auth/internal/middleware"
	"github.com/godeye/auth/internal/models"
	"github.com/godeye/auth/internal/services"
	"github.com/godeye/auth/pkg/errors"
	"github.com/godeye/auth/pkg/logger"
	"github.com/godeye/auth/pkg/validator"
	"github.com/google/uuid"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *services.AuthService
	userService *services.UserService
	logger      logger.Logger
	validator   validator.Validator
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService *services.AuthService, userService *services.UserService, logger logger.Logger) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		userService: userService,
		logger:      logger,
		validator:   validator.New(),
	}
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	var req services.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	// 验证请求参数
	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	// 创建用户
	user, err := h.userService.CreateUser(c.Request.Context(), &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info("User registered successfully", "user_id", user.ID, "email", user.Email)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "用户注册成功",
		"data": gin.H{
			"user_id":     user.ID,
			"email":       user.Email,
			"username":    user.Username,
			"is_verified": user.IsVerified,
		},
	})
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req services.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	// 验证请求参数
	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	// 设置设备信息
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")
	req.DeviceInfo = models.JSONMap{
		"ip":         req.IPAddress,
		"user_agent": req.UserAgent,
		"timestamp":  time.Now(),
	}

	// 用户登录
	resp, err := h.authService.Login(c.Request.Context(), &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info("User logged in successfully", "user_id", resp.User.ID, "email", resp.User.Email)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "登录成功",
		"data":    resp,
	})
}

// RefreshToken 刷新令牌
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// 从 Authorization 头获取 refresh token
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		h.handleError(c, errors.NewUnauthorizedError("缺少刷新令牌"))
		return
	}

	token := authHeader
	if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
		token = authHeader[7:]
	}

	// 刷新令牌
	resp, err := h.authService.RefreshToken(c.Request.Context(), token)
	if err != nil {
		h.handleError(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    resp,
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	userID, err := middleware.GetCurrentUserID(c)
	if err != nil {
		h.handleError(c, err)
		return
	}

	// 获取会话ID（可选）
	var sessionID *uuid.UUID
	if sessionIDStr := c.Query("session_id"); sessionIDStr != "" {
		if id, err := uuid.Parse(sessionIDStr); err == nil {
			sessionID = &id
		}
	}

	// 用户登出
	if err := h.authService.Logout(c.Request.Context(), userID, sessionID); err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info("User logged out successfully", "user_id", userID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "登出成功",
	})
}

// ForgotPassword 忘记密码
func (h *AuthHandler) ForgotPassword(c *gin.Context) {
	var req struct {
		Email string `json:"email" validate:"required,email"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	// TODO: 实现发送重置密码邮件逻辑
	h.logger.Info("Password reset requested", "email", req.Email)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码重置邮件已发送",
	})
}

// ResetPassword 重置密码
func (h *AuthHandler) ResetPassword(c *gin.Context) {
	var req struct {
		Token           string `json:"token" validate:"required"`
		NewPassword     string `json:"new_password" validate:"required,min=8"`
		ConfirmPassword string `json:"confirm_password" validate:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.handleError(c, errors.NewValidationError("请求参数格式错误"))
		return
	}

	if err := h.validator.Validate(&req); err != nil {
		h.handleValidationError(c, err)
		return
	}

	if req.NewPassword != req.ConfirmPassword {
		h.handleError(c, errors.NewValidationError("两次输入的密码不一致"))
		return
	}

	// TODO: 实现重置密码逻辑
	h.logger.Info("Password reset completed")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码重置成功",
	})
}

// handleError 处理错误
func (h *AuthHandler) handleError(c *gin.Context, err error) {
	if appErr := errors.GetAppError(err); appErr != nil {
		c.JSON(appErr.StatusCode, gin.H{
			"success": false,
			"error": gin.H{
				"code":    appErr.Code,
				"message": appErr.Message,
				"details": appErr.Details,
			},
		})
		return
	}

	// 未知错误
	h.logger.Error("Unknown error occurred", "error", err)
	c.JSON(http.StatusInternalServerError, gin.H{
		"success": false,
		"error": gin.H{
			"code":    "INTERNAL_ERROR",
			"message": "服务器内部错误",
		},
	})
}

// handleValidationError 处理验证错误
func (h *AuthHandler) handleValidationError(c *gin.Context, err error) {
	if validationErrs := validator.GetValidationErrors(err); validationErrs != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "VALIDATION_ERROR",
				"message": "输入数据验证失败",
				"details": validationErrs,
			},
		})
		return
	}

	h.handleError(c, err)
}
