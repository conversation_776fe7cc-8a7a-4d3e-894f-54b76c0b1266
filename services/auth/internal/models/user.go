package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// JSONMap 自定义JSON类型
type JSONMap map[string]interface{}

// Value 实现 driver.Valuer 接口
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现 sql.Scanner 接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = make(JSONMap)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONMap", value)
	}

	return json.Unmarshal(bytes, j)
}

// User 用户模型
type User struct {
	ID             uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	Email          string     `gorm:"uniqueIndex;not null" json:"email"`
	Username       string     `gorm:"uniqueIndex;not null" json:"username"`
	PasswordHash   string     `gorm:"not null" json:"-"`
	FirstName      string     `json:"first_name"`
	LastName       string     `json:"last_name"`
	AvatarURL      string     `json:"avatar_url"`
	IsActive       bool       `gorm:"default:true" json:"is_active"`
	IsVerified     bool       `gorm:"default:false" json:"is_verified"`
	Role           string     `gorm:"default:user" json:"role"`
	LastLoginAt    *time.Time `json:"last_login_at"`
	LoginAttempts  int        `gorm:"default:0" json:"-"`
	LockedUntil    *time.Time `json:"-"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
	
	// 关联
	UserRoles []UserRole `gorm:"foreignKey:UserID" json:"user_roles,omitempty"`
	Sessions  []UserSession `gorm:"foreignKey:UserID" json:"sessions,omitempty"`
}

// Role 角色模型
type Role struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	Name        string    `gorm:"uniqueIndex;not null" json:"name"`
	Description string    `json:"description"`
	Permissions JSONMap   `gorm:"type:jsonb;not null;default:'{}'" json:"permissions"`
	IsSystem    bool      `gorm:"default:false" json:"is_system"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	
	// 关联
	UserRoles []UserRole `gorm:"foreignKey:RoleID" json:"user_roles,omitempty"`
}

// UserRole 用户角色关联模型
type UserRole struct {
	ID         uuid.UUID  `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID     uuid.UUID  `gorm:"not null" json:"user_id"`
	RoleID     uuid.UUID  `gorm:"not null" json:"role_id"`
	AssignedBy *uuid.UUID `json:"assigned_by"`
	AssignedAt time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"assigned_at"`
	ExpiresAt  *time.Time `json:"expires_at"`
	
	// 关联
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Role Role `gorm:"foreignKey:RoleID" json:"role,omitempty"`
}

// UserSession 用户会话模型
type UserSession struct {
	ID             uuid.UUID `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID         uuid.UUID `gorm:"not null" json:"user_id"`
	TokenHash      string    `gorm:"not null" json:"-"`
	DeviceInfo     JSONMap   `gorm:"type:jsonb" json:"device_info"`
	IPAddress      string    `gorm:"type:inet" json:"ip_address"`
	UserAgent      string                 `json:"user_agent"`
	IsActive       bool                   `gorm:"default:true" json:"is_active"`
	ExpiresAt      time.Time              `gorm:"not null" json:"expires_at"`
	CreatedAt      time.Time              `json:"created_at"`
	LastAccessedAt time.Time              `gorm:"default:CURRENT_TIMESTAMP" json:"last_accessed_at"`
	
	// 关联
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// AuditLog 审计日志模型
type AuditLog struct {
	ID           uuid.UUID              `gorm:"type:uuid;primary_key;default:uuid_generate_v4()" json:"id"`
	UserID       *uuid.UUID             `json:"user_id"`
	Action       string                 `gorm:"not null" json:"action"`
	ResourceType string                 `json:"resource_type"`
	ResourceID   *uuid.UUID             `json:"resource_id"`
	OldValues    JSONMap `gorm:"type:jsonb" json:"old_values"`
	NewValues    JSONMap `gorm:"type:jsonb" json:"new_values"`
	IPAddress    string                 `gorm:"type:inet" json:"ip_address"`
	UserAgent    string                 `json:"user_agent"`
	RequestID    string                 `json:"request_id"`
	SessionID    string                 `json:"session_id"`
	Success      bool                   `gorm:"default:true" json:"success"`
	ErrorMessage string                 `json:"error_message"`
	DurationMs   int                    `json:"duration_ms"`
	CreatedAt    time.Time              `json:"created_at"`
	
	// 关联
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// BeforeCreate GORM 钩子 - 创建前
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}

func (r *Role) BeforeCreate(tx *gorm.DB) error {
	if r.ID == uuid.Nil {
		r.ID = uuid.New()
	}
	return nil
}

func (ur *UserRole) BeforeCreate(tx *gorm.DB) error {
	if ur.ID == uuid.Nil {
		ur.ID = uuid.New()
	}
	return nil
}

func (us *UserSession) BeforeCreate(tx *gorm.DB) error {
	if us.ID == uuid.Nil {
		us.ID = uuid.New()
	}
	return nil
}

func (al *AuditLog) BeforeCreate(tx *gorm.DB) error {
	if al.ID == uuid.Nil {
		al.ID = uuid.New()
	}
	return nil
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

func (Role) TableName() string {
	return "roles"
}

func (UserRole) TableName() string {
	return "user_roles"
}

func (UserSession) TableName() string {
	return "user_sessions"
}

func (AuditLog) TableName() string {
	return "audit_logs"
}

// GetFullName 获取用户全名
func (u *User) GetFullName() string {
	if u.FirstName != "" && u.LastName != "" {
		return u.FirstName + " " + u.LastName
	}
	if u.FirstName != "" {
		return u.FirstName
	}
	if u.LastName != "" {
		return u.LastName
	}
	return u.Username
}

// IsLocked 检查用户是否被锁定
func (u *User) IsLocked() bool {
	if u.LockedUntil == nil {
		return false
	}
	return time.Now().Before(*u.LockedUntil)
}

// HasPermission 检查角色是否有指定权限
func (r *Role) HasPermission(resource, action string) bool {
	// 如果有全部权限
	if all, ok := r.Permissions["all"].(bool); ok && all {
		return true
	}
	
	// 检查资源权限
	if resourcePerms, ok := r.Permissions[resource]; ok {
		switch perms := resourcePerms.(type) {
		case []interface{}:
			// 权限列表
			for _, perm := range perms {
				if permStr, ok := perm.(string); ok {
					if permStr == action || permStr == "all" {
						return true
					}
				}
			}
		case []string:
			// 字符串权限列表
			for _, perm := range perms {
				if perm == action || perm == "all" {
					return true
				}
			}
		case string:
			// 单个权限
			return perms == action || perms == "all"
		case bool:
			// 布尔权限
			return perms
		}
	}
	
	return false
}
