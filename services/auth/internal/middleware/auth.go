package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/godeye/auth/internal/services"
	"github.com/godeye/auth/pkg/errors"
	"github.com/google/uuid"
)

// AuthService 认证服务接口
type AuthService interface {
	ValidateToken(tokenString string) (*services.TokenClaims, error)
}

// Auth 认证中间件
func Auth(authService AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取 Authorization 头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "UNAUTHORIZED",
					"message": "缺少认证令牌",
				},
			})
			c.Abort()
			return
		}

		// 检查 Bearer 前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "UNAUTHORIZED",
					"message": "无效的认证令牌格式",
				},
			})
			c.Abort()
			return
		}

		// 提取 token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "UNAUTHORIZED",
					"message": "认证令牌为空",
				},
			})
			c.Abort()
			return
		}

		// 验证 token
		claims, err := authService.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "TOKEN_INVALID",
					"message": "无效的认证令牌",
				},
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_username", claims.Username)
		c.Set("user_role", claims.Role)
		c.Set("user_permissions", claims.Permissions)
		c.Set("token_claims", claims)

		c.Next()
	}
}

// RequirePermission 权限检查中间件
func RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户权限
		permissions, exists := c.Get("user_permissions")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "PERMISSION_DENIED",
					"message": "权限不足",
				},
			})
			c.Abort()
			return
		}

		userPermissions, ok := permissions.([]string)
		if !ok {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "PERMISSION_DENIED",
					"message": "权限信息格式错误",
				},
			})
			c.Abort()
			return
		}

		// 检查是否有全部权限
		for _, perm := range userPermissions {
			if perm == "all" || perm == permission {
				c.Next()
				return
			}
		}

		// 检查资源级权限
		parts := strings.Split(permission, ":")
		if len(parts) == 2 {
			resource, _ := parts[0], parts[1]
			for _, perm := range userPermissions {
				if perm == resource+":all" {
					c.Next()
					return
				}
			}
		}

		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "PERMISSION_DENIED",
				"message": "权限不足",
			},
		})
		c.Abort()
	}
}

// RequireRole 角色检查中间件
func RequireRole(role string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "PERMISSION_DENIED",
					"message": "权限不足",
				},
			})
			c.Abort()
			return
		}

		if userRole != role && userRole != "admin" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "PERMISSION_DENIED",
					"message": "权限不足",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetCurrentUserID 获取当前用户ID
func GetCurrentUserID(c *gin.Context) (uuid.UUID, error) {
	userID, exists := c.Get("user_id")
	if !exists {
		return uuid.Nil, errors.NewUnauthorizedError("用户未认证")
	}

	id, ok := userID.(uuid.UUID)
	if !ok {
		return uuid.Nil, errors.NewInternalError("用户ID格式错误")
	}

	return id, nil
}

// GetCurrentUserEmail 获取当前用户邮箱
func GetCurrentUserEmail(c *gin.Context) (string, error) {
	email, exists := c.Get("user_email")
	if !exists {
		return "", errors.NewUnauthorizedError("用户未认证")
	}

	userEmail, ok := email.(string)
	if !ok {
		return "", errors.NewInternalError("用户邮箱格式错误")
	}

	return userEmail, nil
}

// GetCurrentUserRole 获取当前用户角色
func GetCurrentUserRole(c *gin.Context) (string, error) {
	role, exists := c.Get("user_role")
	if !exists {
		return "", errors.NewUnauthorizedError("用户未认证")
	}

	userRole, ok := role.(string)
	if !ok {
		return "", errors.NewInternalError("用户角色格式错误")
	}

	return userRole, nil
}

// GetCurrentUserPermissions 获取当前用户权限
func GetCurrentUserPermissions(c *gin.Context) ([]string, error) {
	permissions, exists := c.Get("user_permissions")
	if !exists {
		return nil, errors.NewUnauthorizedError("用户未认证")
	}

	userPermissions, ok := permissions.([]string)
	if !ok {
		return nil, errors.NewInternalError("用户权限格式错误")
	}

	return userPermissions, nil
}

// HasPermission 检查用户是否有指定权限
func HasPermission(c *gin.Context, permission string) bool {
	permissions, err := GetCurrentUserPermissions(c)
	if err != nil {
		return false
	}

	// 检查是否有全部权限
	for _, perm := range permissions {
		if perm == "all" || perm == permission {
			return true
		}
	}

	// 检查资源级权限
	parts := strings.Split(permission, ":")
	if len(parts) == 2 {
		resource, _ := parts[0], parts[1]
		for _, perm := range permissions {
			if perm == resource+":all" {
				return true
			}
		}
	}

	return false
}
