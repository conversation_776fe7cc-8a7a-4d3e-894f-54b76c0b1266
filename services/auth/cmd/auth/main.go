package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/godeye/auth/internal/config"
	"github.com/godeye/auth/internal/database"
	"github.com/godeye/auth/internal/handlers"
	"github.com/godeye/auth/internal/middleware"
	"github.com/godeye/auth/internal/services"
	"github.com/godeye/auth/pkg/logger"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger := logger.New(cfg.LogLevel)

	// 初始化数据库
	db, err := database.NewPostgresDB(cfg.Database)
	if err != nil {
		logger.Fatal("Failed to connect to database", "error", err)
	}
	// GORM DB doesn't have Close method, get underlying sql.DB
	sqlDB, err := db.DB()
	if err != nil {
		logger.Fatal("Failed to get underlying sql.DB", "error", err)
	}
	defer sqlDB.Close()

	// 初始化 Redis
	redisClient, err := database.NewRedisClient(cfg.Redis)
	if err != nil {
		logger.Fatal("Failed to connect to Redis", "error", err)
	}
	defer redisClient.Close()

	// 运行数据库迁移
	if err := database.Migrate(db); err != nil {
		logger.Fatal("Failed to run database migrations", "error", err)
	}

	// 初始化服务
	userService := services.NewUserService(db, redisClient, logger)
	authService := services.NewAuthService(db, redisClient, cfg.JWT, logger)
	roleService := services.NewRoleService(db, redisClient, logger)

	// 初始化处理器
	authHandler := handlers.NewAuthHandler(authService, userService, logger)
	userHandler := handlers.NewUserHandler(userService, logger)
	roleHandler := handlers.NewRoleHandler(roleService, logger)

	// 设置 Gin 模式
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := gin.New()

	// 添加中间件
	router.Use(middleware.Logger(logger))
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.CORS())
	router.Use(middleware.RateLimit(cfg.RateLimit))

	// 设置调试模式
	gin.SetMode(gin.DebugMode)

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"service":   "auth",
			"version":   "1.0.0",
			"timestamp": time.Now().UTC(),
		})
	})

	// Ping路由
	router.GET("/api/v1/auth/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "pong",
			"service": "auth",
			"timestamp": time.Now().Unix(),
		})
	})

	// API 路由组
	v1 := router.Group("/api/v1")
	{
		// 认证路由 (无需认证)
		auth := v1.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.POST("/logout", middleware.Auth(authService), authHandler.Logout)
			auth.POST("/forgot-password", authHandler.ForgotPassword)
			auth.POST("/reset-password", authHandler.ResetPassword)
		}

		// 用户路由 (需要认证)
		user := v1.Group("/auth")
		user.Use(middleware.Auth(authService))
		{
			user.GET("/profile", userHandler.GetProfile)
			user.PUT("/profile", userHandler.UpdateProfile)
			user.POST("/change-password", userHandler.ChangePassword)
			user.GET("/sessions", userHandler.GetSessions)
			user.DELETE("/sessions/:session_id", userHandler.RevokeSession)
		}

		// 用户管理路由 (需要管理员权限)
		users := v1.Group("/auth/users")
		users.Use(middleware.Auth(authService))
		users.Use(middleware.RequirePermission("users:read"))
		{
			users.GET("", userHandler.ListUsers)
			users.GET("/:user_id", userHandler.GetUser)
			users.PUT("/:user_id", middleware.RequirePermission("users:update"), userHandler.UpdateUser)
			users.DELETE("/:user_id", middleware.RequirePermission("users:delete"), userHandler.DeleteUser)
			users.POST("/:user_id/roles", middleware.RequirePermission("users:update"), userHandler.AssignRole)
			users.DELETE("/:user_id/roles/:role_id", middleware.RequirePermission("users:update"), userHandler.RevokeRole)
		}

		// 角色管理路由 (需要管理员权限)
		roles := v1.Group("/auth/roles")
		roles.Use(middleware.Auth(authService))
		roles.Use(middleware.RequirePermission("roles:read"))
		{
			roles.GET("", roleHandler.ListRoles)
			roles.GET("/:role_id", roleHandler.GetRole)
			roles.POST("", middleware.RequirePermission("roles:create"), roleHandler.CreateRole)
			roles.PUT("/:role_id", middleware.RequirePermission("roles:update"), roleHandler.UpdateRole)
			roles.DELETE("/:role_id", middleware.RequirePermission("roles:delete"), roleHandler.DeleteRole)
		}

		// 审计日志路由 (需要管理员权限)
		audit := v1.Group("/auth/audit-logs")
		audit.Use(middleware.Auth(authService))
		audit.Use(middleware.RequirePermission("audit:read"))
		{
			audit.GET("", userHandler.GetAuditLogs)
		}
	}

	// 创建 HTTP 服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// 启动服务器
	go func() {
		logger.Info("Starting auth service", 
			"port", cfg.Server.Port,
			"environment", cfg.Environment)
		
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", "error", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down auth service...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", "error", err)
	}

	logger.Info("Auth service stopped")
}
