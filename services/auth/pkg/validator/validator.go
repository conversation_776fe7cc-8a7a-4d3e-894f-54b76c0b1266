package validator

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/godeye/auth/pkg/errors"
)

// Validator 验证器接口
type Validator interface {
	Validate(s interface{}) error
}

// validator 验证器实现
type validatorImpl struct {
	validate *validator.Validate
}

// New 创建新的验证器
func New() Validator {
	validate := validator.New()
	
	// 注册自定义验证器
	validate.RegisterValidation("password", validatePassword)
	validate.RegisterValidation("username", validateUsername)
	
	// 注册字段名转换函数
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	return &validatorImpl{
		validate: validate,
	}
}

// Validate 验证结构体
func (v *validatorImpl) Validate(s interface{}) error {
	if err := v.validate.Struct(s); err != nil {
		var validationErrors []errors.ValidationError
		
		for _, err := range err.(validator.ValidationErrors) {
			validationErrors = append(validationErrors, errors.ValidationError{
				Field:   err.Field(),
				Message: getErrorMessage(err),
				Value:   err.Value(),
			})
		}
		
		return errors.NewValidationErrors(validationErrors)
	}
	
	return nil
}

// getErrorMessage 获取错误消息
func getErrorMessage(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s 是必填字段", fe.Field())
	case "email":
		return fmt.Sprintf("%s 必须是有效的邮箱地址", fe.Field())
	case "min":
		return fmt.Sprintf("%s 长度不能少于 %s 个字符", fe.Field(), fe.Param())
	case "max":
		return fmt.Sprintf("%s 长度不能超过 %s 个字符", fe.Field(), fe.Param())
	case "len":
		return fmt.Sprintf("%s 长度必须是 %s 个字符", fe.Field(), fe.Param())
	case "oneof":
		return fmt.Sprintf("%s 必须是以下值之一: %s", fe.Field(), fe.Param())
	case "password":
		return fmt.Sprintf("%s 必须包含至少8个字符，包括大小写字母、数字和特殊字符", fe.Field())
	case "username":
		return fmt.Sprintf("%s 只能包含字母、数字、下划线和连字符，长度3-30个字符", fe.Field())
	case "gte":
		return fmt.Sprintf("%s 必须大于或等于 %s", fe.Field(), fe.Param())
	case "lte":
		return fmt.Sprintf("%s 必须小于或等于 %s", fe.Field(), fe.Param())
	case "gt":
		return fmt.Sprintf("%s 必须大于 %s", fe.Field(), fe.Param())
	case "lt":
		return fmt.Sprintf("%s 必须小于 %s", fe.Field(), fe.Param())
	case "uuid":
		return fmt.Sprintf("%s 必须是有效的UUID格式", fe.Field())
	case "url":
		return fmt.Sprintf("%s 必须是有效的URL地址", fe.Field())
	case "alpha":
		return fmt.Sprintf("%s 只能包含字母", fe.Field())
	case "alphanum":
		return fmt.Sprintf("%s 只能包含字母和数字", fe.Field())
	case "numeric":
		return fmt.Sprintf("%s 只能包含数字", fe.Field())
	default:
		return fmt.Sprintf("%s 格式不正确", fe.Field())
	}
}

// validatePassword 验证密码强度
func validatePassword(fl validator.FieldLevel) bool {
	password := fl.Field().String()
	
	if len(password) < 8 {
		return false
	}
	
	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)
	
	for _, char := range password {
		switch {
		case 'A' <= char && char <= 'Z':
			hasUpper = true
		case 'a' <= char && char <= 'z':
			hasLower = true
		case '0' <= char && char <= '9':
			hasNumber = true
		case strings.ContainsRune("!@#$%^&*()_+-=[]{}|;:,.<>?", char):
			hasSpecial = true
		}
	}
	
	return hasUpper && hasLower && hasNumber && hasSpecial
}

// validateUsername 验证用户名格式
func validateUsername(fl validator.FieldLevel) bool {
	username := fl.Field().String()
	
	if len(username) < 3 || len(username) > 30 {
		return false
	}
	
	for _, char := range username {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return false
		}
	}
	
	return true
}

// GetValidationErrors 获取验证错误
func GetValidationErrors(err error) []errors.ValidationError {
	if appErr := errors.GetAppError(err); appErr != nil {
		if appErr.Code == errors.ErrCodeValidation {
			if validationErrs, ok := appErr.Details.([]errors.ValidationError); ok {
				return validationErrs
			}
		}
	}
	return nil
}
