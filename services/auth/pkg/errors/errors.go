package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode 错误代码类型
type ErrorCode string

const (
	// 通用错误
	ErrCodeInternal     ErrorCode = "INTERNAL_ERROR"
	ErrCodeValidation   ErrorCode = "VALIDATION_ERROR"
	ErrCodeNotFound     ErrorCode = "NOT_FOUND"
	ErrCodeUnauthorized ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden    ErrorCode = "FORBIDDEN"
	ErrCodeConflict     ErrorCode = "CONFLICT"
	ErrCodeRateLimit    ErrorCode = "RATE_LIMIT_EXCEEDED"

	// 认证相关错误
	ErrCodeInvalidCredentials ErrorCode = "INVALID_CREDENTIALS"
	ErrCodeAccountLocked      ErrorCode = "ACCOUNT_LOCKED"
	ErrCodeAccountDisabled    ErrorCode = "ACCOUNT_DISABLED"
	ErrCodeTokenExpired       ErrorCode = "TOKEN_EXPIRED"
	ErrCodeTokenInvalid       ErrorCode = "TOKEN_INVALID"

	// 业务相关错误
	ErrCodeEmailExists    ErrorCode = "EMAIL_EXISTS"
	ErrCodeUsernameExists ErrorCode = "USERNAME_EXISTS"
	ErrCodeRoleNotFound   ErrorCode = "ROLE_NOT_FOUND"
	ErrCodePermissionDenied ErrorCode = "PERMISSION_DENIED"
)

// AppError 应用错误结构
type AppError struct {
	Code       ErrorCode   `json:"code"`
	Message    string      `json:"message"`
	Details    interface{} `json:"details,omitempty"`
	StatusCode int         `json:"-"`
	Err        error       `json:"-"`
}

// Error 实现 error 接口
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s (%v)", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 返回包装的错误
func (e *AppError) Unwrap() error {
	return e.Err
}

// NewAppError 创建应用错误
func NewAppError(code ErrorCode, message string, statusCode int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
	}
}

// NewAppErrorWithDetails 创建带详情的应用错误
func NewAppErrorWithDetails(code ErrorCode, message string, statusCode int, details interface{}) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Details:    details,
		StatusCode: statusCode,
	}
}

// WrapError 包装错误
func WrapError(code ErrorCode, message string, statusCode int, err error) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		StatusCode: statusCode,
		Err:        err,
	}
}

// 预定义错误构造函数

// NewInternalError 创建内部错误
func NewInternalError(message string) *AppError {
	return NewAppError(ErrCodeInternal, message, http.StatusInternalServerError)
}

// NewValidationError 创建验证错误
func NewValidationError(message string) *AppError {
	return NewAppError(ErrCodeValidation, message, http.StatusBadRequest)
}

// NewValidationErrorWithDetails 创建带详情的验证错误
func NewValidationErrorWithDetails(message string, details interface{}) *AppError {
	return NewAppErrorWithDetails(ErrCodeValidation, message, http.StatusBadRequest, details)
}

// NewNotFoundError 创建未找到错误
func NewNotFoundError(message string) *AppError {
	return NewAppError(ErrCodeNotFound, message, http.StatusNotFound)
}

// NewUnauthorizedError 创建未授权错误
func NewUnauthorizedError(message string) *AppError {
	return NewAppError(ErrCodeUnauthorized, message, http.StatusUnauthorized)
}

// NewForbiddenError 创建禁止访问错误
func NewForbiddenError(message string) *AppError {
	return NewAppError(ErrCodeForbidden, message, http.StatusForbidden)
}

// NewConflictError 创建冲突错误
func NewConflictError(message string) *AppError {
	return NewAppError(ErrCodeConflict, message, http.StatusConflict)
}

// NewRateLimitError 创建限流错误
func NewRateLimitError(message string) *AppError {
	return NewAppError(ErrCodeRateLimit, message, http.StatusTooManyRequests)
}

// 认证相关错误

// NewInvalidCredentialsError 创建无效凭据错误
func NewInvalidCredentialsError() *AppError {
	return NewAppError(ErrCodeInvalidCredentials, "邮箱或密码错误", http.StatusUnauthorized)
}

// NewAccountLockedError 创建账号锁定错误
func NewAccountLockedError() *AppError {
	return NewAppError(ErrCodeAccountLocked, "账号已被锁定，请稍后再试", http.StatusUnauthorized)
}

// NewAccountDisabledError 创建账号禁用错误
func NewAccountDisabledError() *AppError {
	return NewAppError(ErrCodeAccountDisabled, "账号已被禁用", http.StatusUnauthorized)
}

// NewTokenExpiredError 创建令牌过期错误
func NewTokenExpiredError() *AppError {
	return NewAppError(ErrCodeTokenExpired, "令牌已过期", http.StatusUnauthorized)
}

// NewTokenInvalidError 创建无效令牌错误
func NewTokenInvalidError() *AppError {
	return NewAppError(ErrCodeTokenInvalid, "无效的令牌", http.StatusUnauthorized)
}

// 业务相关错误

// NewEmailExistsError 创建邮箱已存在错误
func NewEmailExistsError() *AppError {
	return NewAppError(ErrCodeEmailExists, "邮箱已被注册", http.StatusConflict)
}

// NewUsernameExistsError 创建用户名已存在错误
func NewUsernameExistsError() *AppError {
	return NewAppError(ErrCodeUsernameExists, "用户名已被使用", http.StatusConflict)
}

// NewRoleNotFoundError 创建角色未找到错误
func NewRoleNotFoundError() *AppError {
	return NewAppError(ErrCodeRoleNotFound, "角色不存在", http.StatusNotFound)
}

// NewPermissionDeniedError 创建权限拒绝错误
func NewPermissionDeniedError() *AppError {
	return NewAppError(ErrCodePermissionDenied, "权限不足", http.StatusForbidden)
}

// IsAppError 检查是否为应用错误
func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

// GetAppError 获取应用错误
func GetAppError(err error) *AppError {
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}
	return nil
}

// ValidationError 验证错误详情
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// ValidationErrors 验证错误列表
type ValidationErrors []ValidationError

// Error 实现 error 接口
func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return "validation failed"
	}
	return fmt.Sprintf("validation failed: %s", ve[0].Message)
}

// NewValidationErrors 创建验证错误列表
func NewValidationErrors(errors []ValidationError) *AppError {
	return NewAppErrorWithDetails(
		ErrCodeValidation,
		"输入数据验证失败",
		http.StatusBadRequest,
		errors,
	)
}
