package logger

import (
	"log/slog"
	"os"
	"strings"
)

// Logger 日志接口
type Logger interface {
	Debug(msg string, args ...interface{})
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	Fatal(msg string, args ...interface{})
	With(args ...interface{}) Logger
}

// slogLogger slog 实现
type slogLogger struct {
	logger *slog.Logger
}

// New 创建新的日志器
func New(level string) Logger {
	var logLevel slog.Level
	
	switch strings.ToLower(level) {
	case "debug":
		logLevel = slog.LevelDebug
	case "info":
		logLevel = slog.LevelInfo
	case "warn", "warning":
		logLevel = slog.LevelWarn
	case "error":
		logLevel = slog.LevelError
	default:
		logLevel = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{
		Level: logLevel,
	}

	handler := slog.NewJSONHandler(os.Stdo<PERSON>, opts)
	logger := slog.New(handler)

	return &slogLogger{
		logger: logger,
	}
}

// Debug 记录调试日志
func (l *slogLogger) Debug(msg string, args ...interface{}) {
	l.logger.Debug(msg, args...)
}

// Info 记录信息日志
func (l *slogLogger) Info(msg string, args ...interface{}) {
	l.logger.Info(msg, args...)
}

// Warn 记录警告日志
func (l *slogLogger) Warn(msg string, args ...interface{}) {
	l.logger.Warn(msg, args...)
}

// Error 记录错误日志
func (l *slogLogger) Error(msg string, args ...interface{}) {
	l.logger.Error(msg, args...)
}

// Fatal 记录致命错误日志并退出
func (l *slogLogger) Fatal(msg string, args ...interface{}) {
	l.logger.Error(msg, args...)
	os.Exit(1)
}

// With 添加上下文字段
func (l *slogLogger) With(args ...interface{}) Logger {
	return &slogLogger{
		logger: l.logger.With(args...),
	}
}
