# GodEye 认证服务开发环境 Dockerfile
FROM golang:1.22-alpine

# 安装开发工具
RUN go install github.com/cosmtrek/air@v1.49.0

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    git \
    curl \
    && rm -rf /var/cache/apk/*

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 创建 Air 配置文件
RUN echo 'root = "."' > .air.toml && \
    echo 'testdata_dir = "testdata"' >> .air.toml && \
    echo 'tmp_dir = "tmp"' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[build]' >> .air.toml && \
    echo '  args_bin = []' >> .air.toml && \
    echo '  bin = "./tmp/main"' >> .air.toml && \
    echo '  cmd = "go build -o ./tmp/main ./cmd/main.go"' >> .air.toml && \
    echo '  delay = 1000' >> .air.toml && \
    echo '  exclude_dir = ["assets", "tmp", "vendor", "testdata"]' >> .air.toml && \
    echo '  exclude_file = []' >> .air.toml && \
    echo '  exclude_regex = ["_test.go"]' >> .air.toml && \
    echo '  exclude_unchanged = false' >> .air.toml && \
    echo '  follow_symlink = false' >> .air.toml && \
    echo '  full_bin = ""' >> .air.toml && \
    echo '  include_dir = []' >> .air.toml && \
    echo '  include_ext = ["go", "tpl", "tmpl", "html"]' >> .air.toml && \
    echo '  include_file = []' >> .air.toml && \
    echo '  kill_delay = "0s"' >> .air.toml && \
    echo '  log = "build-errors.log"' >> .air.toml && \
    echo '  poll = false' >> .air.toml && \
    echo '  poll_interval = 0' >> .air.toml && \
    echo '  rerun = false' >> .air.toml && \
    echo '  rerun_delay = 500' >> .air.toml && \
    echo '  send_interrupt = false' >> .air.toml && \
    echo '  stop_on_root = false' >> .air.toml

# 暴露端口
EXPOSE 8080

# 启动热重载
CMD ["air", "-c", ".air.toml"]
