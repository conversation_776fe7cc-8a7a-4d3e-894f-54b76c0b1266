#!/usr/bin/env python3
"""
GodEye 系统状态报告
"""

import subprocess
import requests
import time
import sys

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "命令执行超时"
    except Exception as e:
        return False, "", str(e)

def check_service_status():
    """检查服务状态"""
    print("🦅 GodEye 系统状态报告")
    print("=" * 50)
    
    # 检查Docker
    print("\n1. 🐳 Docker 状态:")
    success, stdout, stderr = run_command("docker info")
    if success:
        print("   ✅ Docker 运行正常")
    else:
        print("   ❌ Docker 未运行")
        return False
    
    # 检查容器状态
    print("\n2. 📦 容器状态:")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.simple.yml ps")
    if success:
        lines = stdout.strip().split('\n')
        container_count = 0
        for line in lines[1:]:  # 跳过标题行
            if line.strip() and 'Up' in line:
                container_count += 1
                container_name = line.split()[0]
                print(f"   ✅ {container_name} - 运行中")
        
        if container_count == 0:
            print("   ⚠️  没有运行中的容器")
        else:
            print(f"   📊 总计: {container_count} 个容器运行中")
    
    # 检查数据库连接
    print("\n3. 🗄️ 数据库状态:")
    
    # PostgreSQL
    success, stdout, stderr = run_command("docker exec godeye-postgres pg_isready -U godeye")
    if success and "accepting connections" in stdout:
        print("   ✅ PostgreSQL - 连接正常")
    else:
        print("   ❌ PostgreSQL - 连接失败")
    
    # Redis
    success, stdout, stderr = run_command("docker exec godeye-redis redis-cli ping")
    if success and "PONG" in stdout:
        print("   ✅ Redis - 连接正常")
    else:
        print("   ❌ Redis - 连接失败")
    
    # 检查端口状态
    print("\n4. 🌐 端口状态:")
    ports = [
        (8080, "Web服务"),
        (5432, "PostgreSQL"),
        (6379, "Redis")
    ]
    
    for port, service in ports:
        success, stdout, stderr = run_command(f"lsof -i :{port}")
        if success and stdout.strip():
            print(f"   ✅ 端口 {port} ({service}) - 已占用")
        else:
            print(f"   ❌ 端口 {port} ({service}) - 未占用")
    
    # 检查Web服务
    print("\n5. 🌍 Web服务验证:")
    try:
        response = requests.get("http://localhost:8080/index.html", timeout=5)
        if response.status_code == 200:
            print("   ✅ Web服务 - 响应正常")
            print(f"   📄 页面大小: {len(response.text)} 字符")
        else:
            print(f"   ⚠️ Web服务 - 响应异常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"   ❌ Web服务 - 无法访问: {e}")
    
    # 系统总结
    print("\n" + "=" * 50)
    print("📊 系统总结:")
    print("   🔧 架构: 简化版 Docker Compose")
    print("   🗄️ 数据库: PostgreSQL + Redis")
    print("   🌐 前端: 静态HTML (Python HTTP服务器)")
    print("   🔗 访问地址: http://localhost:8080")
    print("   👤 默认用户: admin / admin123")
    
    print("\n🎯 下一步建议:")
    print("   1. 访问 http://localhost:8080 查看系统界面")
    print("   2. 完善Go后端服务构建")
    print("   3. 集成前端React应用")
    print("   4. 配置监控和告警功能")
    
    print("\n✨ 当前状态: 基础服务已启动，可进行功能验证")
    print("=" * 50)

if __name__ == "__main__":
    check_service_status()
