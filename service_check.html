<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GodEye 服务状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 GodEye 服务状态检查</h1>
        
        <div class="status info">
            <strong>检查时间:</strong> <span id="checkTime"></span>
        </div>
        
        <div class="status" id="overallStatus">
            <strong>整体状态:</strong> 检查中...
        </div>
        
        <h2>🧪 服务测试</h2>
        <button onclick="checkHealth()">健康检查</button>
        <button onclick="testLogin()">登录测试</button>
        <button onclick="testAPI()">API测试</button>
        <button onclick="checkAllPorts()">端口检查</button>
        
        <h2>📊 测试结果</h2>
        <div id="results"></div>
        
        <h2>📋 详细日志</h2>
        <div class="log" id="logs"></div>
        
        <h2>🔧 快速操作</h2>
        <button onclick="openGodEye()">打开 GodEye (端口80)</button>
        <button onclick="openPort8080()">打开端口8080</button>
        <button onclick="openPort3000()">打开端口3000</button>
    </div>

    <script>
        let logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logs.push(logMessage);
            
            const logElement = document.getElementById('logs');
            logElement.textContent = logs.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logMessage);
        }
        
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('overallStatus');
            statusElement.className = `status ${type}`;
            statusElement.innerHTML = `<strong>整体状态:</strong> ${message}`;
        }
        
        function addResult(title, status, details = '') {
            const resultsElement = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${status}`;
            resultDiv.innerHTML = `<strong>${title}:</strong> ${details}`;
            resultsElement.appendChild(resultDiv);
        }
        
        async function checkHealth() {
            log('开始健康检查...');
            try {
                const response = await fetch('http://localhost:80/health');
                if (response.ok) {
                    const data = await response.text();
                    log('健康检查成功: ' + data);
                    addResult('健康检查', 'success', '服务正常运行');
                    return true;
                } else {
                    log('健康检查失败: HTTP ' + response.status);
                    addResult('健康检查', 'error', `HTTP ${response.status}`);
                    return false;
                }
            } catch (error) {
                log('健康检查异常: ' + error.message);
                addResult('健康检查', 'error', '连接失败: ' + error.message);
                return false;
            }
        }
        
        async function testLogin() {
            log('开始登录测试...');
            try {
                const response = await fetch('http://localhost:80/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.code === 200 && data.data && data.data.token) {
                        log('登录测试成功，获取到Token');
                        addResult('登录测试', 'success', '认证系统正常');
                        return data.data.token;
                    } else {
                        log('登录测试失败: ' + JSON.stringify(data));
                        addResult('登录测试', 'error', '认证失败');
                        return null;
                    }
                } else {
                    log('登录测试失败: HTTP ' + response.status);
                    addResult('登录测试', 'error', `HTTP ${response.status}`);
                    return null;
                }
            } catch (error) {
                log('登录测试异常: ' + error.message);
                addResult('登录测试', 'error', '连接失败: ' + error.message);
                return null;
            }
        }
        
        async function testAPI() {
            log('开始API测试...');
            const token = await testLogin();
            if (!token) {
                addResult('API测试', 'error', '无法获取认证Token');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:80/api/auth/user', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('API测试成功: ' + JSON.stringify(data));
                    addResult('API测试', 'success', '用户信息获取正常');
                } else {
                    log('API测试失败: HTTP ' + response.status);
                    addResult('API测试', 'error', `HTTP ${response.status}`);
                }
            } catch (error) {
                log('API测试异常: ' + error.message);
                addResult('API测试', 'error', '连接失败: ' + error.message);
            }
        }
        
        async function checkPort(port) {
            try {
                const response = await fetch(`http://localhost:${port}/health`);
                return response.ok;
            } catch {
                return false;
            }
        }
        
        async function checkAllPorts() {
            log('开始端口检查...');
            const ports = [80, 3000, 8080];
            
            for (const port of ports) {
                const isAvailable = await checkPort(port);
                if (isAvailable) {
                    log(`端口 ${port} 可用`);
                    addResult(`端口 ${port}`, 'success', '服务响应正常');
                } else {
                    log(`端口 ${port} 不可用`);
                    addResult(`端口 ${port}`, 'error', '无响应');
                }
            }
        }
        
        function openGodEye() {
            window.open('http://localhost:80', '_blank');
        }
        
        function openPort8080() {
            window.open('http://localhost:8080', '_blank');
        }
        
        function openPort3000() {
            window.open('http://localhost:3000', '_blank');
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            document.getElementById('checkTime').textContent = new Date().toLocaleString();
            log('页面加载完成，开始自动检查...');
            
            setTimeout(async () => {
                const healthOk = await checkHealth();
                if (healthOk) {
                    updateStatus('服务正常运行', 'success');
                    await testAPI();
                } else {
                    updateStatus('服务未运行或有问题', 'error');
                    await checkAllPorts();
                }
            }, 1000);
        };
    </script>
</body>
</html>
