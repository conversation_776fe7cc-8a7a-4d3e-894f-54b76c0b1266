package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"godeye/internal/app"
	"godeye/internal/config"
	"godeye/internal/database"
	"godeye/internal/scheduler"
)

func main() {
	var command string
	if len(os.Args) > 1 {
		command = os.Args[1]
	}

	switch command {
	case "migrate":
		runMigrations()
	case "server":
		runServer()
	default:
		fmt.Println("Usage: ./main [migrate|server]")
		os.Exit(1)
	}
}

func runMigrations() {
	fmt.Println("运行数据库迁移...")
	
	cfg := config.Load()
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	defer db.Close()

	if err := database.Migrate(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	fmt.Println("数据库迁移完成")
}

func runServer() {
	fmt.Println("启动GodEye服务器...")
	
	cfg := config.Load()
	
	// 连接数据库
	db, err := database.Connect(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	defer db.Close()

	// 创建应用实例
	application := app.New(cfg, db)
	
	// 启动定时任务调度器
	scheduler := scheduler.New(db, cfg)
	go scheduler.Start()
	defer scheduler.Stop()

	// 启动HTTP服务器
	fmt.Printf("服务器启动在端口 %s\n", cfg.Port)
	if err := application.Run(); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}
