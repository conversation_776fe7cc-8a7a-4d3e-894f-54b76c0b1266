#!/bin/bash

echo "🔍 GodEye 快速功能验证"
echo "======================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试结果
PASS_COUNT=0
FAIL_COUNT=0

test_result() {
    local name="$1"
    local result="$2"
    local details="$3"
    
    if [ "$result" = "PASS" ]; then
        echo -e "✅ ${GREEN}PASS${NC}: $name"
        PASS_COUNT=$((PASS_COUNT + 1))
    else
        echo -e "❌ ${RED}FAIL${NC}: $name"
        if [ -n "$details" ]; then
            echo -e "   ${YELLOW}详情${NC}: $details"
        fi
        FAIL_COUNT=$((FAIL_COUNT + 1))
    fi
}

echo -e "${BLUE}1. 检查服务状态${NC}"
echo "-------------------"

# 检查前端服务 (3000)
FRONTEND_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null || echo "000")
if [ "$FRONTEND_STATUS" = "200" ]; then
    test_result "前端服务 (3000)" "PASS"
else
    test_result "前端服务 (3000)" "FAIL" "状态码: $FRONTEND_STATUS"
fi

# 检查Nginx网关 (8080)
NGINX_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 2>/dev/null || echo "000")
if [ "$NGINX_STATUS" = "200" ]; then
    test_result "Nginx网关 (8080)" "PASS"
else
    test_result "Nginx网关 (8080)" "FAIL" "状态码: $NGINX_STATUS"
fi

# 检查认证服务
AUTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081/health 2>/dev/null || echo "000")
if [ "$AUTH_STATUS" = "200" ]; then
    test_result "认证服务 (8081)" "PASS"
else
    test_result "认证服务 (8081)" "FAIL" "状态码: $AUTH_STATUS"
fi

# 检查监控服务
MONITOR_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8082/health 2>/dev/null || echo "000")
if [ "$MONITOR_STATUS" = "200" ]; then
    test_result "监控服务 (8082)" "PASS"
else
    test_result "监控服务 (8082)" "FAIL" "状态码: $MONITOR_STATUS"
fi

# 检查通知服务
NOTIFICATION_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/health 2>/dev/null || echo "000")
if [ "$NOTIFICATION_STATUS" = "200" ]; then
    test_result "通知服务 (8083)" "PASS"
else
    test_result "通知服务 (8083)" "FAIL" "状态码: $NOTIFICATION_STATUS"
fi

echo
echo -e "${BLUE}2. 测试认证功能${NC}"
echo "-------------------"

# 测试登录
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"admin123"}' 2>/dev/null)

if echo "$LOGIN_RESPONSE" | jq -e '.success' > /dev/null 2>&1; then
    test_result "管理员登录" "PASS"
    TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.access_token' 2>/dev/null)
else
    test_result "管理员登录" "FAIL" "$(echo "$LOGIN_RESPONSE" | jq -r '.message // "响应格式错误"' 2>/dev/null || echo "无法解析响应")"
    TOKEN=""
fi

echo
echo -e "${BLUE}3. 测试API端点${NC}"
echo "-------------------"

if [ -n "$TOKEN" ]; then
    # 测试账号API
    ACCOUNTS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "Authorization: Bearer $TOKEN" \
        http://localhost:8080/api/accounts 2>/dev/null || echo "000")
    
    if [ "$ACCOUNTS_STATUS" = "200" ]; then
        test_result "账号管理API" "PASS"
    else
        test_result "账号管理API" "FAIL" "状态码: $ACCOUNTS_STATUS"
    fi
    
    # 测试通知API
    NOTIFICATIONS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "Authorization: Bearer $TOKEN" \
        http://localhost:8080/api/notifications/channels 2>/dev/null || echo "000")
    
    if [ "$NOTIFICATIONS_STATUS" = "200" ]; then
        test_result "通知配置API" "PASS"
    else
        test_result "通知配置API" "FAIL" "状态码: $NOTIFICATIONS_STATUS"
    fi
    
    # 测试监控API
    MONITORS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "Authorization: Bearer $TOKEN" \
        http://localhost:8080/api/monitors 2>/dev/null || echo "000")
    
    if [ "$MONITORS_STATUS" = "200" ]; then
        test_result "监控管理API" "PASS"
    else
        test_result "监控管理API" "FAIL" "状态码: $MONITORS_STATUS"
    fi
else
    test_result "API测试" "FAIL" "无法获取认证token"
fi

echo
echo -e "${BLUE}4. 测试关键页面${NC}"
echo "-------------------"

# 测试关键页面通过nginx访问
PAGES=(
    "/:首页"
    "/login:登录页"
    "/dashboard:仪表板"
    "/accounts:账号管理"
    "/notifications:通知配置"
)

for page in "${PAGES[@]}"; do
    IFS=':' read -r path name <<< "$page"
    
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8080$path" 2>/dev/null || echo "000")
    
    if [ "$STATUS" = "200" ]; then
        test_result "页面 $name" "PASS"
    else
        test_result "页面 $name" "FAIL" "状态码: $STATUS"
    fi
done

echo
echo "======================"
echo -e "${BLUE}📊 测试结果总结${NC}"
echo "======================"
echo -e "通过: ${GREEN}$PASS_COUNT${NC}"
echo -e "失败: ${RED}$FAIL_COUNT${NC}"
echo -e "总计: $(($PASS_COUNT + $FAIL_COUNT))"

if [ $FAIL_COUNT -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    echo
    echo -e "${BLUE}✅ 系统访问信息:${NC}"
    echo "- 主要访问地址: http://localhost:8080"
    echo "- 前端开发服务器: http://localhost:3000"
    echo "- 默认登录账户: <EMAIL> / admin123"
    echo
    echo -e "${BLUE}📋 已修复的问题:${NC}"
    echo "1. ✅ 侧边栏导航菜单添加了'通知配置'选项"
    echo "2. ✅ 通知主页面的'新建通知配置'按钮链接到配置页面"
    echo "3. ✅ 端口配置说明：用户通过8080访问，前端开发在3000"
    exit 0
else
    echo -e "${YELLOW}⚠️  发现 $FAIL_COUNT 个问题需要解决${NC}"
    exit 1
fi
