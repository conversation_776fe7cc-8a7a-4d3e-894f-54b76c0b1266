#!/bin/bash

# GodEye前端页面功能测试脚本
# 测试所有前端页面的可访问性和基本功能

echo "=== GodEye前端页面功能测试 ==="
echo "测试时间: $(date)"
echo ""

BASE_URL="http://localhost:3000"
TIMEOUT=10

# 测试页面可访问性
test_page() {
    local url=$1
    local page_name=$2
    
    echo -n "测试 $page_name ($url): "
    
    # 使用curl测试页面是否可访问
    response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url")
    
    if [ "$response" = "200" ]; then
        echo "✅ 正常"
        return 0
    else
        echo "❌ 失败 (HTTP $response)"
        return 1
    fi
}

# 测试API端点
test_api() {
    local url=$1
    local api_name=$2
    local token=$3
    
    echo -n "测试 $api_name API ($url): "
    
    if [ -n "$token" ]; then
        response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT -H "Authorization: Bearer $token" "$url")
    else
        response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url")
    fi
    
    if [ "$response" = "200" ]; then
        echo "✅ 正常"
        return 0
    else
        echo "❌ 失败 (HTTP $response)"
        return 1
    fi
}

# 检查前端服务是否运行
echo "1. 检查前端服务状态"
if ! curl -s --max-time 5 "$BASE_URL" > /dev/null; then
    echo "❌ 前端服务未运行，请先启动服务"
    exit 1
fi
echo "✅ 前端服务正常运行"
echo ""

# 测试主要页面
echo "2. 测试主要页面可访问性"
passed=0
total=0

# 主页和认证页面
test_page "$BASE_URL" "首页" && ((passed++))
((total++))

test_page "$BASE_URL/login" "登录页面" && ((passed++))
((total++))

# 主要功能页面
test_page "$BASE_URL/dashboard" "仪表板" && ((passed++))
((total++))

test_page "$BASE_URL/monitors" "监控任务" && ((passed++))
((total++))

test_page "$BASE_URL/results" "扫描结果" && ((passed++))
((total++))

test_page "$BASE_URL/accounts" "平台账号" && ((passed++))
((total++))

test_page "$BASE_URL/whitelist" "白名单管理" && ((passed++))
((total++))

test_page "$BASE_URL/notifications" "通知设置" && ((passed++))
((total++))

test_page "$BASE_URL/global-search" "全局搜索" && ((passed++))
((total++))

test_page "$BASE_URL/settings" "系统设置" && ((passed++))
((total++))

echo ""
echo "页面测试结果: $passed/$total 通过"
echo ""

# 测试API端点 (需要JWT token)
echo "3. 测试API端点"

# 获取JWT token (使用测试账号)
echo "获取JWT token..."
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$TOKEN_RESPONSE" | grep -q "access_token"; then
    TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "✅ JWT token获取成功"
else
    echo "❌ JWT token获取失败"
    TOKEN=""
fi

api_passed=0
api_total=0

# 测试主要API端点
test_api "http://localhost:8080/api/monitors" "监控任务" "$TOKEN" && ((api_passed++))
((api_total++))

test_api "http://localhost:8080/api/accounts" "平台账号" "$TOKEN" && ((api_passed++))
((api_total++))

test_api "http://localhost:8080/api/v1/whitelist" "白名单" "$TOKEN" && ((api_passed++))
((api_total++))

test_api "http://localhost:8080/api/results" "扫描结果" "$TOKEN" && ((api_passed++))
((api_total++))

test_api "http://localhost:8080/api/v1/dashboard/stats" "仪表板统计" "$TOKEN" && ((api_passed++))
((api_total++))

echo ""
echo "API测试结果: $api_passed/$api_total 通过"
echo ""

# 计算总体结果
total_tests=$((total + api_total))
total_passed=$((passed + api_passed))
success_rate=$(echo "scale=1; $total_passed * 100 / $total_tests" | bc -l)

echo "=== 测试总结 ==="
echo "总测试项: $total_tests"
echo "通过测试: $total_passed"
echo "失败测试: $((total_tests - total_passed))"
echo "成功率: ${success_rate}%"

if [ "$total_passed" -eq "$total_tests" ]; then
    echo "🎉 所有测试通过！"
    exit 0
else
    echo "⚠️  部分测试失败，请检查相关服务"
    exit 1
fi
