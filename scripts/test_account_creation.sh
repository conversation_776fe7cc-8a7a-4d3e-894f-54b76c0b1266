#!/bin/bash

echo "=== GodEye 账号添加功能测试 ==="
echo

# 1. 登录获取token
echo "1. 登录系统..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"admin123"}')

echo "登录响应: $LOGIN_RESPONSE"

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.access_token')
if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
    echo "❌ 登录失败，无法获取token"
    exit 1
fi

echo "✅ 登录成功，Token: ${TOKEN:0:20}..."
echo

# 2. 测试账号创建 - 模拟前端请求
echo "2. 测试账号创建（模拟前端请求）..."
ACCOUNT_DATA='{
    "platform": "github",
    "username": "lx277856602",
    "token": "*********************************************************************************************",
    "account_type": "personal",
    "description": "Frontend test account",
    "is_active": true,
    "priority": 1,
    "rate_limit": 5000,
    "rate_remaining": 5000
}'

CREATE_RESPONSE=$(curl -s -X POST http://localhost:8080/api/accounts \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "$ACCOUNT_DATA")

echo "创建响应: $CREATE_RESPONSE"

# 检查是否成功
if echo "$CREATE_RESPONSE" | jq -e '.account.id' > /dev/null; then
    ACCOUNT_ID=$(echo "$CREATE_RESPONSE" | jq -r '.account.id')
    echo "✅ 账号创建成功，ID: $ACCOUNT_ID"
else
    echo "❌ 账号创建失败"
    echo "错误信息: $(echo "$CREATE_RESPONSE" | jq -r '.error // "未知错误"')"
fi
echo

# 3. 获取账号列表验证
echo "3. 获取账号列表验证..."
LIST_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/accounts)

ACCOUNT_COUNT=$(echo "$LIST_RESPONSE" | jq '.accounts | length')
echo "当前账号总数: $ACCOUNT_COUNT"

if [ "$ACCOUNT_COUNT" -gt 0 ]; then
    echo "✅ 账号列表获取成功"
    echo "最新账号:"
    echo "$LIST_RESPONSE" | jq '.accounts[-1] | {id, platform, username, status, created_at}'
else
    echo "❌ 账号列表为空"
fi
echo

# 4. 测试前端API格式兼容性
echo "4. 测试前端API格式兼容性..."
FRONTEND_DATA='{
    "platform": "github",
    "account_type": "personal",
    "username": "test-frontend",
    "token": "*********************************************************************************************",
    "description": "Frontend compatibility test"
}'

FRONTEND_RESPONSE=$(curl -s -X POST http://localhost:8080/api/accounts \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "$FRONTEND_DATA")

echo "前端格式响应: $FRONTEND_RESPONSE"

if echo "$FRONTEND_RESPONSE" | jq -e '.account.id' > /dev/null; then
    echo "✅ 前端API格式兼容"
else
    echo "❌ 前端API格式不兼容"
    echo "错误: $(echo "$FRONTEND_RESPONSE" | jq -r '.error // "未知错误"')"
fi
echo

# 5. 测试通知渠道API
echo "5. 测试通知渠道API..."
CHANNELS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/notifications/channels)

echo "通知渠道响应: $CHANNELS_RESPONSE"

if echo "$CHANNELS_RESPONSE" | jq -e '.channels' > /dev/null; then
    CHANNEL_COUNT=$(echo "$CHANNELS_RESPONSE" | jq '.channels | length')
    echo "✅ 通知渠道API正常，当前渠道数: $CHANNEL_COUNT"
else
    echo "❌ 通知渠道API异常"
fi
echo

echo "=== 测试完成 ==="
echo
echo "📊 测试结果总结:"
echo "- 登录功能: ✅"
echo "- 账号创建: $(if echo "$CREATE_RESPONSE" | jq -e '.account.id' > /dev/null; then echo "✅"; else echo "❌"; fi)"
echo "- 账号列表: ✅"
echo "- 前端兼容: $(if echo "$FRONTEND_RESPONSE" | jq -e '.account.id' > /dev/null; then echo "✅"; else echo "❌"; fi)"
echo "- 通知API: $(if echo "$CHANNELS_RESPONSE" | jq -e '.channels' > /dev/null; then echo "✅"; else echo "❌"; fi)"
echo

if echo "$CREATE_RESPONSE" | jq -e '.account.id' > /dev/null && echo "$FRONTEND_RESPONSE" | jq -e '.account.id' > /dev/null; then
    echo "🎉 所有核心功能测试通过！"
    exit 0
else
    echo "⚠️  部分功能存在问题，需要进一步调试"
    exit 1
fi
