#!/bin/bash

# GodEye 告警功能验证脚本
# 验证企业微信和飞书告警功能的实现

echo "🔍 GodEye 告警功能验证"
echo "======================="

# 检查文件是否存在
echo "📁 检查核心文件..."

files=(
    "services/notification/internal/webhook/service.go"
    "services/notification/internal/services/notification_service.go"
    "services/notification/test/webhook_test.go"
    "docs/功能对比与实施计划.md"
    "docs/告警功能使用指南.md"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
    fi
done

echo ""
echo "🔧 检查企业微信告警实现..."

# 检查企业微信相关代码
if grep -q "WeChatWorkMessage" services/notification/internal/webhook/service.go; then
    echo "✅ WeChatWorkMessage 结构体已定义"
else
    echo "❌ WeChatWorkMessage 结构体缺失"
fi

if grep -q "CreateWeChatWorkScanAlert" services/notification/internal/webhook/service.go; then
    echo "✅ CreateWeChatWorkScanAlert 函数已实现"
else
    echo "❌ CreateWeChatWorkScanAlert 函数缺失"
fi

if grep -q "SendWeChatWorkMessage" services/notification/internal/webhook/service.go; then
    echo "✅ SendWeChatWorkMessage 函数已实现"
else
    echo "❌ SendWeChatWorkMessage 函数缺失"
fi

if grep -q "qyapi.weixin.qq.com" services/notification/internal/webhook/service.go; then
    echo "✅ 企业微信域名识别已实现"
else
    echo "❌ 企业微信域名识别缺失"
fi

if grep -q "wechatwork" services/notification/internal/services/notification_service.go; then
    echo "✅ 企业微信告警集成已完成"
else
    echo "❌ 企业微信告警集成缺失"
fi

echo ""
echo "🚀 检查飞书告警实现..."

# 检查飞书相关代码
if grep -q "FeishuMessage" services/notification/internal/webhook/service.go; then
    echo "✅ FeishuMessage 结构体已定义"
else
    echo "❌ FeishuMessage 结构体缺失"
fi

if grep -q "CreateFeishuScanAlert" services/notification/internal/webhook/service.go; then
    echo "✅ CreateFeishuScanAlert 函数已实现"
else
    echo "❌ CreateFeishuScanAlert 函数缺失"
fi

if grep -q "SendFeishuMessage" services/notification/internal/webhook/service.go; then
    echo "✅ SendFeishuMessage 函数已实现"
else
    echo "❌ SendFeishuMessage 函数缺失"
fi

if grep -q "open.feishu.cn" services/notification/internal/webhook/service.go; then
    echo "✅ 飞书域名识别已实现"
else
    echo "❌ 飞书域名识别缺失"
fi

if grep -q "feishu" services/notification/internal/services/notification_service.go; then
    echo "✅ 飞书告警集成已完成"
else
    echo "❌ 飞书告警集成缺失"
fi

echo ""
echo "📋 检查测试文件..."

if grep -q "TestWebhookService_ParseWebhookType" services/notification/test/webhook_test.go; then
    echo "✅ Webhook类型解析测试已创建"
else
    echo "❌ Webhook类型解析测试缺失"
fi

if grep -q "TestWebhookService_CreateScanAlerts" services/notification/test/webhook_test.go; then
    echo "✅ 告警消息创建测试已创建"
else
    echo "❌ 告警消息创建测试缺失"
fi

echo ""
echo "📚 检查文档..."

if grep -q "企业微信告警.*已实现" docs/功能对比与实施计划.md; then
    echo "✅ 功能对比文档已更新（企业微信）"
else
    echo "❌ 功能对比文档需要更新（企业微信）"
fi

if grep -q "飞书告警.*已实现" docs/功能对比与实施计划.md; then
    echo "✅ 功能对比文档已更新（飞书）"
else
    echo "❌ 功能对比文档需要更新（飞书）"
fi

if [ -f "docs/告警功能使用指南.md" ]; then
    echo "✅ 告警功能使用指南已创建"
else
    echo "❌ 告警功能使用指南缺失"
fi

echo ""
echo "🎯 功能兼容性检查..."

# 检查与Hawkeye的兼容性
echo "检查与Hawkeye项目的功能兼容性："

hawkeye_features=(
    "GitHub代码监控"
    "多账号轮换"
    "企业微信告警"
    "钉钉告警"
    "邮件告警"
    "关键词搜索"
)

godeye_implementations=(
    "GitHub.*监控.*已实现"
    "多账号轮换.*已实现"
    "企业微信告警.*已实现"
    "钉钉告警.*已实现"
    "邮件告警.*已实现"
    "关键词搜索.*已实现"
)

for i in "${!hawkeye_features[@]}"; do
    feature="${hawkeye_features[$i]}"
    pattern="${godeye_implementations[$i]}"
    
    if grep -q "$pattern" docs/功能对比与实施计划.md; then
        echo "✅ $feature - 已兼容"
    else
        echo "⚠️  $feature - 需要验证"
    fi
done

echo ""
echo "🆕 GodEye特色功能检查..."

godeye_features=(
    "GitLab代码监控"
    "Gitee代码监控"
    "飞书告警"
    "全量关键词搜索"
    "微服务架构"
)

for feature in "${godeye_features[@]}"; do
    if grep -q "$feature.*已实现\|$feature.*新增特色" docs/功能对比与实施计划.md; then
        echo "✅ $feature - 已实现"
    else
        echo "⚠️  $feature - 需要验证"
    fi
done

echo ""
echo "📊 验证总结"
echo "============"

# 统计实现情况
total_checks=0
passed_checks=0

# 核心文件检查
for file in "${files[@]}"; do
    total_checks=$((total_checks + 1))
    if [ -f "$file" ]; then
        passed_checks=$((passed_checks + 1))
    fi
done

# 功能实现检查
feature_checks=(
    "WeChatWorkMessage"
    "CreateWeChatWorkScanAlert"
    "SendWeChatWorkMessage"
    "qyapi.weixin.qq.com"
    "FeishuMessage"
    "CreateFeishuScanAlert"
    "SendFeishuMessage"
    "open.feishu.cn"
)

for check in "${feature_checks[@]}"; do
    total_checks=$((total_checks + 1))
    if grep -q "$check" services/notification/internal/webhook/service.go; then
        passed_checks=$((passed_checks + 1))
    fi
done

# 集成检查
integration_checks=("wechatwork" "feishu")
for check in "${integration_checks[@]}"; do
    total_checks=$((total_checks + 1))
    if grep -q "$check" services/notification/internal/services/notification_service.go; then
        passed_checks=$((passed_checks + 1))
    fi
done

success_rate=$((passed_checks * 100 / total_checks))

echo "总检查项: $total_checks"
echo "通过检查: $passed_checks"
echo "成功率: $success_rate%"

if [ $success_rate -ge 90 ]; then
    echo "🎉 验证结果: 优秀 - 功能实现完整"
elif [ $success_rate -ge 80 ]; then
    echo "✅ 验证结果: 良好 - 主要功能已实现"
elif [ $success_rate -ge 70 ]; then
    echo "⚠️  验证结果: 一般 - 部分功能需要完善"
else
    echo "❌ 验证结果: 需要改进 - 多项功能缺失"
fi

echo ""
echo "🚀 下一步建议："
echo "1. 启动Docker服务进行端到端测试"
echo "2. 配置实际的Webhook URL进行真实环境测试"
echo "3. 验证告警消息格式和内容"
echo "4. 测试不同风险级别的告警效果"
echo "5. 验证与前端的集成"

echo ""
echo "验证完成！"
