#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}🚀 启动 GodEye 开发环境${NC}"
echo ""

# 检查 Docker 服务
if ! docker info &> /dev/null; then
    log_error "Docker 服务未启动，请启动 Docker Desktop"
    exit 1
fi

# 检查环境配置文件
if [ ! -f .env.dev ]; then
    log_warning ".env.dev 文件不存在，请先运行 ./scripts/setup-dev.sh"
    exit 1
fi

# 加载环境变量
log_info "📋 加载环境配置..."
export $(cat .env.dev | grep -v '^#' | xargs)

# 检查必要的环境变量
if [ "$GITHUB_TOKEN" = "your_github_token_here" ]; then
    log_warning "请在 .env.dev 文件中配置您的 GitHub Token"
    log_info "GitHub Token 获取地址: https://github.com/settings/tokens"
fi

# 停止可能运行的容器
log_info "🛑 停止现有容器..."
docker-compose -f docker-compose.dev.yml down --remove-orphans 2>/dev/null || true

# 清理悬挂的镜像和网络
log_info "🧹 清理 Docker 资源..."
docker system prune -f --volumes 2>/dev/null || true

# 启动基础服务
log_info "🔧 启动基础服务 (数据库、缓存、搜索)..."
docker-compose -f docker-compose.dev.yml up -d postgres-dev redis-dev elasticsearch-dev rabbitmq-dev

# 等待基础服务启动
log_info "⏳ 等待基础服务启动..."
sleep 30

# 检查基础服务健康状态
log_info "🔍 检查基础服务状态..."

# 检查 PostgreSQL
if docker-compose -f docker-compose.dev.yml exec -T postgres-dev pg_isready -U godeye_dev -d godeye_dev &> /dev/null; then
    log_success "PostgreSQL 服务正常"
else
    log_error "PostgreSQL 服务启动失败"
    docker-compose -f docker-compose.dev.yml logs postgres-dev
fi

# 检查 Redis
if docker-compose -f docker-compose.dev.yml exec -T redis-dev redis-cli ping &> /dev/null; then
    log_success "Redis 服务正常"
else
    log_error "Redis 服务启动失败"
    docker-compose -f docker-compose.dev.yml logs redis-dev
fi

# 启动应用服务
log_info "🎯 启动应用服务..."
docker-compose -f docker-compose.dev.yml up -d

echo ""
log_info "⏳ 等待应用服务启动..."
sleep 60

# 健康检查
log_info "🔍 检查服务状态..."

# 检查 API 网关
if curl -f http://localhost:8080/health &> /dev/null; then
    log_success "API 网关正常"
else
    log_warning "API 网关可能未完全启动"
fi

# 检查前端服务
if curl -f http://localhost:3000 &> /dev/null; then
    log_success "前端服务正常"
else
    log_warning "前端服务可能未完全启动"
fi

echo ""
log_success "✅ GodEye 开发环境启动完成!"
echo ""
echo -e "${YELLOW}📋 服务地址:${NC}"
echo "  🌐 前端开发服务: http://localhost:3000"
echo "  🔧 API 网关: http://localhost:8080"
echo "  🔐 认证服务: http://localhost:8081"
echo "  📊 监控服务: http://localhost:8082"
echo "  📢 通知服务: http://localhost:8083"
echo "  🗄️  数据库: localhost:5433"
echo "  🔴 Redis: localhost:6380"
echo "  🔍 Elasticsearch: localhost:9201"
echo "  🐰 RabbitMQ 管理: http://localhost:15673"
echo ""
echo -e "${YELLOW}🔧 常用命令:${NC}"
echo "  查看所有服务状态: docker-compose -f docker-compose.dev.yml ps"
echo "  查看服务日志: docker-compose -f docker-compose.dev.yml logs -f [service]"
echo "  重启服务: docker-compose -f docker-compose.dev.yml restart [service]"
echo "  停止环境: ./scripts/stop-dev.sh"
echo "  清理环境: ./scripts/clean-dev.sh"
echo ""
echo -e "${BLUE}💡 提示:${NC}"
echo "  - 代码修改后会自动重载 (热重载)"
echo "  - 使用 VSCode 打开项目获得最佳开发体验"
echo "  - 查看开发文档: ./docs/development/"
