#!/bin/bash

echo "🔍 GodEye 系统全面功能测试套件"
echo "=================================="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试结果记录
test_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$result" = "PASS" ]; then
        echo -e "  ✅ ${GREEN}PASS${NC}: $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "  ❌ ${RED}FAIL${NC}: $test_name"
        if [ -n "$details" ]; then
            echo -e "     ${YELLOW}详情${NC}: $details"
        fi
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 检查服务状态
check_service_status() {
    echo -e "${BLUE}📊 检查服务运行状态${NC}"
    echo "----------------------------"
    
    # 检查Docker容器状态
    echo "Docker容器状态:"
    docker-compose -f docker-compose.dev.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
    echo
    
    # 检查各服务健康状态
    services=("auth-service-dev:8081" "monitor-service-dev:8082" "notification-service-dev:8083" "frontend-dev:3000" "nginx-dev:8080")
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        status_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$port/health" 2>/dev/null || echo "000")
        
        if [ "$status_code" = "200" ]; then
            test_result "$name 健康检查" "PASS"
        else
            test_result "$name 健康检查" "FAIL" "状态码: $status_code"
        fi
    done
    echo
}

# 测试认证系统
test_authentication() {
    echo -e "${BLUE}🔐 测试认证系统${NC}"
    echo "----------------------------"
    
    # 测试登录
    login_response=$(curl -s -X POST http://localhost:8080/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"admin123"}')
    
    if echo "$login_response" | jq -e '.success' > /dev/null 2>&1; then
        test_result "管理员登录" "PASS"
        TOKEN=$(echo "$login_response" | jq -r '.data.access_token')
    else
        test_result "管理员登录" "FAIL" "$(echo "$login_response" | jq -r '.message // "未知错误"')"
        return 1
    fi
    
    # 测试错误密码
    wrong_login=$(curl -s -X POST http://localhost:8080/api/auth/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"wrongpassword"}')
    
    if echo "$wrong_login" | jq -e '.success == false' > /dev/null 2>&1; then
        test_result "错误密码拒绝" "PASS"
    else
        test_result "错误密码拒绝" "FAIL" "应该拒绝错误密码"
    fi
    
    # 测试token验证
    user_info=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/auth/user)
    if echo "$user_info" | jq -e '.data.user.email' > /dev/null 2>&1; then
        test_result "Token验证" "PASS"
    else
        test_result "Token验证" "FAIL" "无法获取用户信息"
    fi
    
    echo
}

# 测试账号管理
test_account_management() {
    echo -e "${BLUE}👥 测试账号管理${NC}"
    echo "----------------------------"
    
    if [ -z "$TOKEN" ]; then
        test_result "账号管理测试" "FAIL" "需要先登录"
        return 1
    fi
    
    # 测试获取账号列表
    accounts_response=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/accounts)
    if echo "$accounts_response" | jq -e '.accounts' > /dev/null 2>&1; then
        test_result "获取账号列表" "PASS"
        account_count=$(echo "$accounts_response" | jq '.accounts | length')
        echo "    当前账号数量: $account_count"
    else
        test_result "获取账号列表" "FAIL" "无法获取账号列表"
    fi
    
    # 测试创建账号（使用测试token）
    create_response=$(curl -s -X POST http://localhost:8080/api/accounts \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{
            "platform": "github",
            "username": "test-comprehensive",
            "token": "github_pat_test_token_for_testing",
            "account_type": "personal",
            "description": "Comprehensive test account"
        }')
    
    # 注意：这个测试可能会失败，因为token是假的，但我们检查API是否正确处理
    if echo "$create_response" | jq -e '.account.id' > /dev/null 2>&1; then
        test_result "创建账号（有效token）" "PASS"
        TEST_ACCOUNT_ID=$(echo "$create_response" | jq -r '.account.id')
    elif echo "$create_response" | jq -e '.error' | grep -q "validation failed" 2>/dev/null; then
        test_result "创建账号（token验证）" "PASS" "正确拒绝无效token"
    else
        test_result "创建账号" "FAIL" "$(echo "$create_response" | jq -r '.error // "未知错误"')"
    fi
    
    echo
}

# 测试监控功能
test_monitoring() {
    echo -e "${BLUE}🔍 测试监控功能${NC}"
    echo "----------------------------"
    
    if [ -z "$TOKEN" ]; then
        test_result "监控功能测试" "FAIL" "需要先登录"
        return 1
    fi
    
    # 测试监控任务列表
    monitors_response=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/monitors)
    status_code=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/monitors)
    
    if [ "$status_code" = "200" ]; then
        test_result "获取监控任务列表" "PASS"
    else
        test_result "获取监控任务列表" "FAIL" "状态码: $status_code"
    fi
    
    # 测试全局搜索API
    search_response=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/global-search)
    search_status=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/global-search)
    
    if [ "$search_status" = "200" ]; then
        test_result "全局搜索API" "PASS"
    else
        test_result "全局搜索API" "FAIL" "状态码: $search_status"
    fi
    
    # 测试白名单API
    whitelist_status=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/v1/whitelist)
    
    if [ "$whitelist_status" = "200" ]; then
        test_result "白名单API" "PASS"
    else
        test_result "白名单API" "FAIL" "状态码: $whitelist_status"
    fi
    
    echo
}

# 测试通知系统
test_notification_system() {
    echo -e "${BLUE}🔔 测试通知系统${NC}"
    echo "----------------------------"
    
    if [ -z "$TOKEN" ]; then
        test_result "通知系统测试" "FAIL" "需要先登录"
        return 1
    fi
    
    # 测试通知渠道API
    channels_response=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:8080/api/notifications/channels)
    
    if echo "$channels_response" | jq -e '.items' > /dev/null 2>&1; then
        test_result "通知渠道API" "PASS"
        channel_count=$(echo "$channels_response" | jq '.items | length')
        echo "    当前通知渠道数量: $channel_count"
    else
        test_result "通知渠道API" "FAIL" "响应: $channels_response"
    fi
    
    # 测试创建通知渠道
    create_channel_response=$(curl -s -X POST http://localhost:8080/api/notifications/channels \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{
            "name": "测试邮件渠道",
            "type": "email",
            "config": {
                "host": "smtp.test.com",
                "port": 587,
                "username": "<EMAIL>",
                "password": "testpass"
            },
            "description": "测试用邮件通知渠道"
        }')
    
    create_status=$(curl -s -o /dev/null -w "%{http_code}" -X POST http://localhost:8080/api/notifications/channels \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d '{"name":"test","type":"email","config":{}}')
    
    if [ "$create_status" = "200" ] || [ "$create_status" = "201" ]; then
        test_result "创建通知渠道" "PASS"
    else
        test_result "创建通知渠道" "FAIL" "状态码: $create_status"
    fi
    
    echo
}

# 测试前端页面
test_frontend_pages() {
    echo -e "${BLUE}🌐 测试前端页面${NC}"
    echo "----------------------------"
    
    # 关键页面列表
    pages=(
        "/:首页"
        "/login:登录页"
        "/dashboard:仪表板"
        "/accounts:账号管理"
        "/accounts/add:添加账号"
        "/monitors:监控任务"
        "/global-search:全局搜索"
        "/results:扫描结果"
        "/notifications:通知配置"
    )
    
    for page in "${pages[@]}"; do
        IFS=':' read -r path name <<< "$page"
        
        # 通过nginx网关访问
        status_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8080$path" 2>/dev/null || echo "000")
        
        if [ "$status_code" = "200" ]; then
            test_result "前端页面 $name" "PASS"
        else
            # 如果nginx失败，尝试直接访问前端
            direct_status=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000$path" 2>/dev/null || echo "000")
            if [ "$direct_status" = "200" ]; then
                test_result "前端页面 $name (直接访问)" "PASS" "nginx路由可能有问题"
            else
                test_result "前端页面 $name" "FAIL" "nginx: $status_code, 直接: $direct_status"
            fi
        fi
    done
    
    echo
}

# 测试数据库连接
test_database_connectivity() {
    echo -e "${BLUE}🗄️ 测试数据库连接${NC}"
    echo "----------------------------"
    
    # 测试PostgreSQL
    pg_status=$(docker-compose -f docker-compose.dev.yml exec -T postgres-dev pg_isready -U godeye_dev -d godeye_dev 2>/dev/null)
    if echo "$pg_status" | grep -q "accepting connections"; then
        test_result "PostgreSQL连接" "PASS"
    else
        test_result "PostgreSQL连接" "FAIL" "$pg_status"
    fi
    
    # 测试Redis
    redis_status=$(docker-compose -f docker-compose.dev.yml exec -T redis-dev redis-cli ping 2>/dev/null)
    if [ "$redis_status" = "PONG" ]; then
        test_result "Redis连接" "PASS"
    else
        test_result "Redis连接" "FAIL" "$redis_status"
    fi
    
    # 测试Elasticsearch
    es_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:9201/_cluster/health 2>/dev/null)
    if [ "$es_status" = "200" ]; then
        test_result "Elasticsearch连接" "PASS"
    else
        test_result "Elasticsearch连接" "FAIL" "状态码: $es_status"
    fi
    
    echo
}

# 主测试流程
main() {
    echo -e "${BLUE}开始全面功能测试...${NC}"
    echo
    
    # 检查是否在正确的目录
    if [ ! -f "docker-compose.dev.yml" ]; then
        echo -e "${RED}错误: 请在godeye项目根目录运行此脚本${NC}"
        exit 1
    fi
    
    # 执行所有测试
    check_service_status
    test_database_connectivity
    test_authentication
    test_account_management
    test_monitoring
    test_notification_system
    test_frontend_pages
    
    # 输出测试总结
    echo "=================================="
    echo -e "${BLUE}📊 测试结果总结${NC}"
    echo "=================================="
    echo -e "总测试数: ${BLUE}$TOTAL_TESTS${NC}"
    echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败: ${RED}$FAILED_TESTS${NC}"
    echo -e "成功率: ${BLUE}$(( PASSED_TESTS * 100 / TOTAL_TESTS ))%${NC}"
    echo
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！系统功能完整！${NC}"
        exit 0
    else
        echo -e "${YELLOW}⚠️  发现 $FAILED_TESTS 个问题需要修复${NC}"
        exit 1
    fi
}

# 运行主函数
main "$@"
