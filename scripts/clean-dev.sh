#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}🧹 清理 GodEye 开发环境${NC}"
echo ""

# 确认操作
read -p "⚠️  这将删除所有开发数据，确定要继续吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "操作已取消"
    exit 0
fi

# 检查 Docker 服务
if ! docker info &> /dev/null; then
    log_warning "Docker 服务未运行"
    exit 0
fi

# 停止并删除容器和卷
log_info "🛑 停止并删除所有容器..."
docker-compose -f docker-compose.dev.yml down -v --remove-orphans

# 删除相关镜像
log_info "🗑️  删除相关镜像..."
docker images | grep godeye | awk '{print $3}' | xargs docker rmi -f 2>/dev/null || true

# 清理未使用的资源
log_info "🧽 清理未使用的 Docker 资源..."
docker system prune -f --volumes

# 清理构建缓存
log_info "🗂️  清理构建缓存..."
docker builder prune -f

echo ""
log_success "✅ GodEye 开发环境已完全清理"
echo ""
echo -e "${YELLOW}📋 已清理的内容:${NC}"
echo "  - 所有开发容器"
echo "  - 所有开发数据卷"
echo "  - 相关 Docker 镜像"
echo "  - 未使用的 Docker 资源"
echo "  - Docker 构建缓存"
echo ""
echo -e "${BLUE}💡 下次启动:${NC}"
echo "  ./scripts/start-dev.sh"
