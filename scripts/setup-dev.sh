#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}🚀 GodEye 开发环境初始化${NC}"
echo ""

# 检查 macOS 系统
if [[ "$OSTYPE" != "darwin"* ]]; then
    log_warning "此脚本专为 macOS 设计，其他系统可能需要调整"
fi

# 检查并安装依赖
log_info "📦 检查系统依赖..."

# 检查 Homebrew
if ! command -v brew &> /dev/null; then
    log_info "安装 Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
fi

# 检查 Docker Desktop
if ! command -v docker &> /dev/null; then
    log_error "请先安装 Docker Desktop for Mac"
    log_info "下载地址: https://www.docker.com/products/docker-desktop"
    exit 1
fi

# 检查 Docker 服务状态
if ! docker info &> /dev/null; then
    log_error "Docker 服务未启动，请启动 Docker Desktop"
    exit 1
fi

# 检查 Docker Compose
if ! command -v docker-compose &> /dev/null; then
    log_info "安装 Docker Compose..."
    brew install docker-compose
fi

log_success "系统依赖检查完成"

# 创建开发配置文件
log_info "⚙️  创建开发配置文件..."

# 创建 .env.dev 文件
if [ ! -f .env.dev ]; then
    cat > .env.dev << 'EOF'
# GodEye 开发环境配置
NODE_ENV=development
GO_ENV=development

# 数据库配置
DB_HOST=postgres-dev
DB_PORT=5432
DB_NAME=godeye_dev
DB_USER=godeye_dev
DB_PASSWORD=dev123

# Redis 配置
REDIS_HOST=redis-dev
REDIS_PORT=6379

# JWT 配置
JWT_SECRET=dev-jwt-secret-key-change-in-production

# GitHub API 配置 (请填写您的 GitHub Token)
GITHUB_TOKEN=your_github_token_here

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080

# 开发工具配置
HOT_RELOAD=true
DEBUG_MODE=true
LOG_LEVEL=debug
EOF
    log_success "已创建开发环境配置文件 .env.dev"
else
    log_info ".env.dev 文件已存在，跳过创建"
fi

# 创建 VSCode 工作区配置
log_info "🔧 创建 VSCode 工作区配置..."

mkdir -p .vscode

# VSCode 设置
cat > .vscode/settings.json << 'EOF'
{
    "go.toolsManagement.checkForUpdates": "local",
    "go.useLanguageServer": true,
    "go.formatTool": "goimports",
    "go.lintTool": "golangci-lint",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "eslint.autoFixOnSave": true,
    "typescript.preferences.importModuleSpecifier": "relative",
    "files.exclude": {
        "**/node_modules": true,
        "**/vendor": true,
        "**/.git": true,
        "**/dist": true,
        "**/build": true
    },
    "docker.defaultRegistryPath": "godeye"
}
EOF

log_success "VSCode 配置创建完成"

# 设置文件权限
log_info "🔒 设置文件权限..."
chmod 600 .env.dev 2>/dev/null || true
chmod +x scripts/*.sh 2>/dev/null || true

log_success "文件权限设置完成"

echo ""
log_success "✅ GodEye 开发环境初始化完成!"
echo ""
echo -e "${YELLOW}📝 下一步操作:${NC}"
echo "1. 编辑 .env.dev 文件，配置 GitHub Token 等参数"
echo "2. 运行 ./scripts/start-dev.sh 启动开发环境"
echo "3. 使用 VSCode 打开项目: code ."
echo "4. 开始愉快的开发吧! 🎉"
