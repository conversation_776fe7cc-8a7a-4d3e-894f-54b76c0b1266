#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo -e "${BLUE}🛑 停止 GodEye 开发环境${NC}"
echo ""

# 检查 Docker 服务
if ! docker info &> /dev/null; then
    log_warning "Docker 服务未运行"
    exit 0
fi

# 停止所有服务
log_info "🔄 停止所有服务..."
docker-compose -f docker-compose.dev.yml down

# 显示停止的服务
log_info "📋 已停止的服务:"
echo "  - PostgreSQL 开发数据库"
echo "  - Redis 开发缓存"
echo "  - Elasticsearch 开发搜索"
echo "  - RabbitMQ 开发消息队列"
echo "  - 认证服务"
echo "  - 监控服务"
echo "  - 通知服务"
echo "  - 前端开发服务"
echo "  - Nginx 代理"

echo ""
log_success "✅ GodEye 开发环境已停止"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo "  - 数据已保留在 Docker 卷中"
echo "  - 重新启动: ./scripts/start-dev.sh"
echo "  - 完全清理: ./scripts/clean-dev.sh"
