# GodEye 环境配置模板
# 复制此文件为 .env.dev (开发环境) 或 .env (生产环境) 并填写实际值

# ===========================================
# 基础配置
# ===========================================

# 环境类型 (development, staging, production)
NODE_ENV=development
GO_ENV=development

# 应用配置
APP_NAME=GodEye
APP_VERSION=1.0.0
APP_PORT=8080

# 域名配置
DOMAIN=localhost
SSL_ENABLED=false

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL 配置
DB_HOST=postgres
DB_PORT=5432
DB_NAME=godeye
DB_USER=godeye
DB_PASSWORD=your_db_password_here
DB_SSL_MODE=disable

# 数据库连接池配置
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=300

# ===========================================
# 缓存配置
# ===========================================

# Redis 配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Redis 连接池配置
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5

# ===========================================
# 搜索引擎配置
# ===========================================

# Elasticsearch 配置
ES_HOST=elasticsearch
ES_PORT=9200
ES_USERNAME=
ES_PASSWORD=
ES_INDEX_PREFIX=godeye

# ===========================================
# 消息队列配置
# ===========================================

# RabbitMQ 配置
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/

# ===========================================
# 认证配置
# ===========================================

# JWT 配置
JWT_SECRET=your_jwt_secret_key_change_in_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=168h

# 会话配置
SESSION_SECRET=your_session_secret_key_change_in_production
SESSION_MAX_AGE=86400

# ===========================================
# GitHub API 配置
# ===========================================

# GitHub Token (必填)
GITHUB_TOKEN=your_github_token_here

# GitHub API 配置
GITHUB_API_URL=https://api.github.com
GITHUB_RATE_LIMIT=5000
GITHUB_TIMEOUT=30

# ===========================================
# 通知配置
# ===========================================

# SMTP 邮件配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=GodEye <<EMAIL>>
SMTP_TLS=true

# Webhook 配置
WEBHOOK_SECRET=your_webhook_secret
WEBHOOK_TIMEOUT=10

# 钉钉机器人配置
DINGTALK_TOKEN=your_dingtalk_token
DINGTALK_SECRET=your_dingtalk_secret

# ===========================================
# 监控配置
# ===========================================

# Prometheus 配置
PROMETHEUS_PORT=9090
PROMETHEUS_RETENTION=15d

# Grafana 配置
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin123

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout

# ===========================================
# 安全配置
# ===========================================

# CORS 配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# ===========================================
# 前端配置
# ===========================================

# Next.js 配置
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080
NEXT_PUBLIC_APP_NAME=GodEye
NEXT_PUBLIC_APP_VERSION=1.0.0

# ===========================================
# 开发配置
# ===========================================

# 开发工具配置
HOT_RELOAD=true
DEBUG_MODE=true
ENABLE_PROFILER=false

# 测试配置
TEST_DB_NAME=godeye_test
TEST_REDIS_DB=1

# ===========================================
# 生产配置 (仅生产环境)
# ===========================================

# 性能配置
WORKER_PROCESSES=auto
MAX_CONNECTIONS=1024

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# SSL 证书配置 (生产环境)
SSL_CERT_PATH=/etc/ssl/certs/godeye.crt
SSL_KEY_PATH=/etc/ssl/private/godeye.key

# ===========================================
# 扩展配置
# ===========================================

# 第三方集成
SLACK_WEBHOOK_URL=
TEAMS_WEBHOOK_URL=

# 分析配置
ANALYTICS_ENABLED=false
ANALYTICS_TRACKING_ID=

# 功能开关
FEATURE_ADVANCED_SEARCH=true
FEATURE_REAL_TIME_ALERTS=true
FEATURE_BULK_OPERATIONS=true
