#!/bin/bash

echo "🔍 GodEye系统状态检查"
echo "===================="

# 检查Docker
echo "1. Docker状态:"
if command -v docker >/dev/null 2>&1; then
    echo "✅ Docker已安装"
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker运行正常"
    else
        echo "❌ Docker未运行"
        exit 1
    fi
else
    echo "❌ Docker未安装"
    exit 1
fi

# 检查容器
echo ""
echo "2. 容器状态:"
cd godeye 2>/dev/null || { echo "❌ godeye目录不存在"; exit 1; }

if docker-compose -f docker-compose.simple.yml ps | grep -q "Up"; then
    echo "✅ 发现运行中的容器"
    docker-compose -f docker-compose.simple.yml ps
else
    echo "⚠️  没有运行中的容器"
fi

# 检查端口
echo ""
echo "3. 端口检查:"
if command -v lsof >/dev/null 2>&1; then
    for port in 80 3000 8080; do
        if lsof -i :$port >/dev/null 2>&1; then
            echo "⚠️  端口 $port 被占用"
            lsof -i :$port
        else
            echo "✅ 端口 $port 可用"
        fi
    done
else
    echo "⚠️  lsof命令不可用，跳过端口检查"
fi

# 检查网络连接
echo ""
echo "4. 网络连接测试:"
if command -v curl >/dev/null 2>&1; then
    for port in 80 8080 3000; do
        if curl -s --connect-timeout 3 http://localhost:$port/health >/dev/null 2>&1; then
            echo "✅ 端口 $port 响应正常"
        else
            echo "❌ 端口 $port 无响应"
        fi
    done
else
    echo "⚠️  curl命令不可用，跳过网络测试"
fi

# 检查镜像
echo ""
echo "5. Docker镜像:"
if docker images | grep -q godeye; then
    echo "✅ 发现GodEye镜像"
    docker images | grep godeye
else
    echo "⚠️  没有GodEye镜像"
fi

echo ""
echo "检查完成！"
