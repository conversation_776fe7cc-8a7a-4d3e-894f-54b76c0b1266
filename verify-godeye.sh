#!/bin/bash

# GodEye 服务验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 GodEye 服务验证"
echo "=================="

# 检查Docker是否运行
log_info "检查Docker状态..."
if ! docker info > /dev/null 2>&1; then
    log_error "Docker未运行"
    exit 1
fi
log_success "Docker运行正常"

# 检查容器状态
log_info "检查GodEye容器状态..."
echo ""
echo "📋 容器列表:"
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(godeye|NAMES)" || echo "未找到GodEye容器"

echo ""
echo "🔍 GodEye相关容器:"
GODEYE_CONTAINERS=$(docker ps -a --filter "name=godeye" --format "{{.Names}}" | wc -l)
if [ $GODEYE_CONTAINERS -eq 0 ]; then
    log_warning "未找到GodEye容器，需要启动服务"
    echo ""
    echo "🚀 启动命令:"
    echo "cd godeye && docker-compose -f docker-compose.simple.yml up -d"
else
    log_info "找到 $GODEYE_CONTAINERS 个GodEye容器"
fi

# 检查端口占用
log_info "检查端口占用情况..."
echo ""
echo "📡 端口状态:"

check_port() {
    local port=$1
    local service=$2
    if lsof -i :$port > /dev/null 2>&1; then
        echo "   ✅ 端口 $port ($service): 已占用"
        return 0
    else
        echo "   ❌ 端口 $port ($service): 未占用"
        return 1
    fi
}

check_port 80 "GodEye主应用"
check_port 5432 "PostgreSQL"
check_port 6379 "Redis"

# 检查服务健康状态
echo ""
log_info "检查服务健康状态..."

# 检查GodEye应用
echo "🌐 GodEye应用健康检查:"
if curl -s --connect-timeout 5 http://localhost/health > /dev/null 2>&1; then
    log_success "GodEye应用响应正常"
    echo "   响应内容:"
    curl -s http://localhost/health | head -3
else
    log_warning "GodEye应用无响应"
fi

# 检查主页
echo ""
echo "🏠 GodEye主页检查:"
if curl -s --connect-timeout 5 http://localhost > /dev/null 2>&1; then
    log_success "GodEye主页可访问"
else
    log_warning "GodEye主页无法访问"
fi

# 检查数据库连接
echo ""
echo "🗄️ 数据库连接检查:"
if docker exec godeye-postgres pg_isready -U godeye > /dev/null 2>&1; then
    log_success "PostgreSQL连接正常"
else
    log_warning "PostgreSQL连接异常"
fi

# 检查Redis连接
echo ""
echo "💾 Redis连接检查:"
if docker exec godeye-redis redis-cli ping > /dev/null 2>&1; then
    log_success "Redis连接正常"
else
    log_warning "Redis连接异常"
fi

# 显示日志摘要
echo ""
log_info "最近日志摘要..."
echo "📋 GodEye应用日志 (最近10行):"
docker logs godeye-app --tail=10 2>/dev/null || echo "无法获取应用日志"

echo ""
echo "📋 PostgreSQL日志 (最近5行):"
docker logs godeye-postgres --tail=5 2>/dev/null || echo "无法获取数据库日志"

# 生成状态报告
echo ""
echo "=================================="
echo "📊 GodEye 系统状态报告"
echo "=================================="

# 计算服务状态
SERVICES_UP=0
TOTAL_SERVICES=3

if curl -s --connect-timeout 3 http://localhost/health > /dev/null 2>&1; then
    SERVICES_UP=$((SERVICES_UP + 1))
fi

if docker exec godeye-postgres pg_isready -U godeye > /dev/null 2>&1; then
    SERVICES_UP=$((SERVICES_UP + 1))
fi

if docker exec godeye-redis redis-cli ping > /dev/null 2>&1; then
    SERVICES_UP=$((SERVICES_UP + 1))
fi

echo "🎯 服务状态: $SERVICES_UP/$TOTAL_SERVICES 正常"

if [ $SERVICES_UP -eq $TOTAL_SERVICES ]; then
    log_success "🎉 所有服务运行正常！"
    echo ""
    echo "🌐 访问地址: http://localhost"
    echo "👤 默认用户: admin"
    echo "🔑 默认密码: admin123"
    echo ""
    echo "🚀 您可以开始使用GodEye系统了！"
elif [ $SERVICES_UP -gt 0 ]; then
    log_warning "⚠️ 部分服务运行异常，请检查日志"
    echo ""
    echo "🔧 故障排除:"
    echo "   查看完整日志: docker-compose -f docker-compose.simple.yml logs"
    echo "   重启服务: docker-compose -f docker-compose.simple.yml restart"
else
    log_error "❌ 所有服务都未正常运行"
    echo ""
    echo "🚀 启动服务:"
    echo "   cd godeye && docker-compose -f docker-compose.simple.yml up -d"
fi

echo ""
echo "🔧 常用管理命令:"
echo "   启动: docker-compose -f docker-compose.simple.yml up -d"
echo "   停止: docker-compose -f docker-compose.simple.yml down"
echo "   重启: docker-compose -f docker-compose.simple.yml restart"
echo "   日志: docker-compose -f docker-compose.simple.yml logs -f"
echo "   状态: docker-compose -f docker-compose.simple.yml ps"
echo "=================================="
