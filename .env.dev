# GodEye 开发环境配置
NODE_ENV=development
GO_ENV=development

# 数据库配置
DB_HOST=postgres-dev
DB_PORT=5432
DB_NAME=godeye_dev
DB_USER=godeye_dev
DB_PASSWORD=dev123

# Redis 配置
REDIS_HOST=redis-dev
REDIS_PORT=6379

# JWT 配置
JWT_SECRET=dev-jwt-secret-key-change-in-production

# GitHub API 配置 (请填写您的 GitHub Token)
GITHUB_TOKEN=your_github_token_here

# 前端配置
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080

# 开发工具配置
HOT_RELOAD=true
DEBUG_MODE=true
LOG_LEVEL=debug
