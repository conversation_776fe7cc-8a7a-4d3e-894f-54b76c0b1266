#!/bin/bash

echo "🚀 GodEye 简化架构启动脚本"
echo "=========================="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

echo "✅ Docker运行正常"

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose -f docker-compose.simple.yml down --remove-orphans

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker image prune -f

# 构建新镜像
echo "🔨 构建应用镜像..."
docker-compose -f docker-compose.simple.yml build --no-cache

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo "✅ 构建成功"

# 启动服务
echo "🚀 启动服务..."
docker-compose -f docker-compose.simple.yml up -d

if [ $? -ne 0 ]; then
    echo "❌ 启动失败"
    exit 1
fi

echo "✅ 服务启动成功"

# 等待服务就绪
echo "⏳ 等待服务就绪..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.simple.yml ps

# 检查健康状态
echo "🏥 检查健康状态..."
for i in {1..30}; do
    if curl -s http://localhost:80/health > /dev/null; then
        echo "✅ 服务健康检查通过"
        break
    fi
    echo "⏳ 等待服务启动... ($i/30)"
    sleep 2
done

if ! curl -s http://localhost:80/health > /dev/null; then
    echo "❌ 服务健康检查失败"
    echo "📋 查看日志:"
    docker-compose -f docker-compose.simple.yml logs --tail=50
    exit 1
fi

echo ""
echo "🎉 GodEye系统启动成功！"
echo "========================"
echo "🌐 访问地址: http://localhost:80"
echo "👤 默认用户: admin"
echo "🔑 默认密码: admin123"
echo ""
echo "📋 管理命令:"
echo "  查看日志: docker-compose -f docker-compose.simple.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.simple.yml down"
echo "  重启服务: docker-compose -f docker-compose.simple.yml restart"
echo ""
echo "🧪 运行测试: ./comprehensive_test.sh"
