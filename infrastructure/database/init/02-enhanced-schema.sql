-- GodEye 增强数据库架构
-- 基于详细设计文档的完整数据库结构

-- ============================================================================
-- 用户认证域增强 (Auth Domain Enhanced)
-- ============================================================================

-- 增强用户表字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP WITH TIME ZONE;

-- 增强角色表字段
ALTER TABLE roles ADD COLUMN IF NOT EXISTS is_system BOOLEAN DEFAULT false;
ALTER TABLE roles ALTER COLUMN permissions SET NOT NULL;
ALTER TABLE roles ALTER COLUMN permissions SET DEFAULT '{}';

-- 增强用户角色关联表
ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS assigned_by UUID;
ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE user_roles DROP COLUMN IF EXISTS created_at;

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- 监控域增强 (Monitor Domain Enhanced)
-- ============================================================================

-- 增强监控任务表
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS repository_owner VARCHAR(255);
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS repository_name VARCHAR(255);
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS file_extensions TEXT[];
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS exclude_paths TEXT[];
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS scan_depth INTEGER DEFAULT 100;
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT false;
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 5;
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS scan_count INTEGER DEFAULT 0;
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0;
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS last_error TEXT;
ALTER TABLE monitor_tasks ADD COLUMN IF NOT EXISTS config JSONB DEFAULT '{}';

-- 移除外键约束，改为逻辑关联
ALTER TABLE monitor_tasks DROP CONSTRAINT IF EXISTS monitor_tasks_user_id_fkey;

-- 增强扫描结果表
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS scan_session_id UUID NOT NULL DEFAULT uuid_generate_v4();
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS repository_owner VARCHAR(255);
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS repository_name VARCHAR(255);
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS file_name VARCHAR(255);
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS column_number INTEGER;
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS matched_pattern VARCHAR(500);
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS context_before TEXT;
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS context_after TEXT;
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS severity VARCHAR(20) DEFAULT 'info';
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS reviewed_by UUID;
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS review_comment TEXT;
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS commit_hash VARCHAR(40);
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS commit_author VARCHAR(255);
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS commit_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS file_size INTEGER;
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS file_type VARCHAR(50);
ALTER TABLE scan_results ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';
ALTER TABLE scan_results ALTER COLUMN confidence_score SET DEFAULT 0.5;

-- 创建扫描会话表
CREATE TABLE IF NOT EXISTS scan_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES monitor_tasks(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'running',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    files_scanned INTEGER DEFAULT 0,
    results_found INTEGER DEFAULT 0,
    errors_count INTEGER DEFAULT 0,
    scan_config JSONB DEFAULT '{}',
    error_message TEXT,
    statistics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建关键词库表
CREATE TABLE IF NOT EXISTS keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    risk_level VARCHAR(20) DEFAULT 'medium',
    is_regex BOOLEAN DEFAULT false,
    is_case_sensitive BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- 通知域增强 (Notification Domain Enhanced)
-- ============================================================================

-- 增强通知配置表
ALTER TABLE notification_configs ADD COLUMN IF NOT EXISTS name VARCHAR(255) NOT NULL DEFAULT 'Default Config';
ALTER TABLE notification_configs ADD COLUMN IF NOT EXISTS is_default BOOLEAN DEFAULT false;
ALTER TABLE notification_configs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 5;
ALTER TABLE notification_configs ADD COLUMN IF NOT EXISTS rate_limit INTEGER DEFAULT 100;
ALTER TABLE notification_configs ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 3;
ALTER TABLE notification_configs ADD COLUMN IF NOT EXISTS timeout_seconds INTEGER DEFAULT 30;

-- 移除外键约束，改为逻辑关联
ALTER TABLE notification_configs DROP CONSTRAINT IF EXISTS notification_configs_user_id_fkey;

-- 创建通知规则表
CREATE TABLE IF NOT EXISTS notification_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 5,
    cooldown_minutes INTEGER DEFAULT 60,
    max_triggers_per_hour INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 增强通知记录表
ALTER TABLE notification_logs ADD COLUMN IF NOT EXISTS rule_id UUID;
ALTER TABLE notification_logs ADD COLUMN IF NOT EXISTS recipient VARCHAR(255);
ALTER TABLE notification_logs ADD COLUMN IF NOT EXISTS subject VARCHAR(500);
ALTER TABLE notification_logs ADD COLUMN IF NOT EXISTS content TEXT;
ALTER TABLE notification_logs ADD COLUMN IF NOT EXISTS attempts INTEGER DEFAULT 0;
ALTER TABLE notification_logs ADD COLUMN IF NOT EXISTS max_attempts INTEGER DEFAULT 3;
ALTER TABLE notification_logs ADD COLUMN IF NOT EXISTS response_data JSONB;
ALTER TABLE notification_logs RENAME COLUMN message TO error_message;

-- ============================================================================
-- 系统域增强 (System Domain Enhanced)
-- ============================================================================

-- 增强系统配置表
ALTER TABLE system_configs ADD COLUMN IF NOT EXISTS category VARCHAR(100);
ALTER TABLE system_configs ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT false;
ALTER TABLE system_configs ADD COLUMN IF NOT EXISTS is_encrypted BOOLEAN DEFAULT false;

-- 增强操作日志表
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS old_values JSONB;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS new_values JSONB;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS request_id VARCHAR(100);
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS session_id VARCHAR(100);
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS success BOOLEAN DEFAULT true;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS error_message TEXT;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS duration_ms INTEGER;
ALTER TABLE audit_logs RENAME COLUMN details TO old_values;

-- ============================================================================
-- 创建增强索引
-- ============================================================================

-- 用户认证域索引
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_locked_until ON users(locked_until);
CREATE INDEX IF NOT EXISTS idx_roles_is_system ON roles(is_system);
CREATE INDEX IF NOT EXISTS idx_roles_permissions ON roles USING GIN(permissions);
CREATE INDEX IF NOT EXISTS idx_user_roles_assigned_by ON user_roles(assigned_by);
CREATE INDEX IF NOT EXISTS idx_user_roles_expires_at ON user_roles(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(token_hash);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);

-- 监控域索引
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_platform ON monitor_tasks(platform);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_priority ON monitor_tasks(priority);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_repository ON monitor_tasks(repository_owner, repository_name);
CREATE INDEX IF NOT EXISTS idx_monitor_tasks_keywords ON monitor_tasks USING GIN(keywords);
CREATE INDEX IF NOT EXISTS idx_scan_results_session_id ON scan_results(scan_session_id);
CREATE INDEX IF NOT EXISTS idx_scan_results_severity ON scan_results(severity);
CREATE INDEX IF NOT EXISTS idx_scan_results_repository ON scan_results(repository_owner, repository_name);
CREATE INDEX IF NOT EXISTS idx_scan_results_file_path ON scan_results(file_path);
CREATE INDEX IF NOT EXISTS idx_scan_results_confidence_score ON scan_results(confidence_score);
CREATE INDEX IF NOT EXISTS idx_scan_sessions_task_id ON scan_sessions(task_id);
CREATE INDEX IF NOT EXISTS idx_scan_sessions_status ON scan_sessions(status);
CREATE INDEX IF NOT EXISTS idx_scan_sessions_started_at ON scan_sessions(started_at);
CREATE INDEX IF NOT EXISTS idx_keywords_keyword ON keywords(keyword);
CREATE INDEX IF NOT EXISTS idx_keywords_category ON keywords(category);
CREATE INDEX IF NOT EXISTS idx_keywords_risk_level ON keywords(risk_level);
CREATE INDEX IF NOT EXISTS idx_keywords_is_active ON keywords(is_active);

-- 通知域索引
CREATE INDEX IF NOT EXISTS idx_notification_configs_is_default ON notification_configs(is_default);
CREATE INDEX IF NOT EXISTS idx_notification_configs_priority ON notification_configs(priority);
CREATE INDEX IF NOT EXISTS idx_notification_rules_user_id ON notification_rules(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_rules_is_active ON notification_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_notification_rules_conditions ON notification_rules USING GIN(conditions);
CREATE INDEX IF NOT EXISTS idx_notification_logs_rule_id ON notification_logs(rule_id);
CREATE INDEX IF NOT EXISTS idx_notification_logs_attempts ON notification_logs(attempts);

-- 系统域索引
CREATE INDEX IF NOT EXISTS idx_system_configs_category ON system_configs(category);
CREATE INDEX IF NOT EXISTS idx_system_configs_is_public ON system_configs(is_public);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_id ON audit_logs(resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_request_id ON audit_logs(request_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_success ON audit_logs(success);
CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address);

-- ============================================================================
-- 插入增强的默认数据
-- ============================================================================

-- 更新默认角色
UPDATE roles SET permissions = '{"all": true}', is_system = true WHERE name = 'admin';
UPDATE roles SET permissions = '{"users": ["read", "create", "update"], "tasks": ["all"], "results": ["all"], "notifications": ["all"]}', is_system = true WHERE name = 'manager';
UPDATE roles SET permissions = '{"tasks": ["read", "create", "update"], "results": ["read"], "notifications": ["read", "create"]}', is_system = true WHERE name = 'user';

-- 插入默认关键词
INSERT INTO keywords (keyword, category, description, risk_level, is_regex, is_case_sensitive) VALUES 
('password', 'credentials', '密码相关关键词', 'high', false, false),
('secret', 'credentials', '秘密信息关键词', 'high', false, false),
('api_key', 'credentials', 'API密钥关键词', 'high', false, false),
('token', 'credentials', '令牌关键词', 'medium', false, false),
('private_key', 'credentials', '私钥关键词', 'critical', false, false),
('access_token', 'credentials', '访问令牌', 'high', false, false),
('database_url', 'config', '数据库连接字符串', 'medium', false, false),
('smtp_password', 'config', 'SMTP密码', 'medium', false, false),
('aws_secret', 'cloud', 'AWS秘密信息', 'high', false, false),
('github_token', 'cloud', 'GitHub令牌', 'high', false, false)
ON CONFLICT (keyword) DO NOTHING;

-- 插入增强的系统配置
INSERT INTO system_configs (key, value, description, category, is_public) VALUES 
('max_scan_depth', '1000', '最大扫描深度', 'scan', true),
('concurrent_scans', '5', '并发扫描数量', 'scan', true),
('result_retention_days', '365', '扫描结果保留天数', 'storage', true),
('notification_batch_size', '50', '通知批处理大小', 'notification', true),
('api_rate_limit_per_hour', '1000', 'API每小时限制', 'api', true),
('session_timeout_hours', '24', '会话超时时间(小时)', 'auth', true),
('max_login_attempts', '5', '最大登录尝试次数', 'auth', true),
('account_lock_minutes', '30', '账号锁定时间(分钟)', 'auth', true)
ON CONFLICT (key) DO NOTHING;
