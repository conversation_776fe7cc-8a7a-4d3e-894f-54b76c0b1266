// 全局搜索相关类型定义

// 平台类型
export type Platform = 'github' | 'gitlab' | 'gitee';

// 调度类型
export type ScheduleType = 'manual' | 'interval' | 'cron';

// 任务状态
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'paused';

// 搜索结果严重程度
export type Severity = 'low' | 'medium' | 'high' | 'critical';

// 账号状态
export type AccountStatus = 'active' | 'inactive' | 'rate_limited' | 'expired' | 'error';

// 账号类型
export type AccountType = 'personal' | 'organization' | 'app';

// 全局搜索任务
export interface GlobalSearchTask {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  keywords: string[];
  platforms: Platform[];
  schedule_type: ScheduleType;
  schedule_rule?: string;
  status: TaskStatus;
  last_run_at?: string;
  next_run_at?: string;
  created_at: string;
  updated_at: string;
  
  // 统计信息
  total_runs?: number;
  successful_runs?: number;
  failed_runs?: number;
  total_results?: number;
}

// 搜索结果
export interface SearchResult {
  id: string;
  task_id?: string;
  platform: Platform;
  repository_name: string;
  repository_url: string;
  file_path: string;
  file_url: string;
  matched_content: string;
  matched_keywords: string[];
  context_lines: string[];
  severity: Severity;
  risk_score: number;
  commit_hash?: string;
  commit_author?: string;
  commit_date?: string;
  created_at: string;
  
  // 额外信息
  repository_description?: string;
  repository_stars?: number;
  repository_forks?: number;
  file_size?: number;
  line_number?: number;
}

// 平台账号
export interface PlatformAccount {
  id: string;
  user_id: string;
  platform: Platform;
  account_type: AccountType;
  username?: string;
  token: string;
  refresh_token?: string;
  expires_at?: string;
  rate_limit?: number;
  rate_remaining?: number;
  rate_reset_at?: string;
  status: AccountStatus;
  last_used_at?: string;
  last_error?: string;
  created_at: string;
  updated_at: string;
}

// 快速搜索请求
export interface QuickSearchRequest {
  keywords: string[];
  platforms: Platform[];
  max_results?: number;
}

// 快速搜索响应
export interface QuickSearchResponse {
  search_id: string;
  status: 'started' | 'completed' | 'failed';
  results?: SearchResult[];
  total_results?: number;
  platforms_searched?: Platform[];
  search_time?: number;
}

// 搜索统计
export interface SearchStats {
  total_tasks: number;
  active_tasks: number;
  total_results: number;
  results_today: number;
  high_risk_results: number;
  platforms_coverage: Record<Platform, number>;
}

// 搜索趋势数据
export interface SearchTrend {
  date: string;
  total_searches: number;
  total_results: number;
  high_risk_results: number;
  platform_breakdown: Record<Platform, number>;
}

// 平台使用统计
export interface PlatformUsageStats {
  platform: Platform;
  total_accounts: number;
  active_accounts: number;
  total_requests: number;
  requests_today: number;
  rate_limit_hits: number;
  success_rate: number;
  avg_response_time: number;
}

// 账号配额信息
export interface RateLimit {
  limit: number;
  remaining: number;
  reset_at: string;
  used_percentage: number;
}

// 创建全局搜索任务请求
export interface CreateGlobalSearchTaskRequest {
  name: string;
  description?: string;
  keywords: string[];
  platforms: Platform[];
  schedule_type: ScheduleType;
  schedule_rule?: string;
}

// 更新全局搜索任务请求
export interface UpdateGlobalSearchTaskRequest {
  name?: string;
  description?: string;
  keywords?: string[];
  platforms?: Platform[];
  schedule_type?: ScheduleType;
  schedule_rule?: string;
  status?: TaskStatus;
}

// 创建平台账号请求
export interface CreatePlatformAccountRequest {
  platform: Platform;
  account_type: AccountType;
  username?: string;
  token: string;
  refresh_token?: string;
}

// 更新平台账号请求
export interface UpdatePlatformAccountRequest {
  username?: string;
  token?: string;
  refresh_token?: string;
  status?: AccountStatus;
}

// 搜索过滤器
export interface SearchFilter {
  task_id?: string;
  platform?: Platform;
  severity?: Severity;
  keywords?: string[];
  date_from?: string;
  date_to?: string;
  repository?: string;
  min_risk_score?: number;
  max_risk_score?: number;
}

// 分页参数
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// 任务执行历史
export interface TaskExecution {
  id: string;
  task_id: string;
  status: TaskStatus;
  started_at: string;
  completed_at?: string;
  duration?: number;
  results_count: number;
  error_message?: string;
  platforms_searched: Platform[];
  keywords_used: string[];
}

// 关键词建议
export interface KeywordSuggestion {
  keyword: string;
  frequency: number;
  risk_level: Severity;
  related_keywords: string[];
  example_matches: string[];
}

// 所有类型都已通过命名导出提供
