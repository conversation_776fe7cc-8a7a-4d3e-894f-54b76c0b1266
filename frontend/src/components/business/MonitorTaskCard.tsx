import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ge, StatusBadge } from '@/components/ui';
import { cn, formatDate } from '@/lib/utils';

export interface MonitorTask {
  id: string;
  name: string;
  description?: string;
  repository: string;
  branch: string;
  status: 'active' | 'stopped' | 'failed' | 'completed';
  scanType: 'full' | 'incremental' | 'scheduled';
  schedule?: string;
  lastScanAt?: string;
  nextScanAt?: string;
  totalScans: number;
  totalFindings: number;
  criticalFindings: number;
  highFindings: number;
  createdAt: string;
  updatedAt: string;
  owner: string;
  tags?: string[];
}

export interface MonitorTaskCardProps {
  task: MonitorTask;
  onEdit?: (task: MonitorTask) => void;
  onDelete?: (task: MonitorTask) => void;
  onPause?: (task: MonitorTask) => void;
  onResume?: (task: MonitorTask) => void;
  onRunNow?: (task: MonitorTask) => void;
  onViewResults?: (task: MonitorTask) => void;
  className?: string;
}

export const MonitorTaskCard: React.FC<MonitorTaskCardProps> = ({
  task,
  onEdit,
  onDelete,
  onPause,
  onResume,
  onRunNow,
  onViewResults,
  className,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 dark:text-green-400';
      case 'stopped': return 'text-yellow-600 dark:text-yellow-400';
      case 'failed': return 'text-red-600 dark:text-red-400';
      case 'completed': return 'text-blue-600 dark:text-blue-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getScanTypeText = (type: string) => {
    switch (type) {
      case 'full': return '全量扫描';
      case 'incremental': return '增量扫描';
      case 'scheduled': return '定时扫描';
      default: return type;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '运行中';
      case 'stopped': return '已暂停';
      case 'failed': return '错误';
      case 'completed': return '已完成';
      default: return status;
    }
  };

  return (
    <Card className={cn('hover:shadow-md transition-shadow', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-medium truncate">
              {task.name}
            </CardTitle>
            {task.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                {task.description}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2 ml-4">
            <StatusBadge status={task.status} />
            <Badge variant="outline">
              {getScanTypeText(task.scanType)}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 仓库信息 */}
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center space-x-1">
            <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8z" clipRule="evenodd" />
            </svg>
            <span className="font-mono text-gray-700 dark:text-gray-300">
              {task.repository}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
            <span className="text-gray-600 dark:text-gray-400">{task.branch}</span>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {task.totalScans}
            </div>
            <div className="text-xs text-gray-500">总扫描次数</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {task.totalFindings}
            </div>
            <div className="text-xs text-gray-500">总发现数</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-red-600 dark:text-red-400">
              {task.criticalFindings}
            </div>
            <div className="text-xs text-gray-500">严重风险</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-orange-600 dark:text-orange-400">
              {task.highFindings}
            </div>
            <div className="text-xs text-gray-500">高风险</div>
          </div>
        </div>

        {/* 时间信息 */}
        <div className="space-y-2 text-sm">
          {task.lastScanAt && (
            <div className="flex justify-between">
              <span className="text-gray-500">上次扫描:</span>
              <span className="text-gray-700 dark:text-gray-300">
                {formatDate(task.lastScanAt)}
              </span>
            </div>
          )}
          {task.nextScanAt && (
            <div className="flex justify-between">
              <span className="text-gray-500">下次扫描:</span>
              <span className="text-gray-700 dark:text-gray-300">
                {formatDate(task.nextScanAt)}
              </span>
            </div>
          )}
          {task.schedule && (
            <div className="flex justify-between">
              <span className="text-gray-500">扫描计划:</span>
              <span className="text-gray-700 dark:text-gray-300 font-mono text-xs">
                {task.schedule}
              </span>
            </div>
          )}
        </div>

        {/* 标签 */}
        {task.tags && task.tags.length > 0 && (
          <div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              标签
            </div>
            <div className="flex flex-wrap gap-1">
              {task.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* 元信息 */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4">
            <span>创建者: {task.owner}</span>
            <span>创建时间: {formatDate(task.createdAt)}</span>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-2 pt-2">
          {onViewResults && (
            <button
              onClick={() => onViewResults(task)}
              className="btn-secondary text-xs px-3 py-1"
            >
              查看结果
            </button>
          )}
          {onRunNow && task.status !== 'failed' && (
            <button
              onClick={() => onRunNow(task)}
              className="btn-primary text-xs px-3 py-1"
            >
              立即扫描
            </button>
          )}
          {task.status === 'active' && onPause && (
            <button
              onClick={() => onPause(task)}
              className="btn-outline text-xs px-3 py-1"
            >
              暂停
            </button>
          )}
          {task.status === 'stopped' && onResume && (
            <button
              onClick={() => onResume(task)}
              className="btn-outline text-xs px-3 py-1"
            >
              恢复
            </button>
          )}
          {onEdit && (
            <button
              onClick={() => onEdit(task)}
              className="btn-outline text-xs px-3 py-1"
            >
              编辑
            </button>
          )}
          {onDelete && (
            <button
              onClick={() => onDelete(task)}
              className="btn-danger text-xs px-3 py-1"
            >
              删除
            </button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// 监控任务列表组件
export interface MonitorTaskListProps {
  tasks: MonitorTask[];
  loading?: boolean;
  onEdit?: (task: MonitorTask) => void;
  onDelete?: (task: MonitorTask) => void;
  onPause?: (task: MonitorTask) => void;
  onResume?: (task: MonitorTask) => void;
  onRunNow?: (task: MonitorTask) => void;
  onViewResults?: (task: MonitorTask) => void;
  onBatchAction?: (action: string, tasks: MonitorTask[]) => void;
  className?: string;
}

export const MonitorTaskList: React.FC<MonitorTaskListProps> = ({
  tasks,
  loading = false,
  onEdit,
  onDelete,
  onPause,
  onResume,
  onRunNow,
  onViewResults,
  onBatchAction,
  className,
}) => {
  const [selectedTasks, setSelectedTasks] = React.useState<string[]>([]);

  const handleSelectAll = (checked: boolean) => {
    setSelectedTasks(checked ? tasks.map(t => t.id) : []);
  };

  const handleSelectTask = (taskId: string, checked: boolean) => {
    setSelectedTasks(prev => 
      checked 
        ? [...prev, taskId]
        : prev.filter(id => id !== taskId)
    );
  };

  const handleBatchAction = (action: string) => {
    const selectedTaskObjects = tasks.filter(t => selectedTasks.includes(t.id));
    onBatchAction?.(action, selectedTaskObjects);
    setSelectedTasks([]);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-5 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mt-2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="grid grid-cols-4 gap-2">
                  {[...Array(4)].map((_, j) => (
                    <div key={j} className="h-8 bg-gray-200 rounded"></div>
                  ))}
                </div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <Card className="text-center py-12">
        <CardContent>
          <div className="text-gray-500 dark:text-gray-400">
            <svg className="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <h3 className="text-lg font-medium mb-2">暂无监控任务</h3>
            <p className="text-sm">创建您的第一个监控任务来开始代码安全扫描。</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* 批量操作栏 */}
      {onBatchAction && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedTasks.length === tasks.length && tasks.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm">
                  全选 ({selectedTasks.length}/{tasks.length})
                </span>
              </label>
            </div>
            {selectedTasks.length > 0 && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleBatchAction('pause')}
                  className="btn-outline text-sm px-3 py-1"
                >
                  批量暂停
                </button>
                <button
                  onClick={() => handleBatchAction('resume')}
                  className="btn-outline text-sm px-3 py-1"
                >
                  批量恢复
                </button>
                <button
                  onClick={() => handleBatchAction('delete')}
                  className="btn-danger text-sm px-3 py-1"
                >
                  批量删除
                </button>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* 任务网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tasks.map((task) => (
          <div key={task.id} className="relative">
            {onBatchAction && (
              <div className="absolute top-4 left-4 z-10">
                <input
                  type="checkbox"
                  checked={selectedTasks.includes(task.id)}
                  onChange={(e) => handleSelectTask(task.id, e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
            )}
            <MonitorTaskCard
              task={task}
              onEdit={onEdit}
              onDelete={onDelete}
              onPause={onPause}
              onResume={onResume}
              onRunNow={onRunNow}
              onViewResults={onViewResults}
              className={onBatchAction ? 'pt-8' : ''}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
