import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, <PERSON>ge, RiskBadge } from '@/components/ui';
import { cn, formatDate, formatFileSize } from '@/lib/utils';

export interface ScanResult {
  id: string;
  repository: string;
  branch: string;
  filePath: string;
  fileName: string;
  riskLevel: 'critical' | 'high' | 'medium' | 'low' | 'info';
  confidence: number;
  matchedRules: string[];
  content: string;
  lineNumber: number;
  createdAt: string;
  fileSize: number;
  commitHash?: string;
  author?: string;
}

export interface ScanResultCardProps {
  result: ScanResult;
  onView?: (result: ScanResult) => void;
  onIgnore?: (result: ScanResult) => void;
  onMarkFalsePositive?: (result: ScanResult) => void;
  className?: string;
}

export const ScanResultCard: React.FC<ScanResultCardProps> = ({
  result,
  onView,
  onIgnore,
  onMarkFalsePositive,
  className,
}) => {
  const getRiskColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 dark:text-red-400';
      case 'high': return 'text-orange-600 dark:text-orange-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-green-600 dark:text-green-400';
      case 'info': return 'text-blue-600 dark:text-blue-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-red-600 dark:text-red-400';
    if (confidence >= 70) return 'text-orange-600 dark:text-orange-400';
    if (confidence >= 50) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  return (
    <Card className={cn('hover:shadow-md transition-shadow', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base font-medium truncate">
              {result.repository}/{result.fileName}
            </CardTitle>
            <div className="flex items-center space-x-2 mt-1 text-sm text-gray-500">
              <span>{result.branch}</span>
              <span>•</span>
              <span>第 {result.lineNumber} 行</span>
              <span>•</span>
              <span>{formatFileSize(result.fileSize)}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2 ml-4">
            <RiskBadge level={result.riskLevel} />
            <Badge variant="outline" className={getConfidenceColor(result.confidence)}>
              {result.confidence}%
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 匹配规则 */}
        <div>
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            匹配规则
          </div>
          <div className="flex flex-wrap gap-1">
            {result.matchedRules.map((rule, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {rule}
              </Badge>
            ))}
          </div>
        </div>

        {/* 代码片段 */}
        <div>
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            代码片段
          </div>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3 overflow-x-auto">
            <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
              <code>{result.content}</code>
            </pre>
          </div>
        </div>

        {/* 文件路径 */}
        <div>
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            文件路径
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 font-mono bg-gray-50 dark:bg-gray-800 px-2 py-1 rounded">
            {result.filePath}
          </div>
        </div>

        {/* 元信息 */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4">
            <span>发现时间: {formatDate(result.createdAt)}</span>
            {result.author && <span>作者: {result.author}</span>}
            {result.commitHash && (
              <span className="font-mono">
                提交: {result.commitHash.substring(0, 8)}
              </span>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-2 pt-2">
          {onView && (
            <button
              onClick={() => onView(result)}
              className="btn-secondary text-xs px-3 py-1"
            >
              查看详情
            </button>
          )}
          {onMarkFalsePositive && (
            <button
              onClick={() => onMarkFalsePositive(result)}
              className="btn-outline text-xs px-3 py-1"
            >
              误报
            </button>
          )}
          {onIgnore && (
            <button
              onClick={() => onIgnore(result)}
              className="btn-outline text-xs px-3 py-1"
            >
              忽略
            </button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// 扫描结果列表组件
export interface ScanResultListProps {
  results: ScanResult[];
  loading?: boolean;
  onView?: (result: ScanResult) => void;
  onIgnore?: (result: ScanResult) => void;
  onMarkFalsePositive?: (result: ScanResult) => void;
  onBatchAction?: (action: string, results: ScanResult[]) => void;
  className?: string;
}

export const ScanResultList: React.FC<ScanResultListProps> = ({
  results,
  loading = false,
  onView,
  onIgnore,
  onMarkFalsePositive,
  onBatchAction,
  className,
}) => {
  const [selectedResults, setSelectedResults] = React.useState<string[]>([]);

  const handleSelectAll = (checked: boolean) => {
    setSelectedResults(checked ? results.map(r => r.id) : []);
  };

  const handleSelectResult = (resultId: string, checked: boolean) => {
    setSelectedResults(prev => 
      checked 
        ? [...prev, resultId]
        : prev.filter(id => id !== resultId)
    );
  };

  const handleBatchAction = (action: string) => {
    const selectedResultObjects = results.filter(r => selectedResults.includes(r.id));
    onBatchAction?.(action, selectedResultObjects);
    setSelectedResults([]);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mt-2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <Card className="text-center py-12">
        <CardContent>
          <div className="text-gray-500 dark:text-gray-400">
            <svg className="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-lg font-medium mb-2">暂无扫描结果</h3>
            <p className="text-sm">没有发现任何安全风险，系统运行良好。</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* 批量操作栏 */}
      {onBatchAction && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedResults.length === results.length && results.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm">
                  全选 ({selectedResults.length}/{results.length})
                </span>
              </label>
            </div>
            {selectedResults.length > 0 && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleBatchAction('ignore')}
                  className="btn-outline text-sm px-3 py-1"
                >
                  批量忽略
                </button>
                <button
                  onClick={() => handleBatchAction('falsePositive')}
                  className="btn-outline text-sm px-3 py-1"
                >
                  批量标记误报
                </button>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* 结果列表 */}
      <div className="space-y-4">
        {results.map((result) => (
          <div key={result.id} className="flex items-start space-x-3">
            {onBatchAction && (
              <div className="pt-6">
                <input
                  type="checkbox"
                  checked={selectedResults.includes(result.id)}
                  onChange={(e) => handleSelectResult(result.id, e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
            )}
            <div className="flex-1">
              <ScanResultCard
                result={result}
                onView={onView}
                onIgnore={onIgnore}
                onMarkFalsePositive={onMarkFalsePositive}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
