import React, { forwardRef, createContext, useContext } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Tabs Context
interface TabsContextValue {
  value: string;
  onValueChange: (value: string) => void;
  orientation?: 'horizontal' | 'vertical' | null;
}

const TabsContext = createContext<TabsContextValue | undefined>(undefined);

const useTabsContext = () => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tabs components must be used within a Tabs component');
  }
  return context;
};

// Tabs Root
const tabsVariants = cva('', {
  variants: {
    orientation: {
      horizontal: 'space-y-4',
      vertical: 'flex space-x-4',
    },
  },
  defaultVariants: {
    orientation: 'horizontal',
  },
});

export interface TabsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tabsVariants> {
  value: string;
  onValueChange: (value: string) => void;
  defaultValue?: string;
}

const Tabs = forwardRef<HTMLDivElement, TabsProps>(
  ({ className, orientation = 'horizontal', value, onValueChange, children, ...props }, ref) => {
    return (
      <TabsContext.Provider value={{ value, onValueChange, orientation }}>
        <div
          ref={ref}
          className={cn(tabsVariants({ orientation }), className)}
          {...props}
        >
          {children}
        </div>
      </TabsContext.Provider>
    );
  }
);

Tabs.displayName = 'Tabs';

// Tabs List
const tabsListVariants = cva(
  'inline-flex items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500 dark:bg-gray-800 dark:text-gray-400',
  {
    variants: {
      orientation: {
        horizontal: 'h-10 w-full',
        vertical: 'h-auto w-48 flex-col space-y-1 p-2',
      },
      variant: {
        default: '',
        underline: 'bg-transparent p-0 border-b border-gray-200 dark:border-gray-700',
        pills: 'bg-gray-100 dark:bg-gray-800',
      },
    },
    defaultVariants: {
      orientation: 'horizontal',
      variant: 'default',
    },
  }
);

export interface TabsListProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tabsListVariants> {}

const TabsList = forwardRef<HTMLDivElement, TabsListProps>(
  ({ className, variant, ...props }, ref) => {
    const { orientation } = useTabsContext();
    
    return (
      <div
        ref={ref}
        className={cn(tabsListVariants({ orientation, variant }), className)}
        role="tablist"
        {...props}
      />
    );
  }
);

TabsList.displayName = 'TabsList';

// Tabs Trigger
const tabsTriggerVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-gray-950 dark:focus-visible:ring-gray-300',
  {
    variants: {
      variant: {
        default: 'data-[state=active]:bg-white data-[state=active]:text-gray-950 data-[state=active]:shadow-sm dark:data-[state=active]:bg-gray-950 dark:data-[state=active]:text-gray-50',
        underline: 'border-b-2 border-transparent rounded-none data-[state=active]:border-primary-500 data-[state=active]:text-primary-600 dark:data-[state=active]:text-primary-400',
        pills: 'data-[state=active]:bg-primary-500 data-[state=active]:text-white',
      },
      orientation: {
        horizontal: 'w-full',
        vertical: 'w-full justify-start',
      },
    },
    defaultVariants: {
      variant: 'default',
      orientation: 'horizontal',
    },
  }
);

export interface TabsTriggerProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof tabsTriggerVariants> {
  value: string;
  icon?: React.ReactNode;
  badge?: string | number;
}

const TabsTrigger = forwardRef<HTMLButtonElement, TabsTriggerProps>(
  ({ className, variant, value, icon, badge, children, ...props }, ref) => {
    const { value: selectedValue, onValueChange, orientation } = useTabsContext();
    const isActive = selectedValue === value;

    return (
      <button
        ref={ref}
        className={cn(tabsTriggerVariants({ variant, orientation }), className)}
        role="tab"
        aria-selected={isActive}
        data-state={isActive ? 'active' : 'inactive'}
        onClick={() => onValueChange(value)}
        {...props}
      >
        <div className="flex items-center space-x-2">
          {icon && <span className="text-base">{icon}</span>}
          <span>{children}</span>
          {badge && (
            <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
              {badge}
            </span>
          )}
        </div>
      </button>
    );
  }
);

TabsTrigger.displayName = 'TabsTrigger';

// Tabs Content
const tabsContentVariants = cva(
  'mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-950 focus-visible:ring-offset-2 dark:ring-offset-gray-950 dark:focus-visible:ring-gray-300',
  {
    variants: {
      orientation: {
        horizontal: '',
        vertical: 'flex-1',
      },
    },
    defaultVariants: {
      orientation: 'horizontal',
    },
  }
);

export interface TabsContentProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tabsContentVariants> {
  value: string;
  forceMount?: boolean;
}

const TabsContent = forwardRef<HTMLDivElement, TabsContentProps>(
  ({ className, value, forceMount = false, children, ...props }, ref) => {
    const { value: selectedValue, orientation } = useTabsContext();
    const isActive = selectedValue === value;

    if (!isActive && !forceMount) {
      return null;
    }

    return (
      <div
        ref={ref}
        className={cn(tabsContentVariants({ orientation }), className)}
        role="tabpanel"
        data-state={isActive ? 'active' : 'inactive'}
        style={{ display: isActive || forceMount ? 'block' : 'none' }}
        {...props}
      >
        {children}
      </div>
    );
  }
);

TabsContent.displayName = 'TabsContent';

// Card Tabs (特殊样式的标签页)
export interface CardTabsProps {
  items: Array<{
    key: string;
    label: string;
    icon?: React.ReactNode;
    badge?: string | number;
    disabled?: boolean;
    children: React.ReactNode;
  }>;
  activeKey: string;
  onChange: (key: string) => void;
  className?: string;
  tabBarExtraContent?: React.ReactNode;
}

export const CardTabs: React.FC<CardTabsProps> = ({
  items,
  activeKey,
  onChange,
  className,
  tabBarExtraContent,
}) => {
  return (
    <div className={cn('bg-white rounded-lg shadow dark:bg-gray-900', className)}>
      <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700">
        <div className="flex">
          {items.map((item) => (
            <button
              key={item.key}
              onClick={() => !item.disabled && onChange(item.key)}
              disabled={item.disabled}
              className={cn(
                'flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors',
                activeKey === item.key
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300',
                item.disabled && 'opacity-50 cursor-not-allowed'
              )}
            >
              {item.icon && <span>{item.icon}</span>}
              <span>{item.label}</span>
              {item.badge && (
                <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                  {item.badge}
                </span>
              )}
            </button>
          ))}
        </div>
        {tabBarExtraContent && (
          <div className="px-4 py-3">{tabBarExtraContent}</div>
        )}
      </div>
      <div className="p-6">
        {items.find(item => item.key === activeKey)?.children}
      </div>
    </div>
  );
};

// Step Tabs (步骤式标签页)
export interface StepTabsProps {
  current: number;
  items: Array<{
    title: string;
    description?: string;
    icon?: React.ReactNode;
    status?: 'wait' | 'process' | 'finish' | 'error';
    children: React.ReactNode;
  }>;
  onChange?: (current: number) => void;
  className?: string;
}

export const StepTabs: React.FC<StepTabsProps> = ({
  current,
  items,
  onChange,
  className,
}) => {
  return (
    <div className={cn('space-y-6', className)}>
      {/* Steps */}
      <div className="flex items-center">
        {items.map((item, index) => {
          const isActive = index === current;
          const isCompleted = index < current;
          const status = item.status || (isCompleted ? 'finish' : isActive ? 'process' : 'wait');
          
          return (
            <React.Fragment key={index}>
              <div
                className={cn(
                  'flex items-center cursor-pointer',
                  onChange && 'hover:opacity-80'
                )}
                onClick={() => onChange?.(index)}
              >
                <div
                  className={cn(
                    'flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium',
                    {
                      'border-primary-500 bg-primary-500 text-white': status === 'process',
                      'border-green-500 bg-green-500 text-white': status === 'finish',
                      'border-red-500 bg-red-500 text-white': status === 'error',
                      'border-gray-300 bg-white text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400': status === 'wait',
                    }
                  )}
                >
                  {item.icon || (index + 1)}
                </div>
                <div className="ml-3">
                  <div
                    className={cn(
                      'text-sm font-medium',
                      {
                        'text-primary-600 dark:text-primary-400': status === 'process',
                        'text-green-600 dark:text-green-400': status === 'finish',
                        'text-red-600 dark:text-red-400': status === 'error',
                        'text-gray-500 dark:text-gray-400': status === 'wait',
                      }
                    )}
                  >
                    {item.title}
                  </div>
                  {item.description && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {item.description}
                    </div>
                  )}
                </div>
              </div>
              {index < items.length - 1 && (
                <div
                  className={cn(
                    'flex-1 h-px mx-4',
                    isCompleted
                      ? 'bg-green-500'
                      : 'bg-gray-300 dark:bg-gray-600'
                  )}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* Content */}
      <div className="mt-6">
        {items[current]?.children}
      </div>
    </div>
  );
};

export {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
  tabsVariants,
  tabsListVariants,
  tabsTriggerVariants,
  tabsContentVariants,
};
