import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary-600 text-white hover:bg-primary-700',
        secondary:
          'border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700',
        destructive:
          'border-transparent bg-red-600 text-white hover:bg-red-700',
        success:
          'border-transparent bg-green-600 text-white hover:bg-green-700',
        warning:
          'border-transparent bg-yellow-600 text-white hover:bg-yellow-700',
        info:
          'border-transparent bg-blue-600 text-white hover:bg-blue-700',
        outline:
          'border-gray-300 text-gray-900 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-100 dark:hover:bg-gray-800',
        ghost:
          'border-transparent hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100',
      },
      size: {
        default: 'px-2.5 py-0.5 text-xs',
        sm: 'px-2 py-0.5 text-2xs',
        lg: 'px-3 py-1 text-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  icon?: React.ReactNode;
  removable?: boolean;
  onRemove?: () => void;
}

const Badge = forwardRef<HTMLDivElement, BadgeProps>(
  (
    {
      className,
      variant,
      size,
      icon,
      removable,
      onRemove,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn(badgeVariants({ variant, size }), className)}
        {...props}
      >
        {icon && <span className="mr-1">{icon}</span>}
        {children}
        {removable && onRemove && (
          <button
            type="button"
            onClick={onRemove}
            className="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10 focus:outline-none focus:ring-1 focus:ring-white"
          >
            <svg
              className="h-3 w-3"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </div>
    );
  }
);

Badge.displayName = 'Badge';

// 风险等级徽章
export const RiskBadge = forwardRef<
  HTMLDivElement,
  Omit<BadgeProps, 'variant'> & {
    level: 'critical' | 'high' | 'medium' | 'low' | 'info';
  }
>(({ level, children, ...props }, ref) => {
  const riskVariants = {
    critical: 'destructive' as const,
    high: 'warning' as const,
    medium: 'warning' as const,
    low: 'success' as const,
    info: 'info' as const,
  };

  const riskLabels = {
    critical: '严重',
    high: '高危',
    medium: '中危',
    low: '低危',
    info: '信息',
  };

  return (
    <Badge
      ref={ref}
      variant={riskVariants[level]}
      {...props}
    >
      {children || riskLabels[level]}
    </Badge>
  );
});

// 状态徽章
export const StatusBadge = forwardRef<
  HTMLDivElement,
  Omit<BadgeProps, 'variant'> & {
    status: 'active' | 'inactive' | 'pending' | 'completed' | 'failed' | 'running' | 'stopped';
  }
>(({ status, children, ...props }, ref) => {
  const statusVariants = {
    active: 'success' as const,
    inactive: 'secondary' as const,
    pending: 'warning' as const,
    completed: 'success' as const,
    failed: 'destructive' as const,
    running: 'info' as const,
    stopped: 'secondary' as const,
  };

  const statusLabels = {
    active: '活跃',
    inactive: '非活跃',
    pending: '等待中',
    completed: '已完成',
    failed: '失败',
    running: '运行中',
    stopped: '已停止',
  };

  const statusIcons = {
    active: (
      <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 8 8">
        <circle cx={4} cy={4} r={3} />
      </svg>
    ),
    inactive: (
      <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 8 8">
        <circle cx={4} cy={4} r={3} />
      </svg>
    ),
    pending: (
      <svg className="h-3 w-3 animate-spin" fill="none" viewBox="0 0 24 24">
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    ),
    completed: (
      <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M5 13l4 4L19 7"
        />
      </svg>
    ),
    failed: (
      <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    ),
    running: (
      <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M15 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    ),
    stopped: (
      <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 24 24">
        <rect x="6" y="6" width="12" height="12" rx="2" />
      </svg>
    ),
  };

  return (
    <Badge
      ref={ref}
      variant={statusVariants[status]}
      icon={statusIcons[status]}
      {...props}
    >
      {children || statusLabels[status]}
    </Badge>
  );
});

// 数字徽章
export const CountBadge = forwardRef<
  HTMLDivElement,
  Omit<BadgeProps, 'children'> & {
    count: number;
    max?: number;
  }
>(({ count, max = 99, ...props }, ref) => {
  const displayCount = count > max ? `${max}+` : count.toString();
  
  return (
    <Badge
      ref={ref}
      variant="destructive"
      size="sm"
      className="min-w-[1.25rem] h-5 px-1 text-xs"
      {...props}
    >
      {displayCount}
    </Badge>
  );
});

// 在线状态徽章
export const OnlineBadge = forwardRef<
  HTMLDivElement,
  Omit<BadgeProps, 'variant' | 'children'> & {
    online: boolean;
  }
>(({ online, ...props }, ref) => (
  <Badge
    ref={ref}
    variant={online ? 'success' : 'secondary'}
    size="sm"
    icon={
      <svg className="h-2 w-2" fill="currentColor" viewBox="0 0 8 8">
        <circle cx={4} cy={4} r={3} />
      </svg>
    }
    {...props}
  >
    {online ? '在线' : '离线'}
  </Badge>
));

// 新消息徽章
export const NewBadge = forwardRef<HTMLDivElement, BadgeProps>(
  (props, ref) => (
    <Badge
      ref={ref}
      variant="destructive"
      size="sm"
      {...props}
    >
      新
    </Badge>
  )
);

// 热门徽章
export const HotBadge = forwardRef<HTMLDivElement, BadgeProps>(
  (props, ref) => (
    <Badge
      ref={ref}
      variant="warning"
      size="sm"
      icon={
        <svg className="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
            clipRule="evenodd"
          />
        </svg>
      }
      {...props}
    >
      热门
    </Badge>
  )
);

RiskBadge.displayName = 'RiskBadge';
StatusBadge.displayName = 'StatusBadge';
CountBadge.displayName = 'CountBadge';
OnlineBadge.displayName = 'OnlineBadge';
NewBadge.displayName = 'NewBadge';
HotBadge.displayName = 'HotBadge';

export { Badge, badgeVariants };
