import React, { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const loadingVariants = cva('', {
  variants: {
    variant: {
      spinner: 'animate-spin rounded-full border-2 border-gray-300',
      dots: 'flex space-x-1',
      pulse: 'animate-pulse',
      bars: 'flex space-x-1',
      ring: 'animate-spin rounded-full border-2 border-transparent',
    },
    size: {
      sm: '',
      default: '',
      lg: '',
      xl: '',
    },
    color: {
      primary: '',
      secondary: '',
      white: '',
      gray: '',
    },
  },
  defaultVariants: {
    variant: 'spinner',
    size: 'default',
    color: 'primary',
  },
});

export interface LoadingProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'color'>,
    VariantProps<typeof loadingVariants> {
  text?: string;
  overlay?: boolean;
}

const Loading = forwardRef<HTMLDivElement, LoadingProps>(
  (
    {
      className,
      variant,
      size,
      color,
      text,
      overlay = false,
      children,
      ...props
    },
    ref
  ) => {
    const renderSpinner = () => {
      const sizeClasses = {
        sm: 'h-4 w-4',
        default: 'h-6 w-6',
        lg: 'h-8 w-8',
        xl: 'h-12 w-12',
      };

      const colorClasses = {
        primary: 'border-t-primary-600',
        secondary: 'border-t-gray-600',
        white: 'border-t-white',
        gray: 'border-t-gray-400',
      };

      switch (variant) {
        case 'spinner':
          return (
            <div
              className={cn(
                'animate-spin rounded-full border-2 border-gray-300',
                sizeClasses[size || 'default'],
                colorClasses[color || 'primary']
              )}
            />
          );

        case 'dots':
          return (
            <div className="flex space-x-1">
              {[0, 1, 2].map(i => (
                <div
                  key={i}
                  className={cn(
                    'rounded-full animate-pulse',
                    size === 'sm' && 'h-2 w-2',
                    size === 'default' && 'h-3 w-3',
                    size === 'lg' && 'h-4 w-4',
                    size === 'xl' && 'h-6 w-6',
                    color === 'primary' && 'bg-primary-600',
                    color === 'secondary' && 'bg-gray-600',
                    color === 'white' && 'bg-white',
                    color === 'gray' && 'bg-gray-400'
                  )}
                  style={{
                    animationDelay: `${i * 0.2}s`,
                    animationDuration: '1.4s',
                  }}
                />
              ))}
            </div>
          );

        case 'pulse':
          return (
            <div
              className={cn(
                'animate-pulse rounded',
                sizeClasses[size || 'default'],
                color === 'primary' && 'bg-primary-200',
                color === 'secondary' && 'bg-gray-200',
                color === 'white' && 'bg-white/20',
                color === 'gray' && 'bg-gray-300'
              )}
            />
          );

        case 'bars':
          return (
            <div className="flex space-x-1 items-end">
              {[0, 1, 2, 3].map(i => (
                <div
                  key={i}
                  className={cn(
                    'animate-pulse',
                    size === 'sm' && 'w-1 h-4',
                    size === 'default' && 'w-1 h-6',
                    size === 'lg' && 'w-2 h-8',
                    size === 'xl' && 'w-2 h-12',
                    color === 'primary' && 'bg-primary-600',
                    color === 'secondary' && 'bg-gray-600',
                    color === 'white' && 'bg-white',
                    color === 'gray' && 'bg-gray-400'
                  )}
                  style={{
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: '1s',
                    animationDirection: 'alternate',
                    animationIterationCount: 'infinite',
                  }}
                />
              ))}
            </div>
          );

        case 'ring':
          return (
            <div
              className={cn(
                'animate-spin rounded-full border-2 border-transparent',
                sizeClasses[size || 'default'],
                color === 'primary' && 'border-t-primary-600 border-r-primary-600',
                color === 'secondary' && 'border-t-gray-600 border-r-gray-600',
                color === 'white' && 'border-t-white border-r-white',
                color === 'gray' && 'border-t-gray-400 border-r-gray-400'
              )}
            />
          );

        default:
          return null;
      }
    };

    const content = (
      <div
        ref={ref}
        className={cn(
          'flex items-center justify-center',
          text && 'flex-col space-y-2',
          className
        )}
        {...props}
      >
        {renderSpinner()}
        {text && (
          <p
            className={cn(
              'text-sm',
              color === 'white' ? 'text-white' : 'text-gray-600 dark:text-gray-400'
            )}
          >
            {text}
          </p>
        )}
        {children}
      </div>
    );

    if (overlay) {
      return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="rounded-lg bg-white dark:bg-gray-800 p-6 shadow-lg">
            {content}
          </div>
        </div>
      );
    }

    return content;
  }
);

Loading.displayName = 'Loading';

// 骨架屏组件
export const Skeleton = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    lines?: number;
    avatar?: boolean;
    button?: boolean;
  }
>(({ className, lines = 3, avatar = false, button = false, ...props }, ref) => (
  <div ref={ref} className={cn('animate-pulse', className)} {...props}>
    <div className="space-y-3">
      {avatar && (
        <div className="flex items-center space-x-4">
          <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700" />
          <div className="space-y-2">
            <div className="h-4 w-32 rounded bg-gray-200 dark:bg-gray-700" />
            <div className="h-3 w-24 rounded bg-gray-200 dark:bg-gray-700" />
          </div>
        </div>
      )}
      
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, i) => (
          <div
            key={i}
            className={cn(
              'h-4 rounded bg-gray-200 dark:bg-gray-700',
              i === lines - 1 ? 'w-3/4' : 'w-full'
            )}
          />
        ))}
      </div>
      
      {button && (
        <div className="h-10 w-24 rounded bg-gray-200 dark:bg-gray-700" />
      )}
    </div>
  </div>
));

// 表格骨架屏
export const TableSkeleton = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    rows?: number;
    columns?: number;
  }
>(({ className, rows = 5, columns = 4, ...props }, ref) => (
  <div ref={ref} className={cn('animate-pulse', className)} {...props}>
    <div className="space-y-3">
      {/* 表头 */}
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, i) => (
          <div
            key={i}
            className="h-4 flex-1 rounded bg-gray-300 dark:bg-gray-600"
          />
        ))}
      </div>
      
      {/* 表格行 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={colIndex}
              className="h-4 flex-1 rounded bg-gray-200 dark:bg-gray-700"
            />
          ))}
        </div>
      ))}
    </div>
  </div>
));

// 卡片骨架屏
export const CardSkeleton = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'animate-pulse rounded-lg border border-gray-200 dark:border-gray-700 p-6',
      className
    )}
    {...props}
  >
    <div className="space-y-4">
      <div className="h-6 w-3/4 rounded bg-gray-200 dark:bg-gray-700" />
      <div className="space-y-2">
        <div className="h-4 w-full rounded bg-gray-200 dark:bg-gray-700" />
        <div className="h-4 w-5/6 rounded bg-gray-200 dark:bg-gray-700" />
      </div>
      <div className="flex space-x-2">
        <div className="h-8 w-16 rounded bg-gray-200 dark:bg-gray-700" />
        <div className="h-8 w-16 rounded bg-gray-200 dark:bg-gray-700" />
      </div>
    </div>
  </div>
));

// 页面加载器
export const PageLoader = forwardRef<HTMLDivElement, LoadingProps>(
  ({ text = '加载中...', ...props }, ref) => (
    <Loading
      ref={ref}
      variant="spinner"
      size="lg"
      color="primary"
      text={text}
      className="min-h-[200px]"
      {...props}
    />
  )
);

// 按钮加载器
export const ButtonLoader = forwardRef<HTMLDivElement, LoadingProps>(
  (props, ref) => (
    <Loading
      ref={ref}
      variant="spinner"
      size="sm"
      color="white"
      className="mr-2"
      {...props}
    />
  )
);

// 全屏加载器
export const FullScreenLoader = forwardRef<HTMLDivElement, LoadingProps>(
  ({ text = '正在加载...', ...props }, ref) => (
    <Loading
      ref={ref}
      variant="spinner"
      size="xl"
      color="primary"
      text={text}
      overlay
      {...props}
    />
  )
);

Skeleton.displayName = 'Skeleton';
TableSkeleton.displayName = 'TableSkeleton';
CardSkeleton.displayName = 'CardSkeleton';
PageLoader.displayName = 'PageLoader';
ButtonLoader.displayName = 'ButtonLoader';
FullScreenLoader.displayName = 'FullScreenLoader';

export { Loading, loadingVariants };
