import React, { forwardRef, createContext, useContext } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Form Context
interface FormContextValue {
  errors?: Record<string, string>;
  touched?: Record<string, boolean>;
  isSubmitting?: boolean;
}

const FormContext = createContext<FormContextValue>({});

export const useFormContext = () => useContext(FormContext);

// Form Provider
export interface FormProviderProps extends FormContextValue {
  children: React.ReactNode;
}

export const FormProvider: React.FC<FormProviderProps> = ({
  children,
  errors = {},
  touched = {},
  isSubmitting = false,
}) => {
  return (
    <FormContext.Provider value={{ errors, touched, isSubmitting }}>
      {children}
    </FormContext.Provider>
  );
};

// Form Root
const formVariants = cva('space-y-6', {
  variants: {
    layout: {
      vertical: 'space-y-6',
      horizontal: 'space-y-4',
      inline: 'flex flex-wrap items-end gap-4',
    },
  },
  defaultVariants: {
    layout: 'vertical',
  },
});

export interface FormProps
  extends React.FormHTMLAttributes<HTMLFormElement>,
    VariantProps<typeof formVariants> {}

const Form = forwardRef<HTMLFormElement, FormProps>(
  ({ className, layout, ...props }, ref) => (
    <form
      ref={ref}
      className={cn(formVariants({ layout }), className)}
      {...props}
    />
  )
);

Form.displayName = 'Form';

// Form Item
export interface FormItemProps extends React.HTMLAttributes<HTMLDivElement> {
  label?: string;
  name?: string;
  required?: boolean;
  help?: string;
  layout?: 'vertical' | 'horizontal';
  labelCol?: string;
  wrapperCol?: string;
}

const FormItem = forwardRef<HTMLDivElement, FormItemProps>(
  ({
    className,
    label,
    name,
    required,
    help,
    layout = 'vertical',
    labelCol = 'w-24',
    wrapperCol = 'flex-1',
    children,
    ...props
  }, ref) => {
    const { errors, touched } = useFormContext();
    const error = name && errors?.[name];
    const isTouched = name && touched?.[name];
    const showError = error && isTouched;

    if (layout === 'horizontal') {
      return (
        <div
          ref={ref}
          className={cn('flex items-start space-x-4', className)}
          {...props}
        >
          {label && (
            <label
              htmlFor={name}
              className={cn(
                'pt-2 text-sm font-medium text-gray-700 dark:text-gray-300',
                required && "after:content-['*'] after:text-red-500 after:ml-1",
                labelCol
              )}
            >
              {label}
            </label>
          )}
          <div className={cn('space-y-1', wrapperCol)}>
            {children}
            {help && !showError && (
              <p className="text-sm text-gray-500 dark:text-gray-400">{help}</p>
            )}
            {showError && (
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            )}
          </div>
        </div>
      );
    }

    return (
      <div ref={ref} className={cn('space-y-2', className)} {...props}>
        {label && (
          <label
            htmlFor={name}
            className={cn(
              'block text-sm font-medium text-gray-700 dark:text-gray-300',
              required && "after:content-['*'] after:text-red-500 after:ml-1"
            )}
          >
            {label}
          </label>
        )}
        <div className="space-y-1">
          {children}
          {help && !showError && (
            <p className="text-sm text-gray-500 dark:text-gray-400">{help}</p>
          )}
          {showError && (
            <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
          )}
        </div>
      </div>
    );
  }
);

FormItem.displayName = 'FormItem';

// Form Label
export interface FormLabelProps
  extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean;
}

const FormLabel = forwardRef<HTMLLabelElement, FormLabelProps>(
  ({ className, required, children, ...props }, ref) => (
    <label
      ref={ref}
      className={cn(
        'block text-sm font-medium text-gray-700 dark:text-gray-300',
        required && "after:content-['*'] after:text-red-500 after:ml-1",
        className
      )}
      {...props}
    >
      {children}
    </label>
  )
);

FormLabel.displayName = 'FormLabel';

// Form Control
export interface FormControlProps extends React.HTMLAttributes<HTMLDivElement> {}

const FormControl = forwardRef<HTMLDivElement, FormControlProps>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('space-y-1', className)} {...props} />
  )
);

FormControl.displayName = 'FormControl';

// Form Description
export interface FormDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement> {}

const FormDescription = forwardRef<HTMLParagraphElement, FormDescriptionProps>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('text-sm text-gray-500 dark:text-gray-400', className)}
      {...props}
    />
  )
);

FormDescription.displayName = 'FormDescription';

// Form Message
export interface FormMessageProps
  extends React.HTMLAttributes<HTMLParagraphElement> {
  type?: 'error' | 'warning' | 'success' | 'info';
}

const FormMessage = forwardRef<HTMLParagraphElement, FormMessageProps>(
  ({ className, type = 'error', ...props }, ref) => (
    <p
      ref={ref}
      className={cn(
        'text-sm',
        {
          'text-red-600 dark:text-red-400': type === 'error',
          'text-yellow-600 dark:text-yellow-400': type === 'warning',
          'text-green-600 dark:text-green-400': type === 'success',
          'text-blue-600 dark:text-blue-400': type === 'info',
        },
        className
      )}
      {...props}
    />
  )
);

FormMessage.displayName = 'FormMessage';

// Form Group
export interface FormGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  description?: string;
}

const FormGroup = forwardRef<HTMLDivElement, FormGroupProps>(
  ({ className, title, description, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('space-y-4 p-6 border border-gray-200 rounded-lg dark:border-gray-700', className)}
      {...props}
    >
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  )
);

FormGroup.displayName = 'FormGroup';

// Form Actions
export interface FormActionsProps extends React.HTMLAttributes<HTMLDivElement> {
  align?: 'left' | 'center' | 'right';
}

const FormActions = forwardRef<HTMLDivElement, FormActionsProps>(
  ({ className, align = 'right', children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        'flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-700',
        {
          'justify-start': align === 'left',
          'justify-center': align === 'center',
          'justify-end': align === 'right',
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
);

FormActions.displayName = 'FormActions';

// Search Form
export interface SearchFormProps {
  onSearch: (values: Record<string, any>) => void;
  onReset?: () => void;
  loading?: boolean;
  children: React.ReactNode;
  className?: string;
}

export const SearchForm: React.FC<SearchFormProps> = ({
  onSearch,
  onReset,
  loading = false,
  children,
  className,
}) => {
  const [values, setValues] = React.useState<Record<string, any>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(values);
  };

  const handleReset = () => {
    setValues({});
    onReset?.();
  };

  const handleChange = (name: string, value: any) => {
    setValues(prev => ({ ...prev, [name]: value }));
  };

  return (
    <Form
      layout="inline"
      onSubmit={handleSubmit}
      className={cn('p-4 bg-gray-50 rounded-lg dark:bg-gray-800', className)}
    >
      <div className="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child as React.ReactElement<any>, {
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
                const name = e.target.name;
                const value = e.target.value;
                handleChange(name, value);
              },
              value: values[child.props.name] || '',
            });
          }
          return child;
        })}
      </div>
      <div className="flex gap-2">
        <button
          type="submit"
          disabled={loading}
          className="btn-primary"
        >
          {loading ? '搜索中...' : '搜索'}
        </button>
        <button
          type="button"
          onClick={handleReset}
          className="btn-secondary"
        >
          重置
        </button>
      </div>
    </Form>
  );
};

// Filter Form
export interface FilterFormProps {
  filters: Array<{
    name: string;
    label: string;
    type: 'input' | 'select' | 'date' | 'dateRange';
    options?: Array<{ label: string; value: any }>;
    placeholder?: string;
  }>;
  values: Record<string, any>;
  onChange: (name: string, value: any) => void;
  onClear?: () => void;
  className?: string;
}

export const FilterForm: React.FC<FilterFormProps> = ({
  filters,
  values,
  onChange,
  onClear,
  className,
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filters.map((filter) => (
          <FormItem key={filter.name} label={filter.label}>
            {filter.type === 'input' && (
              <input
                type="text"
                name={filter.name}
                value={values[filter.name] || ''}
                onChange={(e) => onChange(filter.name, e.target.value)}
                placeholder={filter.placeholder}
                className="input-primary"
              />
            )}
            {filter.type === 'select' && (
              <select
                name={filter.name}
                value={values[filter.name] || ''}
                onChange={(e) => onChange(filter.name, e.target.value)}
                className="input-primary"
              >
                <option value="">请选择</option>
                {filter.options?.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            )}
            {filter.type === 'date' && (
              <input
                type="date"
                name={filter.name}
                value={values[filter.name] || ''}
                onChange={(e) => onChange(filter.name, e.target.value)}
                className="input-primary"
              />
            )}
          </FormItem>
        ))}
      </div>
      {onClear && (
        <div className="flex justify-end">
          <button
            type="button"
            onClick={onClear}
            className="btn-secondary"
          >
            清空筛选
          </button>
        </div>
      )}
    </div>
  );
};

export {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormGroup,
  FormActions,
  formVariants,
};
