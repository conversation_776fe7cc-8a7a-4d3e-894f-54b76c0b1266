// API 配置和请求工具
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  (config) => {
    let token = localStorage.getItem('access_token');

    // 开发环境下使用默认测试 token
    if (!token && process.env.NODE_ENV === 'development') {
      token = 'dev-test-token';
    }

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理认证错误
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/api/v1/auth/refresh`, {
            refresh_token: refreshToken,
          });

          const { access_token } = response.data;
          localStorage.setItem('access_token', access_token);

          // 重试原始请求
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // 刷新失败，清除token并跳转到登录页
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// API 响应类型定义
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  status_code?: number;
  type?: string;
}

// 通用API请求方法
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.get(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.post(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.put(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.delete(url, config),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> =>
    apiClient.patch(url, data, config),
};

// 认证相关API
export const authApi = {
  login: (credentials: { email: string; password: string }) =>
    api.post('/api/v1/auth/login', credentials),
  
  register: (userData: { username: string; email: string; password: string }) =>
    api.post('/api/v1/auth/register', userData),
  
  logout: () =>
    api.post('/api/v1/auth/logout'),
  
  refreshToken: (refreshToken: string) =>
    api.post('/api/v1/auth/refresh', { refresh_token: refreshToken }),
  
  getProfile: () =>
    api.get('/api/v1/auth/profile'),
};

// 监控任务相关API
export const monitorApi = {
  // 获取监控任务列表
  getTasks: (params?: { page?: number; limit?: number; status?: string }) =>
    api.get('/api/v1/monitors', { params }),
  
  // 创建监控任务
  createTask: (taskData: any) =>
    api.post('/api/v1/monitors', taskData),
  
  // 获取监控任务详情
  getTask: (id: string) =>
    api.get(`/api/v1/monitors/${id}`),
  
  // 更新监控任务
  updateTask: (id: string, taskData: any) =>
    api.put(`/api/v1/monitors/${id}`, taskData),
  
  // 删除监控任务
  deleteTask: (id: string) =>
    api.delete(`/api/v1/monitors/${id}`),
};

// 全局搜索相关API
export const globalSearchApi = {
  // 获取全局搜索任务列表
  getTasks: (params?: { page?: number; limit?: number; status?: string }) =>
    api.get('/api/global-search/tasks', { params }),

  // 创建全局搜索任务
  createTask: (taskData: {
    name: string;
    description?: string;
    keywords: string[];
    platforms: string[];
    schedule_type: string;
    schedule_rule?: string;
  }) =>
    api.post('/api/global-search/tasks', {
      name: taskData.name,
      description: taskData.description || '',
      keywords: { list: taskData.keywords },
      platforms: { list: taskData.platforms },
      search_type: 'code',
      schedule: {
        type: taskData.schedule_type,
        rule: taskData.schedule_rule || ''
      },
      is_active: true,
      search_depth: 1,
      max_results: 1000,
      concurrent_limit: 3
    }),

  // 获取全局搜索任务详情
  getTask: (id: string) =>
    api.get(`/api/global-search/tasks/${id}`),

  // 更新全局搜索任务
  updateTask: (id: string, taskData: any) =>
    api.put(`/api/global-search/tasks/${id}`, taskData),

  // 删除全局搜索任务
  deleteTask: (id: string) =>
    api.delete(`/api/global-search/tasks/${id}`),

  // 执行快速搜索
  quickSearch: (searchData: {
    keywords: string[];
    platforms: string[];
    max_results?: number;
  }) =>
    api.post('/api/global-search/search', {
      keywords: { list: searchData.keywords },
      platforms: { list: searchData.platforms },
      search_type: 'code',
      max_results: searchData.max_results || 100,
      search_depth: 1,
      concurrent_limit: 3
    }),
  
  // 获取搜索结果
  getResults: (params?: {
    task_id?: string;
    page?: number;
    limit?: number;
    platform?: string;
    severity?: string;
  }) =>
    api.get('/api/global-search/results', { params }),
  
  // 获取搜索趋势
  getTrends: (params?: { days?: number }) =>
    api.get('/api/v1/stats/search-trends', { params }),
};

// 平台账号管理API
export const accountApi = {
  // 获取平台账号列表
  getAccounts: (params?: { platform?: string; status?: string }) =>
    api.get('/api/accounts', { params }),
  
  // 添加平台账号
  createAccount: (accountData: {
    platform: string;
    account_type: string;
    username?: string;
    token: string;
    refresh_token?: string;
    description?: string;
    is_active?: boolean;
    priority?: number;
    rate_limit?: number;
    rate_remaining?: number;
  }) =>
    api.post('/api/accounts', accountData),
  
  // 获取平台账号详情
  getAccount: (id: string) =>
    api.get(`/api/accounts/${id}`),

  // 更新平台账号
  updateAccount: (id: string, accountData: any) =>
    api.put(`/api/accounts/${id}`, accountData),

  // 删除平台账号
  deleteAccount: (id: string) =>
    api.delete(`/api/accounts/${id}`),

  // 验证平台账号
  validateAccount: (id: string) =>
    api.post(`/api/accounts/${id}/validate`),

  // 获取账号配额信息
  getRateLimit: (id: string) =>
    api.get(`/api/accounts/${id}/rate-limit`),
  
  // 获取平台使用统计
  getPlatformStats: () =>
    api.get('/api/v1/stats/platform-usage'),
};

// 系统相关API
export const systemApi = {
  // 获取支持的平台列表
  getPlatforms: () =>
    api.get('/api/v1/system/platforms'),

  // 获取系统健康状态
  getHealth: () =>
    api.get('/health'),
};

// 白名单相关API
export const whitelistAPI = {
  // 获取白名单列表
  list: (params?: any) =>
    api.get('/api/v1/whitelist', { params }),

  // 创建白名单条目
  create: (data: any) =>
    api.post('/api/v1/whitelist', data),

  // 获取白名单详情
  get: (id: string) =>
    api.get(`/api/v1/whitelist/${id}`),

  // 更新白名单条目
  update: (id: string, data: any) =>
    api.put(`/api/v1/whitelist/${id}`, data),

  // 删除白名单条目
  delete: (id: string) =>
    api.delete(`/api/v1/whitelist/${id}`),

  // 检查是否在白名单中
  check: (platform: string, repo: string) =>
    api.get('/api/v1/whitelist/check', { params: { platform, repo } }),
};

// 仪表板统计API
export const dashboardApi = {
  // 获取总体统计
  getOverallStats: () =>
    api.get('/api/v1/dashboard/stats'),

  // 获取趋势数据
  getTrendData: (params?: { days?: number }) =>
    api.get('/api/v1/dashboard/trends', { params }),

  // 获取风险分布
  getRiskDistribution: () =>
    api.get('/api/v1/dashboard/risk-distribution'),

  // 获取平台使用统计
  getPlatformStats: () =>
    api.get('/api/v1/dashboard/platform-stats'),

  // 获取最近活动
  getRecentActivity: (params?: { limit?: number }) =>
    api.get('/api/v1/dashboard/recent-activity', { params }),

  // 获取任务状态统计
  getTaskStats: () =>
    api.get('/api/v1/dashboard/task-stats'),
};

// 通知管理API
export const notificationApi = {
  // 获取通知渠道列表
  getChannels: () =>
    api.get('/api/notifications/channels'),

  // 创建通知渠道
  createChannel: (channelData: {
    name: string;
    type: string;
    config: any;
    is_active?: boolean;
  }) =>
    api.post('/api/notifications/channels', channelData),

  // 更新通知渠道
  updateChannel: (id: string, channelData: any) =>
    api.put(`/api/notifications/channels/${id}`, channelData),

  // 删除通知渠道
  deleteChannel: (id: string) =>
    api.delete(`/api/notifications/channels/${id}`),

  // 测试通知渠道
  testChannel: (id: string) =>
    api.post(`/api/notifications/channels/${id}/test`),

  // 获取通知历史
  getHistory: (params?: { limit?: number; offset?: number }) =>
    api.get('/api/notifications/history', { params }),

  // 获取通知模板
  getTemplates: () =>
    api.get('/api/notifications/templates'),

  // 创建通知模板
  createTemplate: (templateData: {
    name: string;
    type: string;
    subject?: string;
    content: string;
  }) =>
    api.post('/api/notifications/templates', templateData),
};

export default api;
