import React, { createContext, useContext, useEffect, useState } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'user';
  avatar?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // 检查本地存储的认证状态
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 确保在客户端环境中运行
        if (typeof window !== 'undefined') {
          const token = localStorage.getItem('access_token');
          if (token) {
            // TODO: 验证token并获取用户信息
            // 这里暂时使用模拟数据
            const mockUser: User = {
              id: '1',
              email: '<EMAIL>',
              name: '管理员',
              role: 'admin',
            };
            setUser(mockUser);
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        if (typeof window !== 'undefined') {
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
        }
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      // TODO: 实际的登录API调用
      // const response = await fetch('/api/auth/login', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ email, password }),
      // });

      // 模拟登录
      if (email === '<EMAIL>' && password === 'admin123') {
        const mockUser: User = {
          id: '1',
          email: '<EMAIL>',
          name: '管理员',
          role: 'admin',
        };

        // 使用开发环境的测试token
        const mockToken = 'dev-test-token';
        if (typeof window !== 'undefined') {
          localStorage.setItem('access_token', mockToken);
          localStorage.setItem('refresh_token', 'mock-refresh-token');
        }
        setUser(mockUser);
      } else {
        throw new Error('邮箱或密码错误');
      }
    } catch (error) {
      throw error;
    }
  };

  const register = async (name: string, email: string, password: string) => {
    try {
      // TODO: 实际的注册API调用
      // const response = await fetch('/api/auth/register', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ name, email, password }),
      // });

      // 模拟注册
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 注册成功后不自动登录，让用户手动登录
    } catch (error) {
      throw new Error('注册失败，请稍后重试');
    }
  };

  const updateProfile = async (data: Partial<User>) => {
    try {
      // TODO: 实际的更新API调用
      // const response = await fetch('/api/auth/profile', {
      //   method: 'PUT',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      //   },
      //   body: JSON.stringify(data),
      // });

      // 模拟更新
      if (user) {
        setUser({ ...user, ...data });
      }
    } catch (error) {
      throw new Error('更新失败，请稍后重试');
    }
  };

  const logout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
    setUser(null);
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    updateProfile,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
