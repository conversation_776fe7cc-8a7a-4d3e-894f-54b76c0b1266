import React, { useState, useEffect } from 'react';
import {
  EyeIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { format, subDays } from 'date-fns';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Card, Badge } from '@/components/ui';
import { dashboardApi } from '@/lib/api';

// 格式化活动时间
const formatActivityTime = (createdAt: string) => {
  const now = new Date();
  const activityTime = new Date(createdAt);
  const diffInMinutes = Math.floor((now.getTime() - activityTime.getTime()) / (1000 * 60));

  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`;
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}小时前`;
  } else {
    return `${Math.floor(diffInMinutes / 1440)}天前`;
  }
};

const DashboardPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardStats, setDashboardStats] = useState<any>(null);
  const [trendData, setTrendData] = useState<any[]>([]);
  const [riskDistribution, setRiskDistribution] = useState<any[]>([]);
  const [platformStats, setPlatformStats] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState<any[]>([]);
  const [taskStats, setTaskStats] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 并行加载所有数据
      const [
        statsResponse,
        trendsResponse,
        riskResponse,
        platformResponse,
        activityResponse,
        taskStatsResponse
      ] = await Promise.all([
        dashboardApi.getOverallStats(),
        dashboardApi.getTrendData({ days: 7 }),
        dashboardApi.getRiskDistribution(),
        dashboardApi.getPlatformStats(),
        dashboardApi.getRecentActivity({ limit: 10 }),
        dashboardApi.getTaskStats()
      ]);

      setDashboardStats(statsResponse.data.data);
      setTrendData(trendsResponse.data.data || []);
      setRiskDistribution(riskResponse.data.data || []);
      setPlatformStats(platformResponse.data.data || []);
      setRecentActivities(activityResponse.data.data || []);
      setTaskStats(taskStatsResponse.data.data || []);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      // 使用默认数据作为后备
      setDashboardStats({
        total_tasks: 0,
        active_tasks: 0,
        total_results: 0,
        high_risk_results: 0,
        total_accounts: 0,
        active_accounts: 0,
        today_tasks: 0,
        today_results: 0
      });
    } finally {
      setLoading(false);
    }
  };

  // 统计数据
  const stats = dashboardStats ? [
    {
      name: '总任务数',
      value: dashboardStats.total_tasks?.toString() || '0',
      change: `活跃: ${dashboardStats.active_tasks || 0}`,
      changeType: 'neutral' as const,
      icon: EyeIcon,
      bgColor: 'bg-blue-100',
      iconColor: 'text-blue-600',
    },
    {
      name: '今日结果',
      value: dashboardStats.today_results?.toString() || '0',
      change: `总计: ${dashboardStats.total_results || 0}`,
      changeType: 'neutral' as const,
      icon: DocumentTextIcon,
      bgColor: 'bg-green-100',
      iconColor: 'text-green-600',
    },
    {
      name: '高风险威胁',
      value: dashboardStats.high_risk_results?.toString() || '0',
      change: `今日: ${dashboardStats.today_tasks || 0}`,
      changeType: 'neutral' as const,
      icon: ExclamationTriangleIcon,
      bgColor: 'bg-yellow-100',
      iconColor: 'text-yellow-600',
    },
    {
      name: '平台账号',
      value: dashboardStats.total_accounts?.toString() || '0',
      change: `活跃: ${dashboardStats.active_accounts || 0}`,
      changeType: 'neutral' as const,
      icon: CheckCircleIcon,
      bgColor: 'bg-green-100',
      iconColor: 'text-green-600',
    },
  ] : [];

  // 处理最近活动数据
  const processedActivities = recentActivities.map(activity => ({
    id: activity.id,
    repository: activity.description?.split(' - ')[0] || '未知仓库',
    riskLevel: activity.status === 'high' ? 'high' : activity.status === 'medium' ? 'medium' : 'low',
    type: activity.title,
    foundAt: formatActivityTime(activity.created_at),
    status: activity.type === 'search_result' || activity.type === 'scan_result' ? 'pending' : 'resolved',
  }));

  const getRiskBadgeVariant = (level: string) => {
    switch (level) {
      case 'high': return 'destructive';
      case 'medium': return 'warning';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'resolved': return 'success';
      case 'pending': return 'warning';
      case 'ignored': return 'secondary';
      default: return 'secondary';
    }
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
            <p className="mt-1 text-sm text-gray-600">加载中...</p>
          </div>
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
          <p className="mt-1 text-sm text-gray-600">
            监控概览和最新扫描结果
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <Card key={stat.name} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`p-2 ${stat.bgColor} rounded-lg`}>
                    <stat.icon className={`h-6 w-6 ${stat.iconColor}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
                <div className="flex items-center text-gray-600">
                  <span className="text-sm font-medium">{stat.change}</span>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* 图表区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 趋势图表 */}
          <Card className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">扫描趋势</h2>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="tasks"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  name="任务数量"
                />
                <Line
                  type="monotone"
                  dataKey="results"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="结果数量"
                />
                <Line
                  type="monotone"
                  dataKey="threats"
                  stroke="#ef4444"
                  strokeWidth={2}
                  name="威胁数量"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>

          {/* 风险分布饼图 */}
          <Card className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">风险分布</h2>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={riskDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {riskDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </div>

        {/* 仓库统计 */}
        <Card className="p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">仓库扫描统计</h2>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={platformStats}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="scans" fill="#3b82f6" name="扫描次数" />
              <Bar dataKey="threats" fill="#ef4444" name="威胁数量" />
            </BarChart>
          </ResponsiveContainer>
        </Card>

        {/* 最新扫描结果 */}
        <Card>
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">最新扫描结果</h3>
          </div>
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    仓库
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    风险等级
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    发现时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {processedActivities.slice(0, 5).map((result) => (
                  <tr key={result.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {result.repository}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant={getRiskBadgeVariant(result.riskLevel)}>
                        {result.riskLevel === 'high' && '高风险'}
                        {result.riskLevel === 'medium' && '中风险'}
                        {result.riskLevel === 'low' && '低风险'}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {result.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {result.foundAt}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant={getStatusBadgeVariant(result.status)}>
                        {result.status === 'resolved' && '已处理'}
                        {result.status === 'pending' && '待处理'}
                        {result.status === 'ignored' && '已忽略'}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      </div>
    </ProtectedRoute>
  );
};

export default DashboardPage;
