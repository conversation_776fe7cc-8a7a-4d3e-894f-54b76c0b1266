import React, { useState, useEffect } from 'react';
import Link from 'next/link';

interface NotificationConfig {
  id: string;
  name: string;
  type: string;
  config: any;
  is_active: boolean;
  created_at: string;
}

const NotificationsPage: React.FC = () => {
  const [configs, setConfigs] = useState<NotificationConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchConfigs();
  }, []);

  const fetchConfigs = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('未登录');
        return;
      }

      const response = await fetch('http://localhost:8080/api/notifications/channels', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取通知配置失败');
      }

      const data = await response.json();
      setConfigs(data.data?.configs || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'email': return '邮件';
      case 'webhook': return 'Webhook';
      case 'dingtalk': return '钉钉';
      case 'feishu': return '飞书';
      case 'wechat_work': return '企业微信';
      default: return type;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">通知管理</h1>
            <p className="text-gray-600 mt-1">管理告警通知配置</p>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">通知管理</h1>
            <p className="text-gray-600 mt-1">管理告警通知配置</p>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-red-600">错误: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">通知管理</h1>
          <p className="text-gray-600 mt-1">管理告警通知配置</p>
        </div>
        <Link href="/notifications/channels">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            新建通知配置
          </button>
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">通知配置列表</h3>
        </div>
        
        {configs.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            暂无通知配置
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    配置名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    通知类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {configs.map((config) => (
                  <tr key={config.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{config.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {getTypeLabel(config.type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        config.is_active 
                          ? 'text-green-600 bg-green-100' 
                          : 'text-gray-600 bg-gray-100'
                      }`}>
                        {config.is_active ? '启用' : '禁用'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(config.created_at).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button className="text-blue-600 hover:text-blue-900">
                        编辑
                      </button>
                      <button className="text-red-600 hover:text-red-900">
                        删除
                      </button>
                      <button className="text-green-600 hover:text-green-900">
                        测试
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 通知类型说明 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">支持的通知类型</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-gray-900">邮件通知</h4>
            <p className="text-sm text-gray-600 mt-1">通过SMTP发送邮件告警</p>
          </div>
          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-gray-900">Webhook</h4>
            <p className="text-sm text-gray-600 mt-1">发送HTTP请求到指定URL</p>
          </div>
          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-gray-900">钉钉机器人</h4>
            <p className="text-sm text-gray-600 mt-1">发送消息到钉钉群聊</p>
          </div>
          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-gray-900">飞书机器人</h4>
            <p className="text-sm text-gray-600 mt-1">发送消息到飞书群聊</p>
          </div>
          <div className="border rounded-lg p-4">
            <h4 className="font-medium text-gray-900">企业微信</h4>
            <p className="text-sm text-gray-600 mt-1">发送消息到企业微信群</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage;
