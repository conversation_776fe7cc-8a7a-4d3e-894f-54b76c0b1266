import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { notificationApi } from '@/lib/api';

interface NotificationChannel {
  id: string;
  name: string;
  type: string;
  config: any;
  is_active: boolean;
  created_at: string;
}

const NotificationChannelsPage: React.FC = () => {
  const router = useRouter();
  const [channels, setChannels] = useState<NotificationChannel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('email');
  const [mounted, setMounted] = useState(false);

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    type: 'email',
    config: {},
    description: '',
    is_active: true,
  });

  useEffect(() => {
    setMounted(true);
    fetchChannels();
  }, []);

  const fetchChannels = async () => {
    try {
      setLoading(true);
      const response = await notificationApi.getChannels();
      setChannels(response.data.channels || []);
      setError(null);
    } catch (err: any) {
      console.error('获取通知渠道失败:', err);
      setError(err.response?.data?.error || err.message || '获取通知渠道失败');
      setChannels([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateChannel = async () => {
    try {
      await notificationApi.createChannel(formData);
      alert('通知渠道创建成功');
      setShowAddModal(false);
      resetForm();
      await fetchChannels();
    } catch (err: any) {
      console.error('创建通知渠道失败:', err);
      alert(err.response?.data?.error || err.message || '创建失败');
    }
  };

  const handleDeleteChannel = async (id: string) => {
    if (!confirm('确定要删除这个通知渠道吗？')) return;

    try {
      await notificationApi.deleteChannel(id);
      alert('删除成功');
      await fetchChannels();
    } catch (err: any) {
      console.error('删除通知渠道失败:', err);
      alert(err.response?.data?.error || err.message || '删除失败');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      type: 'email',
      config: {},
      description: '',
      is_active: true,
    });
  };

  const getTypeLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      email: '邮件',
      webhook: 'Webhook',
      dingtalk: '钉钉',
      wechatwork: '企业微信',
      feishu: '飞书',
      slack: 'Slack',
    };
    return labels[type] || type;
  };

  const renderConfigForm = () => {
    switch (selectedType) {
      case 'email':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">SMTP服务器</label>
              <input
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="smtp.gmail.com"
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, host: e.target.value }
                }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">端口</label>
              <input
                type="number"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="587"
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, port: parseInt(e.target.value) }
                }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">用户名</label>
              <input
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, username: e.target.value }
                }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">密码</label>
              <input
                type="password"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, password: e.target.value }
                }))}
              />
            </div>
          </div>
        );
      case 'dingtalk':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Webhook URL</label>
              <input
                type="url"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="https://oapi.dingtalk.com/robot/send?access_token=..."
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, webhook_url: e.target.value }
                }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">密钥 (可选)</label>
              <input
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, secret: e.target.value }
                }))}
              />
            </div>
          </div>
        );
      case 'wechatwork':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Webhook URL</label>
              <input
                type="url"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=..."
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, webhook_url: e.target.value }
                }))}
              />
            </div>
          </div>
        );
      case 'feishu':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Webhook URL</label>
              <input
                type="url"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="https://open.feishu.cn/open-apis/bot/v2/hook/..."
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, webhook_url: e.target.value }
                }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">签名密钥 (可选)</label>
              <input
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, secret: e.target.value }
                }))}
              />
            </div>
          </div>
        );
      default:
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700">配置 (JSON)</label>
            <textarea
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              rows={4}
              placeholder='{"key": "value"}'
              onChange={(e) => {
                try {
                  const config = JSON.parse(e.target.value);
                  setFormData(prev => ({ ...prev, config }));
                } catch (err) {
                  // 忽略JSON解析错误
                }
              }}
            />
          </div>
        );
    }
  };

  if (!mounted) {
    return <div>Loading...</div>;
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">通知渠道</h1>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">通知渠道</h1>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-red-600">错误: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">通知渠道</h1>
          <p className="text-gray-600 mt-1">配置告警通知的发送渠道</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          新建渠道
        </button>
      </div>

      {/* 渠道列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                名称
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {channels.map((channel) => (
              <tr key={channel.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{channel.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {getTypeLabel(channel.type)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    channel.is_active 
                      ? 'text-green-600 bg-green-100' 
                      : 'text-gray-600 bg-gray-100'
                  }`}>
                    {channel.is_active ? '启用' : '禁用'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(channel.created_at).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => handleDeleteChannel(channel.id)}
                    className="text-red-600 hover:text-red-900 ml-4"
                  >
                    删除
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {channels.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">暂无通知渠道</p>
          </div>
        )}
      </div>

      {/* 添加渠道模态框 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">新建通知渠道</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">渠道名称</label>
                <input
                  type="text"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">渠道类型</label>
                <select
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  value={selectedType}
                  onChange={(e) => {
                    setSelectedType(e.target.value);
                    setFormData(prev => ({ ...prev, type: e.target.value, config: {} }));
                  }}
                >
                  <option value="email">邮件</option>
                  <option value="dingtalk">钉钉</option>
                  <option value="wechatwork">企业微信</option>
                  <option value="feishu">飞书</option>
                  <option value="webhook">Webhook</option>
                </select>
              </div>

              {renderConfigForm()}

              <div>
                <label className="block text-sm font-medium text-gray-700">描述</label>
                <textarea
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  rows={2}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false);
                  resetForm();
                }}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handleCreateChannel}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                创建
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationChannelsPage;
