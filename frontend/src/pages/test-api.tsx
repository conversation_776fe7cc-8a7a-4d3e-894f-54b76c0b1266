import React, { useState, useEffect } from 'react';
import { systemApi, accountApi } from '@/lib/api';

const TestApiPage: React.FC = () => {
  const [platforms, setPlatforms] = useState<string[]>([]);
  const [accounts, setAccounts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // 测试账号 API
        console.log('测试账号 API...');
        const accountResponse = await accountApi.getAccounts();
        console.log('账号 API 响应:', accountResponse);
        setAccounts((accountResponse.data as any) || []);

        // 测试平台 API
        console.log('测试平台 API...');
        const platformResponse = await systemApi.getPlatforms();
        console.log('平台 API 响应:', platformResponse);
        const platforms = (platformResponse.data as any)?.platforms || (platformResponse.data as any)?.data?.platforms || [];
        setPlatforms(platforms);

      } catch (err: any) {
        console.error('API 错误:', err);
        setError(err.message || '获取数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">API 连接测试</h1>
        
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">账号列表 ({accounts.length} 个)</h2>

          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">加载中...</span>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800">错误: {error}</p>
            </div>
          )}

          {!loading && !error && (
            <div className="space-y-2">
              {accounts.length > 0 ? (
                <div className="space-y-2">
                  {accounts.map((account, index) => (
                    <div key={account.id || index} className="p-3 bg-gray-50 rounded border">
                      <div className="font-medium">{account.username}</div>
                      <div className="text-sm text-gray-600">
                        平台: {account.platform} | 状态: {account.status}
                      </div>
                      <div className="text-xs text-gray-500">
                        ID: {account.id}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">未找到账号数据</p>
              )}
            </div>
          )}
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">支持的平台</h2>
          
          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">加载中...</span>
            </div>
          )}
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800">错误: {error}</p>
            </div>
          )}
          
          {!loading && !error && (
            <div className="space-y-2">
              {platforms.length > 0 ? (
                <ul className="list-disc list-inside">
                  {platforms.map((platform, index) => (
                    <li key={index} className="text-gray-700 capitalize">
                      {platform}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">未找到支持的平台</p>
              )}
            </div>
          )}
        </div>
        
        <div className="mt-6 bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">API 配置信息</h2>
          <div className="space-y-2 text-sm">
            <p><strong>API Base URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8082'}</p>
            <p><strong>环境:</strong> {process.env.NODE_ENV}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestApiPage;
