import React, { useState, useEffect } from 'react';
import { PlusIcon, TrashIcon, PencilIcon } from '@heroicons/react/24/outline';
import { whitelistAPI } from '../../lib/api';
import { Card, Button, Badge, Input, Select, Modal } from '@/components/ui';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

interface WhitelistItem {
  id: string;
  type: string;
  platform: string;
  identifier: string;
  reason: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface WhitelistListResponse {
  items: WhitelistItem[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

const WhitelistPage: React.FC = () => {
  const [whitelists, setWhitelists] = useState<WhitelistItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<WhitelistItem | null>(null);
  const [formData, setFormData] = useState({
    type: 'repository',
    platform: 'github',
    identifier: '',
    reason: '',
    status: 'active'
  });

  // 加载白名单列表
  const loadWhitelists = async () => {
    setLoading(true);
    try {
      const response = await whitelistAPI.list();
      
      if (response.data) {
        const data = response.data as WhitelistListResponse;
        setWhitelists(data.items || []);
      }
    } catch (error) {
      console.error('加载白名单失败:', error);
      alert('加载白名单失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWhitelists();
  }, []);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingItem) {
        await whitelistAPI.update(editingItem.id, formData);
        alert('更新成功');
      } else {
        await whitelistAPI.create(formData);
        alert('添加成功');
      }
      
      setModalVisible(false);
      setEditingItem(null);
      setFormData({
        type: 'repository',
        platform: 'github',
        identifier: '',
        reason: '',
        status: 'active'
      });
      loadWhitelists();
    } catch (error) {
      console.error('操作失败:', error);
      alert('操作失败');
    }
  };

  // 删除白名单条目
  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这个白名单条目吗？')) return;
    
    try {
      await whitelistAPI.delete(id);
      alert('删除成功');
      loadWhitelists();
    } catch (error) {
      console.error('删除失败:', error);
      alert('删除失败');
    }
  };

  // 打开编辑模态框
  const handleEdit = (item: WhitelistItem) => {
    setEditingItem(item);
    setFormData({
      type: item.type,
      platform: item.platform,
      identifier: item.identifier,
      reason: item.reason,
      status: item.status
    });
    setModalVisible(true);
  };

  // 打开新增模态框
  const handleAdd = () => {
    setEditingItem(null);
    setFormData({
      type: 'repository',
      platform: 'github',
      identifier: '',
      reason: '',
      status: 'active'
    });
    setModalVisible(true);
  };

  const getTypeText = (type: string) => {
    const typeMap: { [key: string]: string } = {
      repository: '仓库',
      user: '用户',
      organization: '组织',
    };
    return typeMap[type] || type;
  };

  const getPlatformText = (platform: string) => {
    const platformMap: { [key: string]: string } = {
      github: 'GitHub',
      gitlab: 'GitLab',
      gitee: 'Gitee',
    };
    return platformMap[platform] || platform;
  };

  return (
    <ProtectedRoute>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">白名单管理</h1>
          <Button onClick={handleAdd}>
            <PlusIcon className="h-4 w-4 mr-2" />
            添加白名单
          </Button>
        </div>

        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : (
          <div className="space-y-4">
            {whitelists.length === 0 ? (
              <Card className="p-8 text-center">
                <p className="text-gray-500">暂无白名单条目</p>
              </Card>
            ) : (
              whitelists.map((item) => (
                <Card key={item.id} className="p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge variant={item.type === 'repository' ? 'default' : 'secondary'}>
                          {getTypeText(item.type)}
                        </Badge>
                        <Badge variant="outline">
                          {getPlatformText(item.platform)}
                        </Badge>
                        <Badge variant={item.status === 'active' ? 'success' : 'secondary'}>
                          {item.status === 'active' ? '启用' : '禁用'}
                        </Badge>
                      </div>
                      <h3 className="font-medium text-lg">{item.identifier}</h3>
                      <p className="text-gray-600 mt-1">{item.reason}</p>
                      <p className="text-sm text-gray-400 mt-2">
                        创建时间: {new Date(item.created_at).toLocaleString()}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(item)}
                      >
                        <PencilIcon className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(item.id)}
                      >
                        <TrashIcon className="h-4 w-4 mr-1" />
                        删除
                      </Button>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        )}

        {/* 添加/编辑模态框 */}
        <Modal
          open={modalVisible}
          onClose={() => {
            setModalVisible(false);
            setEditingItem(null);
          }}
        >
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              {editingItem ? '编辑白名单' : '添加白名单'}
            </h2>
            <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                类型
              </label>
              <Select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              >
                <option value="repository">仓库</option>
                <option value="user">用户</option>
                <option value="organization">组织</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                平台
              </label>
              <Select
                value={formData.platform}
                onChange={(e) => setFormData({ ...formData, platform: e.target.value })}
              >
                <option value="github">GitHub</option>
                <option value="gitlab">GitLab</option>
                <option value="gitee">Gitee</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                标识符
              </label>
              <Input
                value={formData.identifier}
                onChange={(e) => setFormData({ ...formData, identifier: e.target.value })}
                placeholder="仓库格式: owner/repo，用户格式: username"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                原因
              </label>
              <textarea
                value={formData.reason}
                onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
                placeholder="请输入加入白名单的原因"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                状态
              </label>
              <Select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
              >
                <option value="active">启用</option>
                <option value="disabled">禁用</option>
              </Select>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setModalVisible(false);
                  setEditingItem(null);
                }}
              >
                取消
              </Button>
              <Button type="submit">
                {editingItem ? '更新' : '添加'}
              </Button>
            </div>
          </form>
          </div>
        </Modal>
      </div>
    </ProtectedRoute>
  );
};

export default WhitelistPage;
