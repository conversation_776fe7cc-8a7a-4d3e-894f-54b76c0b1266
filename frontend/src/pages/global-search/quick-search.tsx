import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { globalSearchApi, systemApi } from '@/lib/api';
import { Platform, SearchResult, QuickSearchResponse } from '@/types/global-search';

const QuickSearchPage: React.FC = () => {
  const router = useRouter();
  const [keywords, setKeywords] = useState<string[]>(['']);
  const [selectedPlatforms, setSelectedPlatforms] = useState<Platform[]>([]);
  const [availablePlatforms, setAvailablePlatforms] = useState<Platform[]>([]);
  const [maxResults, setMaxResults] = useState(100);
  const [searching, setSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchResponse, setSearchResponse] = useState<QuickSearchResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 获取可用平台
  useEffect(() => {
    const fetchPlatforms = async () => {
      try {
        const response = await systemApi.getPlatforms();
        const platforms = (response.data as any)?.platforms || (response.data as any)?.data?.platforms || [];
        setAvailablePlatforms(platforms);
        setSelectedPlatforms(platforms);
      } catch (err) {
        console.error('获取平台列表失败:', err);
      }
    };
    fetchPlatforms();
  }, []);

  // 添加关键词
  const addKeyword = () => {
    setKeywords([...keywords, '']);
  };

  // 删除关键词
  const removeKeyword = (index: number) => {
    if (keywords.length > 1) {
      setKeywords(keywords.filter((_, i) => i !== index));
    }
  };

  // 更新关键词
  const updateKeyword = (index: number, value: string) => {
    const newKeywords = [...keywords];
    newKeywords[index] = value;
    setKeywords(newKeywords);
  };

  // 切换平台选择
  const togglePlatform = (platform: Platform) => {
    if (selectedPlatforms.includes(platform)) {
      setSelectedPlatforms(selectedPlatforms.filter(p => p !== platform));
    } else {
      setSelectedPlatforms([...selectedPlatforms, platform]);
    }
  };

  // 执行搜索
  const handleSearch = async () => {
    const validKeywords = keywords.filter(k => k.trim());
    if (validKeywords.length === 0) {
      setError('请至少输入一个关键词');
      return;
    }
    if (selectedPlatforms.length === 0) {
      setError('请至少选择一个平台');
      return;
    }

    try {
      setSearching(true);
      setError(null);
      setSearchResults([]);
      
      const response = await globalSearchApi.quickSearch({
        keywords: validKeywords,
        platforms: selectedPlatforms,
        max_results: maxResults,
      });

      const searchData = response.data as QuickSearchResponse;
      setSearchResponse(searchData);
      setSearchResults(searchData.results || []);
    } catch (err: any) {
      setError(err.response?.data?.message || '搜索失败');
    } finally {
      setSearching(false);
    }
  };

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 平台图标映射
  const getPlatformIcon = (platform: Platform) => {
    switch (platform) {
      case 'github': return '🐙';
      case 'gitlab': return '🦊';
      case 'gitee': return '🌟';
      default: return '📁';
    }
  };

  return (
    <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">快速搜索</h1>
            <p className="text-gray-600 mt-1">在多个平台中快速搜索关键词</p>
          </div>
          <button
            onClick={() => router.push('/global-search')}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            返回任务列表
          </button>
        </div>

        {/* 搜索表单 */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="space-y-6">
            {/* 关键词输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索关键词
              </label>
              <div className="space-y-2">
                {keywords.map((keyword, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={keyword}
                      onChange={(e) => updateKeyword(index, e.target.value)}
                      placeholder="输入关键词，如：password、API_KEY、secret"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    {keywords.length > 1 && (
                      <button
                        onClick={() => removeKeyword(index)}
                        className="p-2 text-red-600 hover:text-red-800"
                      >
                        ✕
                      </button>
                    )}
                  </div>
                ))}
                <button
                  onClick={addKeyword}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  + 添加关键词
                </button>
              </div>
            </div>

            {/* 平台选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索平台
              </label>
              <div className="flex flex-wrap gap-3">
                {availablePlatforms.map((platform) => (
                  <label key={platform} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedPlatforms.includes(platform)}
                      onChange={() => togglePlatform(platform)}
                      className="rounded border-gray-300"
                    />
                    <span className="flex items-center space-x-1">
                      <span>{getPlatformIcon(platform)}</span>
                      <span className="capitalize">{platform}</span>
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* 最大结果数 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最大结果数
              </label>
              <select
                value={maxResults}
                onChange={(e) => setMaxResults(Number(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
                <option value={500}>500</option>
              </select>
            </div>

            {/* 搜索按钮 */}
            <div className="flex justify-end">
              <button
                onClick={handleSearch}
                disabled={searching}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {searching ? '搜索中...' : '开始搜索'}
              </button>
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* 搜索进度 */}
        {searching && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
              <span className="text-blue-800">正在搜索中，请稍候...</span>
            </div>
          </div>
        )}

        {/* 搜索结果统计 */}
        {searchResponse && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-green-800">搜索完成</h3>
                <p className="text-green-600">
                  找到 {searchResponse.total_results || 0} 个结果，
                  搜索了 {searchResponse.platforms_searched?.length || 0} 个平台，
                  耗时 {searchResponse.search_time || 0} 秒
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 搜索结果 */}
        {searchResults.length > 0 && (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">搜索结果</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {searchResults.map((result) => (
                <div key={result.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-lg">{getPlatformIcon(result.platform)}</span>
                        <a
                          href={result.repository_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {result.repository_name}
                        </a>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(result.severity)}`}>
                          {result.severity}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600 mb-2">
                        <a
                          href={result.file_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:text-blue-600"
                        >
                          {result.file_path}
                        </a>
                      </div>
                      <div className="bg-gray-100 rounded p-3 text-sm font-mono">
                        <div className="text-gray-600 mb-1">匹配内容:</div>
                        <div className="text-gray-900">{result.matched_content}</div>
                      </div>
                      <div className="flex flex-wrap gap-1 mt-2">
                        {result.matched_keywords.map((keyword, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="ml-4 text-right text-sm text-gray-500">
                      <div>风险评分: {result.risk_score}</div>
                      <div>{new Date(result.created_at).toLocaleString()}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 无结果提示 */}
        {searchResponse && searchResults.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配结果</h3>
            <p className="text-gray-600">尝试使用不同的关键词或选择更多平台</p>
          </div>
        )}
    </div>
  );
};

export default QuickSearchPage;
