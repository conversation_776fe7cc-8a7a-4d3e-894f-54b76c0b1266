import React, { useState, useEffect } from 'react';

interface GlobalSearchTask {
  id: string;
  name: string;
  description: string;
  platforms: string[];
  keywords: string[];
  status: string;
  created_at: string;
  total_runs: number;
  success_runs: number;
  failed_runs: number;
}

const GlobalSearchPage: React.FC = () => {
  const [tasks, setTasks] = useState<GlobalSearchTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    platforms: ['github'],
    keywords: [''],
  });

  useEffect(() => {
    fetchTasks();
  }, []);

  const fetchTasks = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('未登录');
        return;
      }

      const response = await fetch('/api/monitors', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取全局搜索任务失败');
      }

      const data = await response.json();
      setTasks(data.data?.tasks || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const createTask = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        alert('未登录');
        return;
      }

      const response = await fetch('/api/monitors', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          platforms: formData.platforms,
          keywords: formData.keywords.filter(k => k.trim() !== ''),
          file_types: [],
          exclude_rules: {},
          schedule_type: 'manual',
        }),
      });

      if (!response.ok) {
        throw new Error('创建任务失败');
      }

      alert('任务创建成功');
      setShowCreateForm(false);
      setFormData({
        name: '',
        description: '',
        platforms: ['github'],
        keywords: [''],
      });
      fetchTasks();
    } catch (err) {
      alert(err instanceof Error ? err.message : '创建失败');
    }
  };

  const addKeyword = () => {
    setFormData({
      ...formData,
      keywords: [...formData.keywords, ''],
    });
  };

  const updateKeyword = (index: number, value: string) => {
    const newKeywords = [...formData.keywords];
    newKeywords[index] = value;
    setFormData({
      ...formData,
      keywords: newKeywords,
    });
  };

  const removeKeyword = (index: number) => {
    const newKeywords = formData.keywords.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      keywords: newKeywords.length > 0 ? newKeywords : [''],
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">全局搜索任务</h1>
            <p className="text-gray-600 mt-1">管理跨平台关键词搜索任务</p>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">全局搜索任务</h1>
            <p className="text-gray-600 mt-1">管理跨平台关键词搜索任务</p>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-red-600">错误: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">全局搜索任务</h1>
          <p className="text-gray-600 mt-1">管理跨平台关键词搜索任务</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          新建搜索任务
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">全局搜索任务列表</h3>
        </div>

        {tasks.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            暂无全局搜索任务
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    任务名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    平台
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    关键词
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    运行统计
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {tasks.map((task) => (
                  <tr key={task.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{task.name}</div>
                        <div className="text-sm text-gray-500">{task.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {task.platforms?.join(', ') || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex flex-wrap gap-1">
                        {task.keywords?.slice(0, 3).map((keyword, index) => (
                          <span key={index} className="bg-gray-100 px-2 py-1 rounded text-xs">
                            {keyword}
                          </span>
                        ))}
                        {task.keywords?.length > 3 && (
                          <span className="text-xs text-gray-500">+{task.keywords.length - 3}</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(task.status)}`}>
                        {task.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>总计: {task.total_runs}</div>
                      <div className="text-xs text-gray-500">
                        成功: {task.success_runs} | 失败: {task.failed_runs}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(task.created_at).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 创建任务模态框 */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">创建全局搜索任务</h3>
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">任务名称</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="输入任务名称"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">描述</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={3}
                    placeholder="输入任务描述"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">搜索平台</label>
                  <div className="mt-1 space-y-2">
                    {['github', 'gitlab', 'gitee'].map((platform) => (
                      <label key={platform} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.platforms.includes(platform)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData({
                                ...formData,
                                platforms: [...formData.platforms, platform],
                              });
                            } else {
                              setFormData({
                                ...formData,
                                platforms: formData.platforms.filter(p => p !== platform),
                              });
                            }
                          }}
                          className="mr-2"
                        />
                        {platform.toUpperCase()}
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">关键词</label>
                  <div className="mt-1 space-y-2">
                    {formData.keywords.map((keyword, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={keyword}
                          onChange={(e) => updateKeyword(index, e.target.value)}
                          className="flex-1 border border-gray-300 rounded-md px-3 py-2"
                          placeholder="输入关键词"
                        />
                        {formData.keywords.length > 1 && (
                          <button
                            onClick={() => removeKeyword(index)}
                            className="text-red-600 hover:text-red-800"
                          >
                            删除
                          </button>
                        )}
                      </div>
                    ))}
                    <button
                      onClick={addKeyword}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      + 添加关键词
                    </button>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <button
                    onClick={() => setShowCreateForm(false)}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
                  >
                    取消
                  </button>
                  <button
                    onClick={createTask}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                  >
                    创建任务
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlobalSearchPage;