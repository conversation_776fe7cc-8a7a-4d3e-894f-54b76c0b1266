import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { globalSearchApi, systemApi } from '@/lib/api';
import { Platform, ScheduleType, CreateGlobalSearchTaskRequest } from '@/types/global-search';

const CreateGlobalSearchTaskPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [availablePlatforms, setAvailablePlatforms] = useState<Platform[]>([]);
  const [formData, setFormData] = useState<CreateGlobalSearchTaskRequest>({
    name: '',
    description: '',
    keywords: [],
    platforms: [],
    schedule_type: 'manual',
    schedule_rule: '',
  });

  // 获取可用平台
  useEffect(() => {
    const fetchPlatforms = async () => {
      try {
        const response = await systemApi.getPlatforms();
        const platforms = (response.data as any)?.platforms || (response.data as any)?.data?.platforms || [];
        setAvailablePlatforms(platforms);
        setFormData(prev => ({ ...prev, platforms }));
      } catch (err) {
        console.error('获取平台列表失败:', err);
      }
    };

    fetchPlatforms();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('请输入任务名称');
      return;
    }

    if (formData.keywords.length === 0) {
      alert('请至少添加一个关键词');
      return;
    }

    if (formData.platforms.length === 0) {
      alert('请至少选择一个平台');
      return;
    }

    try {
      setLoading(true);
      await globalSearchApi.createTask(formData);
      alert('全局搜索任务创建成功');
      router.push('/global-search');
    } catch (err: any) {
      alert(err.response?.data?.message || '创建任务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleKeywordAdd = (keyword: string) => {
    if (keyword.trim() && !formData.keywords.includes(keyword.trim())) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, keyword.trim()]
      }));
    }
  };

  const handleKeywordRemove = (index: number) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter((_, i) => i !== index)
    }));
  };

  const handlePlatformToggle = (platform: Platform) => {
    setFormData(prev => ({
      ...prev,
      platforms: prev.platforms.includes(platform)
        ? prev.platforms.filter(p => p !== platform)
        : [...prev.platforms, platform]
    }));
  };

  return (
    <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">创建全局搜索任务</h1>
          <p className="text-gray-600 mt-1">配置关键词在多个代码平台进行全量搜索</p>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  任务名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="输入任务名称"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  调度类型
                </label>
                <select
                  value={formData.schedule_type}
                  onChange={(e) => handleInputChange('schedule_type', e.target.value as ScheduleType)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="manual">手动执行</option>
                  <option value="cron">定时执行</option>
                  <option value="interval">间隔执行</option>
                </select>
              </div>
            </div>

            {/* 描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                任务描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="描述任务的用途和目标"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* 调度规则 */}
            {formData.schedule_type !== 'manual' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  调度规则 *
                </label>
                <input
                  type="text"
                  value={formData.schedule_rule}
                  onChange={(e) => handleInputChange('schedule_rule', e.target.value)}
                  placeholder={
                    formData.schedule_type === 'cron' 
                      ? "例如: 0 */6 * * * (每6小时执行一次)"
                      : "例如: 6h (每6小时执行一次)"
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required={formData.schedule_type === 'interval' || formData.schedule_type === 'cron'}
                />
                <p className="text-sm text-gray-500 mt-1">
                  {formData.schedule_type === 'cron' 
                    ? "使用 Cron 表达式格式"
                    : "使用时间间隔格式，如: 1h, 30m, 1d"
                  }
                </p>
              </div>
            )}

            {/* 关键词配置 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索关键词 *
              </label>
              <div className="space-y-3">
                <div className="flex">
                  <input
                    type="text"
                    placeholder="输入关键词后按回车添加"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleKeywordAdd(e.currentTarget.value);
                        e.currentTarget.value = '';
                      }
                    }}
                  />
                  <button
                    type="button"
                    onClick={(e) => {
                      const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                      handleKeywordAdd(input.value);
                      input.value = '';
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors"
                  >
                    添加
                  </button>
                </div>
                
                {formData.keywords.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.keywords.map((keyword, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                      >
                        {keyword}
                        <button
                          type="button"
                          onClick={() => handleKeywordRemove(index)}
                          className="ml-2 text-blue-600 hover:text-blue-800"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* 平台选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                搜索平台 *
              </label>
              <div className="grid grid-cols-3 gap-4">
                {availablePlatforms.map((platform) => (
                  <label key={platform} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.platforms.includes(platform)}
                      onChange={() => handlePlatformToggle(platform)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700 capitalize">{platform}</span>
                  </label>
                ))}
              </div>
            </div>



            {/* 操作按钮 */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <button
                type="button"
                onClick={() => router.push('/global-search')}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                disabled={loading}
              >
                取消
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {loading ? '创建中...' : '创建任务'}
              </button>
            </div>
          </form>
        </div>
    </div>
  );
};

export default CreateGlobalSearchTaskPage;
