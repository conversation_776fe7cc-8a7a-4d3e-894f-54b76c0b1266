import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { globalSearchApi, whitelistAPI } from '@/lib/api';
import { SearchResult, Platform, Severity } from '@/types/global-search';

const GlobalSearchResultsPage: React.FC = () => {
  const router = useRouter();
  const { taskId } = router.query;
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    platform: '',
    severity: '',
    keyword: '',
  });
  const [whitelistLoading, setWhitelistLoading] = useState<string | null>(null);

  // 获取搜索结果
  const fetchResults = async () => {
    if (!taskId) return;
    
    try {
      setLoading(true);
      const response = await globalSearchApi.getResults({ task_id: taskId as string, ...filters });
      setResults(response.data.data || []);
    } catch (err: any) {
      setError(err.response?.data?.message || '获取搜索结果失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (taskId) {
      fetchResults();
    }
  }, [taskId, filters]);

  // 添加到白名单
  const handleAddToWhitelist = async (result: SearchResult) => {
    try {
      setWhitelistLoading(result.id);

      // 从仓库URL中提取仓库标识符
      const repoIdentifier = result.repository_name;

      await whitelistAPI.create({
        type: 'repository',
        platform: result.platform,
        identifier: repoIdentifier,
        reason: `从搜索结果添加 - 关键词: ${result.matched_keywords.join(', ')}`,
        status: 'active'
      });

      alert('已成功添加到白名单');
    } catch (error: any) {
      console.error('添加白名单失败:', error);
      alert('添加白名单失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setWhitelistLoading(null);
    }
  };

  // 严重程度颜色映射
  const getSeverityColor = (severity: Severity) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      case 'critical': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 平台图标映射
  const getPlatformIcon = (platform: Platform) => {
    switch (platform) {
      case 'github': return '🐙';
      case 'gitlab': return '🦊';
      case 'gitee': return '🌟';
      default: return '📁';
    }
  };

  // 处理过滤器变化
  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">搜索结果</h1>
            <p className="text-gray-600 mt-1">任务ID: {taskId}</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => router.push('/global-search')}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              返回任务列表
            </button>
            <button
              onClick={() => fetchResults()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              刷新结果
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* 过滤器 */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                平台
              </label>
              <select
                value={filters.platform}
                onChange={(e) => handleFilterChange('platform', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">全部平台</option>
                <option value="github">GitHub</option>
                <option value="gitlab">GitLab</option>
                <option value="gitee">Gitee</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                严重程度
              </label>
              <select
                value={filters.severity}
                onChange={(e) => handleFilterChange('severity', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">全部级别</option>
                <option value="critical">严重</option>
                <option value="high">高</option>
                <option value="medium">中</option>
                <option value="low">低</option>
                <option value="info">信息</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                关键词
              </label>
              <input
                type="text"
                value={filters.keyword}
                onChange={(e) => handleFilterChange('keyword', e.target.value)}
                placeholder="搜索关键词"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex items-end">
              <button
                onClick={() => setFilters({ platform: '', severity: '', keyword: '' })}
                className="w-full px-3 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
              >
                清除过滤器
              </button>
            </div>
          </div>
        </div>

        {/* 结果统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {['total', 'critical', 'high', 'medium'].map((type) => {
            let count = 0;
            let label = '';
            let icon = '';
            
            switch (type) {
              case 'total':
                count = results.length;
                label = '总结果数';
                icon = '📊';
                break;
              case 'critical':
                count = results.filter(r => r.severity === 'critical').length;
                label = '严重风险';
                icon = '🚨';
                break;
              case 'high':
                count = results.filter(r => r.severity === 'high').length;
                label = '高风险';
                icon = '⚠️';
                break;
              case 'medium':
                count = results.filter(r => r.severity === 'medium').length;
                label = '中风险';
                icon = '⚡';
                break;
            }

            return (
              <div key={type} className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="text-2xl">{icon}</div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          {label}
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {count}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 搜索结果列表 */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {results.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🔍</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无搜索结果</h3>
              <p className="text-gray-600">该任务还没有找到匹配的代码</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {results.map((result) => (
                <div key={result.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* 仓库信息 */}
                      <div className="flex items-center mb-2">
                        <span className="text-xl mr-2">{getPlatformIcon(result.platform)}</span>
                        <a
                          href={result.repository_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {result.repository_name}
                        </a>
                        <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(result.severity)}`}>
                          {result.severity}
                        </span>
                      </div>

                      {/* 文件路径 */}
                      <div className="text-sm text-gray-600 mb-2">
                        <span className="font-mono">{result.file_path}</span>
                        {result.line_number && (
                          <span className="ml-2">第 {result.line_number} 行</span>
                        )}
                      </div>

                      {/* 匹配内容 */}
                      <div className="bg-gray-50 rounded-lg p-3 mb-3">
                        <pre className="text-sm text-gray-800 whitespace-pre-wrap font-mono">
                          {result.matched_content}
                        </pre>
                      </div>

                      {/* 匹配关键词 */}
                      <div className="flex flex-wrap gap-2 mb-2">
                        {result.matched_keywords.map((keyword, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded text-xs bg-yellow-100 text-yellow-800"
                          >
                            {keyword}
                          </span>
                        ))}
                      </div>

                      {/* 元数据 */}
                      <div className="text-xs text-gray-500 space-x-4">
                        <span>发现时间: {new Date(result.created_at).toLocaleString()}</span>
                        {result.commit_hash && (
                          <span>提交: {result.commit_hash.substring(0, 8)}</span>
                        )}
                        {result.commit_author && (
                          <span>作者: {result.commit_author}</span>
                        )}
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="ml-4 flex-shrink-0">
                      <div className="flex flex-col space-y-2">
                        <div className="flex space-x-2">
                          <a
                            href={result.file_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            查看文件
                          </a>
                          {result.repository_url && (
                            <a
                              href={result.repository_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-green-600 hover:text-green-800 text-sm"
                            >
                              查看仓库
                            </a>
                          )}
                        </div>
                        <button
                          onClick={() => handleAddToWhitelist(result)}
                          disabled={whitelistLoading === result.id}
                          className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded border disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {whitelistLoading === result.id ? '添加中...' : '加入白名单'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
    </div>
  );
};

export default GlobalSearchResultsPage;
