import React, { useState } from 'react';
import {
  UserIcon,
  BellIcon,
  ShieldCheckIcon,
  CogIcon,
  KeyIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  PhotoIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Card, Button, Input, Tabs, TabsList, TabsTrigger, TabsContent, Select, Badge } from '@/components/ui';

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [showPassword, setShowPassword] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);

  // 个人资料状态
  const [profileData, setProfileData] = useState({
    name: '管理员',
    email: '<EMAIL>',
    phone: '+86 138 0013 8000',
    department: '技术部',
    position: '系统管理员',
    bio: '负责系统安全监控和用户管理',
  });

  // 通知设置状态
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    weeklyReport: true,
    securityAlerts: true,
    systemUpdates: false,
  });

  // 安全设置状态
  const [securityData, setSecurityData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorEnabled: false,
    sessionTimeout: '30',
    loginNotifications: true,
  });

  // 系统设置状态
  const [systemSettings, setSystemSettings] = useState({
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    theme: 'light',
    autoLogout: '60',
    maxLoginAttempts: '5',
  });

  // 处理函数
  const handleProfileUpdate = () => {
    // TODO: 实际的个人资料更新API调用
    console.log('Updating profile:', profileData);
  };

  const handlePasswordChange = () => {
    if (securityData.newPassword !== securityData.confirmPassword) {
      alert('新密码和确认密码不匹配');
      return;
    }
    // TODO: 实际的密码更改API调用
    console.log('Changing password');
  };

  const handleNotificationUpdate = () => {
    // TODO: 实际的通知设置更新API调用
    console.log('Updating notifications:', notificationSettings);
  };

  const handleSystemUpdate = () => {
    // TODO: 实际的系统设置更新API调用
    console.log('Updating system settings:', systemSettings);
  };

  const handleAvatarUpload = () => {
    // TODO: 头像上传功能
    console.log('Upload avatar');
  };

  return (
    <ProtectedRoute>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">设置</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理您的账户设置和系统配置
          </p>
        </div>

        {/* 设置标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile">
              <UserIcon className="h-4 w-4 mr-2" />
              个人资料
            </TabsTrigger>
            <TabsTrigger value="notifications">
              <BellIcon className="h-4 w-4 mr-2" />
              通知设置
            </TabsTrigger>
            <TabsTrigger value="security">
              <ShieldCheckIcon className="h-4 w-4 mr-2" />
              安全设置
            </TabsTrigger>
            <TabsTrigger value="system">
              <CogIcon className="h-4 w-4 mr-2" />
              系统设置
            </TabsTrigger>
          </TabsList>

          {/* 个人资料 */}
          <TabsContent value="profile">
            <div className="space-y-6">
              {/* 头像和基本信息 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-6">基本信息</h3>
                <div className="flex items-start space-x-6">
                  <div className="flex-shrink-0">
                    <div className="relative">
                      <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
                        <UserIcon className="h-12 w-12 text-gray-400" />
                      </div>
                      <button
                        onClick={handleAvatarUpload}
                        className="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-1.5 hover:bg-blue-700"
                      >
                        <PhotoIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div className="flex-1 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Input
                          label="姓名"
                          value={profileData.name}
                          onChange={(e) => setProfileData(prev => ({...prev, name: e.target.value}))}
                        />
                      </div>
                      <div>
                        <Input
                          label="邮箱"
                          type="email"
                          value={profileData.email}
                          onChange={(e) => setProfileData(prev => ({...prev, email: e.target.value}))}
                        />
                      </div>
                      <div>
                        <Input
                          label="手机号"
                          value={profileData.phone}
                          onChange={(e) => setProfileData(prev => ({...prev, phone: e.target.value}))}
                        />
                      </div>
                      <div>
                        <Input
                          label="部门"
                          value={profileData.department}
                          onChange={(e) => setProfileData(prev => ({...prev, department: e.target.value}))}
                        />
                      </div>
                      <div>
                        <Input
                          label="职位"
                          value={profileData.position}
                          onChange={(e) => setProfileData(prev => ({...prev, position: e.target.value}))}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        个人简介
                      </label>
                      <textarea
                        rows={3}
                        value={profileData.bio}
                        onChange={(e) => setProfileData(prev => ({...prev, bio: e.target.value}))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="请输入个人简介"
                      />
                    </div>
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <Button onClick={handleProfileUpdate}>
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    保存更改
                  </Button>
                </div>
              </Card>

              {/* 账户状态 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">账户状态</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CheckCircleIcon className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">账户状态</p>
                      <Badge variant="success">活跃</Badge>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">角色权限</p>
                      <Badge variant="default">管理员</Badge>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <UserIcon className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">注册时间</p>
                      <p className="text-sm text-gray-500">2024-01-01</p>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          {/* 通知设置 */}
          <TabsContent value="notifications">
            <div className="space-y-6">
              {/* 邮件通知 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">邮件通知</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">启用邮件通知</p>
                      <p className="text-sm text-gray-500">接收系统相关的邮件通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={notificationSettings.emailNotifications}
                        onChange={(e) => setNotificationSettings(prev => ({...prev, emailNotifications: e.target.checked}))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">安全警报</p>
                      <p className="text-sm text-gray-500">发现高风险问题时立即通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={notificationSettings.securityAlerts}
                        onChange={(e) => setNotificationSettings(prev => ({...prev, securityAlerts: e.target.checked}))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">周报</p>
                      <p className="text-sm text-gray-500">每周发送扫描结果摘要</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={notificationSettings.weeklyReport}
                        onChange={(e) => setNotificationSettings(prev => ({...prev, weeklyReport: e.target.checked}))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">系统更新</p>
                      <p className="text-sm text-gray-500">系统维护和更新通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={notificationSettings.systemUpdates}
                        onChange={(e) => setNotificationSettings(prev => ({...prev, systemUpdates: e.target.checked}))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <Button onClick={handleNotificationUpdate}>
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    保存设置
                  </Button>
                </div>
              </Card>

              {/* 其他通知方式 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">其他通知方式</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">短信通知</p>
                      <p className="text-sm text-gray-500">紧急情况下发送短信通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={notificationSettings.smsNotifications}
                        onChange={(e) => setNotificationSettings(prev => ({...prev, smsNotifications: e.target.checked}))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">浏览器推送</p>
                      <p className="text-sm text-gray-500">在浏览器中显示推送通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={notificationSettings.pushNotifications}
                        onChange={(e) => setNotificationSettings(prev => ({...prev, pushNotifications: e.target.checked}))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          {/* 安全设置 */}
          <TabsContent value="security">
            <div className="space-y-6">
              {/* 密码修改 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">修改密码</h3>
                <div className="space-y-4">
                  <div>
                    <Input
                      label="当前密码"
                      type={showCurrentPassword ? "text" : "password"}
                      value={securityData.currentPassword}
                      onChange={(e) => setSecurityData(prev => ({...prev, currentPassword: e.target.value}))}
                      rightIcon={
                        <button
                          type="button"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          {showCurrentPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                        </button>
                      }
                    />
                  </div>
                  <div>
                    <Input
                      label="新密码"
                      type={showNewPassword ? "text" : "password"}
                      value={securityData.newPassword}
                      onChange={(e) => setSecurityData(prev => ({...prev, newPassword: e.target.value}))}
                      rightIcon={
                        <button
                          type="button"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          {showNewPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                        </button>
                      }
                    />
                  </div>
                  <div>
                    <Input
                      label="确认新密码"
                      type="password"
                      value={securityData.confirmPassword}
                      onChange={(e) => setSecurityData(prev => ({...prev, confirmPassword: e.target.value}))}
                    />
                  </div>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div className="flex">
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-800">密码要求</h3>
                        <div className="mt-2 text-sm text-yellow-700">
                          <ul className="list-disc list-inside space-y-1">
                            <li>至少8个字符</li>
                            <li>包含大小写字母</li>
                            <li>包含数字和特殊字符</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <Button onClick={handlePasswordChange}>
                    <KeyIcon className="h-4 w-4 mr-2" />
                    更新密码
                  </Button>
                </div>
              </Card>

              {/* 会话管理 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">会话管理</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      会话超时时间（分钟）
                    </label>
                    <Select
                      value={securityData.sessionTimeout}
                      onChange={(e) => setSecurityData(prev => ({...prev, sessionTimeout: e.target.value}))}
                    >
                      <option value="15">15分钟</option>
                      <option value="30">30分钟</option>
                      <option value="60">1小时</option>
                      <option value="120">2小时</option>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">登录通知</p>
                      <p className="text-sm text-gray-500">新设备登录时发送通知</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={securityData.loginNotifications}
                        onChange={(e) => setSecurityData(prev => ({...prev, loginNotifications: e.target.checked}))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </Card>

              {/* API 密钥 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">API 密钥</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Input
                      value="godeye_xxxxxxxxxxxxxxxxxxxxxxxx"
                      readOnly
                      className="flex-1 font-mono text-sm"
                    />
                    <Button variant="outline">
                      <PencilIcon className="h-4 w-4 mr-2" />
                      重新生成
                    </Button>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div className="flex">
                      <InformationCircleIcon className="h-5 w-5 text-blue-400" />
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">API 密钥说明</h3>
                        <div className="mt-2 text-sm text-blue-700">
                          <p>API 密钥用于第三方集成和自动化脚本，请妥善保管。重新生成后旧密钥将立即失效。</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          {/* 系统设置 */}
          <TabsContent value="system">
            <div className="space-y-6">
              {/* 基本设置 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">基本设置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      语言
                    </label>
                    <Select
                      value={systemSettings.language}
                      onChange={(e) => setSystemSettings(prev => ({...prev, language: e.target.value}))}
                    >
                      <option value="zh-CN">简体中文</option>
                      <option value="en-US">English</option>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      时区
                    </label>
                    <Select
                      value={systemSettings.timezone}
                      onChange={(e) => setSystemSettings(prev => ({...prev, timezone: e.target.value}))}
                    >
                      <option value="Asia/Shanghai">Asia/Shanghai</option>
                      <option value="UTC">UTC</option>
                      <option value="America/New_York">America/New_York</option>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      日期格式
                    </label>
                    <Select
                      value={systemSettings.dateFormat}
                      onChange={(e) => setSystemSettings(prev => ({...prev, dateFormat: e.target.value}))}
                    >
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      主题
                    </label>
                    <Select
                      value={systemSettings.theme}
                      onChange={(e) => setSystemSettings(prev => ({...prev, theme: e.target.value}))}
                    >
                      <option value="light">浅色</option>
                      <option value="dark">深色</option>
                      <option value="auto">跟随系统</option>
                    </Select>
                  </div>
                </div>
              </Card>

              {/* 安全配置 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">安全配置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      自动登出时间（分钟）
                    </label>
                    <Select
                      value={systemSettings.autoLogout}
                      onChange={(e) => setSystemSettings(prev => ({...prev, autoLogout: e.target.value}))}
                    >
                      <option value="30">30分钟</option>
                      <option value="60">1小时</option>
                      <option value="120">2小时</option>
                      <option value="240">4小时</option>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      最大登录尝试次数
                    </label>
                    <Select
                      value={systemSettings.maxLoginAttempts}
                      onChange={(e) => setSystemSettings(prev => ({...prev, maxLoginAttempts: e.target.value}))}
                    >
                      <option value="3">3次</option>
                      <option value="5">5次</option>
                      <option value="10">10次</option>
                    </Select>
                  </div>
                </div>
              </Card>

              {/* 系统信息 */}
              <Card className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">系统信息</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">版本信息</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">系统版本:</span>
                        <span className="font-medium">GodEye v1.0.0</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">构建时间:</span>
                        <span className="font-medium">2024-01-15</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">API版本:</span>
                        <span className="font-medium">v1</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">运行状态</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">系统状态:</span>
                        <Badge variant="success">正常运行</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">运行时间:</span>
                        <span className="font-medium">15天 8小时</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">活跃用户:</span>
                        <span className="font-medium">5</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <div className="flex justify-end">
                <Button onClick={handleSystemUpdate}>
                  <CheckCircleIcon className="h-4 w-4 mr-2" />
                  保存设置
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  );
};

export default SettingsPage;
