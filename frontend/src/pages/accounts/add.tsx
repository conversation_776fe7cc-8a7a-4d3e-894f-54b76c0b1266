import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { accountApi } from '@/lib/api';
import { Platform, AccountType } from '@/types/global-search';

const AddAccountPage: React.FC = () => {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    platform: 'github' as Platform,
    username: '',
    token: '',
    account_type: 'personal' as AccountType,
    description: '',
    is_active: true,
    priority: 1,
    rate_limit: 5000,
    rate_remaining: 5000,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  // 安全导航函数
  const safeNavigate = (path: string) => {
    if (!mounted) return;
    // 移除有问题的路径检查，让Next.js处理路由
    router.push(path);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.token.trim()) {
      alert('请输入访问令牌');
      return;
    }

    if (!formData.username.trim()) {
      alert('请输入用户名');
      return;
    }

    try {
      setLoading(true);

      // 确保字段名与后端API匹配
      const accountData = {
        platform: formData.platform,
        account_type: formData.account_type,
        username: formData.username,
        token: formData.token,
        description: formData.description,
        is_active: formData.is_active,
        priority: formData.priority,
        rate_limit: formData.rate_limit,
        rate_remaining: formData.rate_remaining,
      };

      console.log('发送账号数据:', accountData);
      await accountApi.createAccount(accountData);
      alert('平台账号添加成功');
      safeNavigate('/accounts');
    } catch (err: any) {
      console.error('添加账号失败:', err);
      const errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || '添加账号失败';
      alert(`添加失败: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (!mounted) {
    return <div>Loading...</div>;
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">添加平台账号</h1>
        <p className="text-gray-600 mt-1">配置代码平台的API访问账号</p>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 平台选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              代码平台 *
            </label>
            <select
              value={formData.platform}
              onChange={(e) => handleInputChange('platform', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="github">GitHub</option>
              <option value="gitlab">GitLab</option>
              <option value="gitee">Gitee</option>
            </select>
          </div>

          {/* 用户名 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              用户名 *
            </label>
            <input
              type="text"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              placeholder="输入平台用户名"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          {/* 访问令牌 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              访问令牌 *
            </label>
            <input
              type="password"
              value={formData.token}
              onChange={(e) => handleInputChange('token', e.target.value)}
              placeholder="输入API访问令牌"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              请确保令牌具有代码搜索权限
            </p>
          </div>

          {/* 账号类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              账号类型
            </label>
            <select
              value={formData.account_type}
              onChange={(e) => handleInputChange('account_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="personal">个人账号</option>
              <option value="organization">组织账号</option>
              <option value="app">应用账号</option>
            </select>
          </div>

          {/* 描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="账号用途描述（可选）"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={() => safeNavigate('/accounts')}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {loading ? '添加中...' : '添加账号'}
            </button>
          </div>
        </form>
      </div>

      {/* 帮助信息 */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 mb-2">如何获取访问令牌？</h3>
        <div className="text-sm text-blue-700 space-y-2">
          <div>
            <strong>GitHub:</strong>
            <span className="ml-1">Settings → Developer settings → Personal access tokens → Generate new token</span>
          </div>
          <div>
            <strong>GitLab:</strong>
            <span className="ml-1">User Settings → Access Tokens → Add a personal access token</span>
          </div>
          <div>
            <strong>Gitee:</strong>
            <span className="ml-1">设置 → 私人令牌 → 生成新令牌</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddAccountPage;
