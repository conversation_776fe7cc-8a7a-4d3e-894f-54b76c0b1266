import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { accountApi } from '@/lib/api';
import { PlatformUsageStats, Platform } from '@/types/global-search';

const AccountStatsPage: React.FC = () => {
  const router = useRouter();
  const [stats, setStats] = useState<PlatformUsageStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('24h');

  // 获取使用统计
  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await accountApi.getPlatformStats();
      setStats(response.data.data || []);
    } catch (err: any) {
      setError(err.response?.data?.message || '获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, [timeRange]);

  // 平台图标映射
  const getPlatformIcon = (platform: Platform) => {
    switch (platform) {
      case 'github': return '🐙';
      case 'gitlab': return '🦊';
      case 'gitee': return '🌟';
      default: return '📁';
    }
  };

  // 计算使用率百分比
  const getUsagePercentage = (used: number, total: number) => {
    if (total === 0) return 0;
    return Math.round((used / total) * 100);
  };

  // 获取使用率颜色
  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600 bg-red-100';
    if (percentage >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">账号使用统计</h1>
            <p className="text-gray-600 mt-1">查看各平台账号的API使用情况</p>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="1h">最近1小时</option>
              <option value="24h">最近24小时</option>
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
            </select>
            <button
              onClick={() => router.push('/accounts')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              返回账号管理
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* 总体统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {['total_requests', 'requests_today', 'rate_limit_hits', 'success_rate'].map((metric) => {
            const totalValue = stats.reduce((sum, stat) => {
              switch (metric) {
                case 'total_requests': return sum + stat.total_requests;
                case 'requests_today': return sum + stat.requests_today;
                case 'rate_limit_hits': return sum + stat.rate_limit_hits;
                case 'success_rate': return sum + stat.success_rate;
                default: return sum;
              }
            }, 0);

            const labels = {
              total_requests: '总请求数',
              requests_today: '今日请求',
              rate_limit_hits: '限流次数',
              success_rate: '成功率'
            };

            const icons = {
              total_requests: '📊',
              requests_today: '📈',
              rate_limit_hits: '⏱️',
              success_rate: '✅'
            };

            return (
              <div key={metric} className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="text-2xl">{icons[metric as keyof typeof icons]}</div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          {labels[metric as keyof typeof labels]}
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {totalValue.toLocaleString()}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* 平台详细统计 */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">平台使用详情</h2>
          </div>
          
          {stats.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📈</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无统计数据</h3>
              <p className="text-gray-600">选择的时间范围内没有API使用记录</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      平台
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      总请求数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      成功/失败
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      限流次数
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      平均响应时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      配额使用率
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {stats.map((stat) => {
                    const successRate = Math.round(stat.success_rate);

                    // 使用rate_limit_hits作为配额使用指标
                    const quotaUsage = stat.rate_limit_hits;

                    return (
                      <tr key={stat.platform} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <span className="text-xl mr-2">{getPlatformIcon(stat.platform)}</span>
                            <span className="capitalize font-medium">{stat.platform}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {stat.total_requests.toLocaleString()}
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm">
                            <div className="text-green-600">
                              ✓ {successRate}%
                            </div>
                            <div className="text-gray-600">
                              今日: {stat.requests_today}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {stat.rate_limit_hits > 0 ? (
                            <span className="text-yellow-600">
                              {stat.rate_limit_hits}
                            </span>
                          ) : (
                            <span className="text-green-600">0</span>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {stat.avg_response_time}ms
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="flex-1">
                              <div className="flex justify-between text-sm mb-1">
                                <span>活跃账号: {stat.active_accounts}</span>
                                <span>总账号: {stat.total_accounts}</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    quotaUsage >= 10 ? 'bg-red-500' :
                                    quotaUsage >= 5 ? 'bg-yellow-500' : 'bg-green-500'
                                  }`}
                                  style={{ width: `${Math.min(quotaUsage * 10, 100)}%` }}
                                ></div>
                              </div>
                            </div>
                            <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getUsageColor(quotaUsage)}`}>
                              {quotaUsage}%
                            </span>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
    </div>
  );
};

export default AccountStatsPage;
