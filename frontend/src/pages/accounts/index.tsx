import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { accountApi } from '@/lib/api';
import { PlatformAccount, Platform, AccountStatus } from '@/types/global-search';

const AccountsPage: React.FC = () => {
  const router = useRouter();
  const [accounts, setAccounts] = useState<PlatformAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 获取账号列表
  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const response = await accountApi.getAccounts();
      setAccounts((response.data as any) || []);
    } catch (err: any) {
      setError(err.response?.data?.message || '获取账号列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 安全导航函数
  const safeNavigate = (path: string) => {
    if (!mounted) return;
    // 移除有问题的路径检查，让Next.js处理路由
    router.push(path);
  };

  useEffect(() => {
    setMounted(true);
    fetchAccounts();
  }, []);

  // 删除账号
  const handleDeleteAccount = async (id: string) => {
    if (!confirm('确定要删除这个账号吗？')) return;
    
    try {
      await accountApi.deleteAccount(id);
      await fetchAccounts();
    } catch (err: any) {
      setError(err.response?.data?.message || '删除账号失败');
    }
  };

  // 获取平台图标
  const getPlatformIcon = (platform: Platform) => {
    switch (platform) {
      case 'github': return '🐙';
      case 'gitlab': return '🦊';
      case 'gitee': return '🌟';
      default: return '📁';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: AccountStatus) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'inactive': return 'text-red-600 bg-red-100';
      case 'rate_limited': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取状态文本
  const getStatusText = (status: AccountStatus) => {
    switch (status) {
      case 'active': return '正常';
      case 'inactive': return '失效';
      case 'rate_limited': return '限流';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">平台账号管理</h1>
          <p className="text-gray-600 mt-1">管理各平台的API访问账号和密钥</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => safeNavigate('/accounts/stats')}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            使用统计
          </button>
          <button
            onClick={() => safeNavigate('/accounts/add')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            添加账号
          </button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {/* 账号统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {['github', 'gitlab', 'gitee', 'total'].map((platform) => {
          const platformAccounts = platform === 'total' 
            ? accounts 
            : accounts.filter(acc => acc.platform === platform);
          const activeAccounts = platformAccounts.filter(acc => acc.status === 'active');
          
          return (
            <div key={platform} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-2xl">
                      {platform === 'total' ? '📊' : getPlatformIcon(platform as Platform)}
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {platform === 'total' ? '总计' : platform.toUpperCase()}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {activeAccounts.length} / {platformAccounts.length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 账号列表 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">账号列表</h3>
          
          {accounts.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔑</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无平台账号</h3>
              <p className="text-gray-600">添加你的第一个平台账号以开始搜索</p>
              <button
                onClick={() => safeNavigate('/accounts/add')}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                添加账号
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      平台
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用户名
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      最后使用
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {accounts.map((account) => (
                    <tr key={account.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-lg mr-2">{getPlatformIcon(account.platform)}</span>
                          <span className="text-sm font-medium text-gray-900">
                            {account.platform.toUpperCase()}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{account.username}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(account.status)}`}>
                          {getStatusText(account.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {account.last_used_at ? new Date(account.last_used_at).toLocaleDateString() : '从未使用'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleDeleteAccount(account.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccountsPage;
