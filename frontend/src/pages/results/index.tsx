import React, { useState, useEffect } from 'react';

interface ScanResult {
  id: string;
  task_id: string;
  task_name: string;
  repository: string;
  file_path: string;
  file_url: string;
  keyword: string;
  content_snippet: string;
  risk_level: string;
  confidence: number;
  status: string;
  created_at: string;
}

const ResultsPage: React.FC = () => {
  const [results, setResults] = useState<ScanResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedResult, setSelectedResult] = useState<ScanResult | null>(null);

  useEffect(() => {
    fetchResults();
  }, []);

  const fetchResults = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('未登录');
        return;
      }

      const response = await fetch('http://localhost:8080/api/results', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取扫描结果失败');
      }

      const data = await response.json();
      setResults(data.data?.results || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskLevelLabel = (level: string) => {
    switch (level) {
      case 'high': return '高风险';
      case 'medium': return '中风险';
      case 'low': return '低风险';
      default: return level;
    }
  };

  const addToWhitelist = async (result: ScanResult, type: 'repository' | 'file') => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        alert('未登录');
        return;
      }

      const identifier = type === 'repository' ? result.repository : `${result.repository}/${result.file_path}`;

      const response = await fetch('http://localhost:8082/api/v1/whitelist', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: type,
          platform: 'github',
          identifier: identifier,
          reason: `从扫描结果添加${type === 'repository' ? '仓库' : '文件'}白名单`,
        }),
      });

      if (!response.ok) {
        throw new Error('添加白名单失败');
      }

      alert(`已成功添加${type === 'repository' ? '仓库' : '文件'}白名单`);
    } catch (err) {
      alert(err instanceof Error ? err.message : '操作失败');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">扫描结果</h1>
            <p className="text-gray-600 mt-1">查看和管理代码泄露扫描结果</p>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">扫描结果</h1>
            <p className="text-gray-600 mt-1">查看和管理代码泄露扫描结果</p>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-red-600">错误: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">扫描结果</h1>
          <p className="text-gray-600 mt-1">查看和管理代码泄露扫描结果</p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={fetchResults}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            刷新
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">扫描结果列表</h3>
        </div>

        {results.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            暂无扫描结果
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    仓库/文件
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    关键词
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    风险等级
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    置信度
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    发现时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {results.map((result) => (
                  <tr key={result.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{result.repository}</div>
                        <div className="text-sm text-gray-500">{result.file_path}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span className="bg-gray-100 px-2 py-1 rounded text-xs">{result.keyword}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRiskLevelColor(result.risk_level)}`}>
                        {getRiskLevelLabel(result.risk_level)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {Math.round(result.confidence * 100)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(result.created_at).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => setSelectedResult(result)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        查看详情
                      </button>
                      <div className="relative inline-block">
                        <button
                          onClick={() => addToWhitelist(result, 'repository')}
                          className="text-green-600 hover:text-green-900"
                        >
                          仓库白名单
                        </button>
                      </div>
                      <div className="relative inline-block">
                        <button
                          onClick={() => addToWhitelist(result, 'file')}
                          className="text-yellow-600 hover:text-yellow-900"
                        >
                          文件白名单
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 详情模态框 */}
      {selectedResult && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">扫描结果详情</h3>
                <button
                  onClick={() => setSelectedResult(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">仓库</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedResult.repository}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">文件路径</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedResult.file_path}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">关键词</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedResult.keyword}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">代码片段</label>
                  <pre className="mt-1 text-sm text-gray-900 bg-gray-100 p-3 rounded overflow-x-auto">
                    {selectedResult.content_snippet}
                  </pre>
                </div>

                <div className="flex justify-end space-x-2">
                  <button
                    onClick={() => setSelectedResult(null)}
                    className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
                  >
                    关闭
                  </button>
                  {selectedResult.file_url && (
                    <a
                      href={selectedResult.file_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                    >
                      查看源文件
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsPage;
