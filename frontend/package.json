{"name": "godeye-frontend", "version": "1.0.0", "description": "GodEye GitHub 代码泄露监控系统前端", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@ant-design/icons": "^5.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "antd": "^5.12.0", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.0", "next": "^14.0.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "react-query": "^3.39.0", "recharts": "^2.15.4", "socket.io-client": "^4.7.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}