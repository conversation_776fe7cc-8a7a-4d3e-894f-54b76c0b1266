{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6", "es2020"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/styles/*": ["./src/styles/*"], "@/utils/*": ["./src/utils/*"], "@/hooks/*": ["./src/hooks/*"], "@/types/*": ["./src/types/*"], "@/lib/*": ["./src/lib/*"], "@/config/*": ["./src/config/*"], "@/services/*": ["./src/services/*"], "@/store/*": ["./src/store/*"], "@/constants/*": ["./src/constants/*"]}, "types": ["node", "jest", "@testing-library/jest-dom"], "declaration": false, "declarationMap": false, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*", "pages/**/*", "components/**/*", "lib/**/*", "utils/**/*", "types/**/*", "hooks/**/*", "config/**/*", "services/**/*", "store/**/*", "constants/**/*"], "exclude": ["node_modules", ".next", "out", "dist", "build", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "ts-node": {"compilerOptions": {"module": "CommonJS"}}}