# GodEye 简化架构设计

## 🎯 架构简化目标

基于您的反馈，将GodEye从复杂的微服务架构简化为类似Hawkeye的简单架构：

### 原始复杂架构（9个容器）❌
- postgres-dev, redis-dev, elasticsearch-dev, rabbitmq-dev
- auth-dev, monitor-dev, notification-dev  
- frontend-dev, nginx-dev

### 新简化架构（3个容器）✅
- **godeye-postgres**: PostgreSQL + Redis数据层
- **godeye-redis**: Redis缓存层
- **godeye-app**: Go后端 + React前端 + Nginx（单体应用）

## 📊 与Hawkeye架构对比

| 组件 | Hawkeye | GodEye简化版 | 说明 |
|------|---------|-------------|------|
| 数据库 | MongoDB | PostgreSQL | 更强的一致性 |
| 缓存 | Redis | Redis | 保持一致 |
| 后端 | Python Flask | Go Gin | 性能提升 |
| 前端 | Vue.js | React | 现代化UI |
| 代理 | OpenResty | Nginx | 简化配置 |
| 架构 | 单体 | 单体 | 保持简单 |

## 🔧 技术栈简化

### 移除的复杂组件
- ❌ Elasticsearch（搜索功能用PostgreSQL全文搜索替代）
- ❌ RabbitMQ（异步任务用Go goroutine替代）
- ❌ 微服务架构（合并为单体应用）
- ❌ 多个独立服务（认证、监控、通知合并）

### 保留的核心组件
- ✅ PostgreSQL（主数据库）
- ✅ Redis（缓存和会话）
- ✅ Go后端（单体应用）
- ✅ React前端（现代UI）
- ✅ Nginx（反向代理）

## 📁 新的项目结构

```
godeye/
├── cmd/server/main.go              # 应用入口
├── internal/
│   ├── app/                        # 应用核心
│   ├── config/                     # 配置管理
│   ├── database/                   # 数据库层
│   ├── handlers/                   # HTTP处理器
│   ├── models/                     # 数据模型
│   ├── services/                   # 业务逻辑
│   └── scheduler/                  # 定时任务
├── frontend/                       # React前端
├── tests/                          # 测试文件
├── docker-compose.simple.yml       # 简化部署
├── Dockerfile.simple              # 单体构建
└── nginx.conf                     # Nginx配置
```

## 🚀 部署简化

### 一键启动
```bash
cd godeye
docker-compose -f docker-compose.simple.yml up -d
```

### 服务访问
- **前端界面**: http://localhost:80
- **API接口**: http://localhost:80/api/
- **健康检查**: http://localhost:80/health

## 🔄 功能迁移计划

### 1. 认证系统
- **Hawkeye**: Flask session + MongoDB
- **GodEye**: JWT + PostgreSQL
- **测试**: 登录/登出/权限验证

### 2. GitHub监控
- **Hawkeye**: PyGithub + MongoDB
- **GodEye**: go-github + PostgreSQL  
- **测试**: 账号管理/搜索/结果存储

### 3. 通知系统
- **Hawkeye**: 企业微信/钉钉/邮件
- **GodEye**: 相同通知渠道
- **测试**: 各种通知渠道发送

### 4. 定时任务
- **Hawkeye**: Huey + Redis
- **GodEye**: cron + Go goroutine
- **测试**: 定时搜索/清理任务

### 5. 前端界面
- **Hawkeye**: Vue.js + Element UI
- **GodEye**: React + Tailwind CSS
- **测试**: 页面功能/交互逻辑

## 🧪 测试策略

### 单元测试
```bash
go test ./internal/...
npm test
```

### 集成测试
```bash
go test -tags=integration ./tests/...
```

### 端到端测试
```bash
npm run e2e
```

### 功能对比测试
- 与Hawkeye功能逐一对比
- 确保100%功能对等
- 性能基准测试

## 📈 预期收益

### 简化收益
- 🚀 **部署简单**: 3个容器 vs 9个容器
- 🔧 **维护容易**: 单体应用 vs 微服务
- 🐛 **调试方便**: 统一日志 vs 分散日志
- 📦 **资源节省**: 更少的容器开销

### 功能保证
- ✅ **100%兼容**: 所有Hawkeye功能
- ✅ **性能提升**: Go vs Python
- ✅ **现代UI**: React vs Vue.js
- ✅ **测试完善**: 全面的测试覆盖

## 🎯 下一步行动

1. **完成简化架构代码**
2. **实现核心功能模块**
3. **编写完整测试套件**
4. **与Hawkeye功能对比验证**
5. **优化性能和用户体验**

这个简化架构将大大降低复杂性，同时保证功能完整性和可维护性。
