# 多阶段构建：前端构建
FROM node:18-alpine AS frontend-builder
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN if [ -f package.json ]; then npm ci --only=production; fi
COPY frontend/ ./
RUN if [ -f package.json ]; then npm run build; else mkdir -p dist && echo '<!DOCTYPE html><html><head><title>GodEye</title></head><body><h1>GodEye System</h1><p>Frontend not built</p></body></html>' > dist/index.html; fi

# 多阶段构建：Go后端构建
FROM golang:1.21-alpine AS backend-builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/server

# 最终运行镜像
FROM alpine:latest
RUN apk --no-cache add ca-certificates nginx
WORKDIR /app

# 复制Go二进制文件
COPY --from=backend-builder /app/main .

# 复制静态文件
COPY static ./static

# 复制前端构建文件
COPY --from=frontend-builder /app/frontend/dist ./frontend

# 复制Nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 复制启动脚本
COPY docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh

# 创建日志目录
RUN mkdir -p /app/logs

EXPOSE 80

CMD ["./docker-entrypoint.sh"]
