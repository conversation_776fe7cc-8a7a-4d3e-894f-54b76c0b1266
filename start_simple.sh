#!/bin/bash

echo "🚀 启动GodEye简化架构"
echo "========================"

# 检查Docker环境
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker服务未运行"
    exit 1
fi

echo "✅ Docker环境正常"

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.simple.yml down --remove-orphans

# 清理可能的端口占用
echo "🧹 清理端口占用..."
PORTS=(80 5432 6379)
for port in "${PORTS[@]}"; do
    if lsof -ti:$port &> /dev/null; then
        echo "⚠️  端口 $port 被占用，尝试释放..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 1
    fi
done

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose -f docker-compose.simple.yml up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose -f docker-compose.simple.yml ps

# 测试服务连通性
echo "🧪 测试服务连通性..."

# 测试数据库
if docker exec godeye-postgres pg_isready -U godeye &> /dev/null; then
    echo "✅ PostgreSQL 连接正常"
else
    echo "❌ PostgreSQL 连接失败"
fi

# 测试Redis
if docker exec godeye-redis redis-cli ping | grep -q PONG; then
    echo "✅ Redis 连接正常"
else
    echo "❌ Redis 连接失败"
fi

# 测试应用
if curl -s http://localhost:80/health > /dev/null; then
    echo "✅ GodEye应用 (80) 可访问"
else
    echo "❌ GodEye应用 (80) 不可访问"
fi

# 显示日志
echo "📋 显示最近的服务日志..."
echo "--- PostgreSQL 日志 ---"
docker-compose -f docker-compose.simple.yml logs --tail=5 postgres

echo "--- Redis 日志 ---"
docker-compose -f docker-compose.simple.yml logs --tail=5 redis

echo "--- GodEye应用 日志 ---"
docker-compose -f docker-compose.simple.yml logs --tail=10 godeye-app

# 显示访问信息
echo ""
echo "🎉 GodEye简化架构启动完成！"
echo "================================"
echo "📱 访问地址:"
echo "   主页: http://localhost:80"
echo "   API: http://localhost:80/api/"
echo "   健康检查: http://localhost:80/health"
echo ""
echo "🔑 默认登录信息:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "🛠️  服务管理:"
echo "   查看日志: docker-compose -f docker-compose.simple.yml logs -f"
echo "   停止服务: docker-compose -f docker-compose.simple.yml down"
echo "   重启服务: docker-compose -f docker-compose.simple.yml restart"
echo ""
echo "📊 架构对比:"
echo "   原架构: 9个容器 (复杂微服务)"
echo "   新架构: 3个容器 (简化单体)"
echo "   简化率: 67% 容器减少"
echo ""

# 运行功能测试
if command -v python3 &> /dev/null; then
    echo "🧪 运行功能测试..."
    python3 -c "
import requests
import json

def test_health():
    try:
        response = requests.get('http://localhost:80/health', timeout=5)
        if response.status_code == 200:
            print('✅ 健康检查通过')
            return True
        else:
            print(f'❌ 健康检查失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ 健康检查异常: {e}')
        return False

def test_api():
    try:
        response = requests.get('http://localhost:80/api/auth/check', timeout=5)
        # 401是预期的，因为没有认证
        if response.status_code in [401, 403]:
            print('✅ API接口响应正常')
            return True
        else:
            print(f'❌ API接口异常: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ API接口异常: {e}')
        return False

# 执行测试
health_ok = test_health()
api_ok = test_api()

if health_ok and api_ok:
    print('🎯 基础功能测试通过！')
else:
    print('⚠️  部分功能测试失败，请检查日志')
"
else
    echo "⚠️  Python3未安装，跳过功能测试"
fi

echo ""
echo "✨ 简化架构启动完成！请访问 http://localhost:80 开始使用"
