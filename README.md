# GodEye - GitHub 代码泄露监控系统

## 📋 项目概述

GodEye 是一个现代化的 GitHub 代码泄露监控系统，基于微服务架构设计，使用 Go + PostgreSQL + React + Next.js 技术栈开发。系统提供实时监控、智能分析、多渠道告警等功能，帮助企业和开发者及时发现和处理代码泄露风险。

## 🎯 核心功能

### 🔍 智能监控
- **实时扫描**: 持续监控 GitHub 公开仓库
- **关键词匹配**: 支持自定义关键词和正则表达式
- **智能过滤**: 减少误报，提高检测精度
- **多源监控**: 支持多个 GitHub 账号和组织

### 📊 数据分析
- **可视化仪表板**: 直观展示监控数据和趋势
- **风险评估**: 自动评估泄露风险等级
- **历史追踪**: 完整的监控历史记录
- **统计报告**: 定期生成监控报告

### 🔔 告警通知
- **多渠道通知**: 邮件、Webhook、钉钉等
- **智能告警**: 基于规则的灵活告警策略
- **实时推送**: 发现问题立即通知
- **告警管理**: 告警确认、处理状态跟踪

### 👥 用户管理
- **角色权限**: 基于 RBAC 的权限控制
- **多租户**: 支持多组织独立管理
- **审计日志**: 完整的操作审计记录
- **SSO 集成**: 支持企业单点登录

## 🏗️ 技术架构

### 后端技术栈
- **语言**: Go 1.21+
- **框架**: Gin Web Framework
- **数据库**: PostgreSQL 15+
- **缓存**: Redis 7+
- **搜索**: Elasticsearch 8+
- **消息队列**: RabbitMQ
- **认证**: JWT + RBAC

### 前端技术栈
- **框架**: React 18 + Next.js 14
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **组件库**: Ant Design / Chakra UI
- **状态管理**: Zustand / Redux Toolkit
- **图表**: Chart.js / Recharts

### 基础设施
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: Loki + Promtail
- **CI/CD**: GitHub Actions

## 🚀 快速开始

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- Git 2.30+
- Node.js 18+ (开发环境)
- Go 1.21+ (开发环境)

### 开发环境启动

1. **克隆项目**
```bash
git clone <repository-url> godeye
cd godeye
```

2. **配置环境变量**
```bash
cp .env.example .env.dev
# 编辑 .env.dev 文件，配置必要的环境变量
```

3. **启动开发环境**
```bash
./scripts/start-dev.sh
```

4. **访问应用**
- 前端: http://localhost:3000
- API: http://localhost:8080
- 监控: http://localhost:3001

### 生产环境部署

1. **配置生产环境**
```bash
cp .env.example .env
# 编辑 .env 文件，配置生产环境变量
```

2. **一键部署**
```bash
./scripts/deploy.sh
```

## 📁 项目结构

```
godeye/
├── README.md                   # 项目说明
├── .gitignore                 # Git 忽略文件
├── .env.example               # 环境变量模板
├── docker-compose.dev.yml     # 开发环境编排
├── docker-compose.prod.yml    # 生产环境编排
├── scripts/                   # 脚本目录
│   ├── start-dev.sh           # 启动开发环境
│   ├── stop-dev.sh            # 停止开发环境
│   └── deploy.sh              # 生产部署脚本
├── docs/                      # 项目文档
│   ├── api/                   # API 文档
│   ├── deployment/            # 部署文档
│   ├── development/           # 开发文档
│   └── architecture/          # 架构文档
├── frontend/                  # React + Next.js 前端
│   ├── src/                   # 源代码
│   ├── public/                # 静态资源
│   ├── package.json           # 依赖配置
│   └── next.config.js         # Next.js 配置
├── services/                  # 微服务后端
│   ├── auth/                  # 认证服务
│   ├── monitor/               # 监控服务
│   └── notification/          # 通知服务
├── infrastructure/            # 基础设施配置
│   ├── nginx/                 # Nginx 配置
│   ├── database/              # 数据库相关
│   ├── monitoring/            # 监控配置
│   └── config/                # 其他配置
├── tests/                     # 测试文件
│   ├── integration/           # 集成测试
│   ├── e2e/                   # 端到端测试
│   └── performance/           # 性能测试
└── tools/                     # 开发工具
    ├── code-generator/        # 代码生成器
    ├── migration/             # 数据库迁移工具
    └── backup/                # 备份工具
```

## 🔧 开发指南

### 代码规范
- Go: 遵循 Go 官方代码规范，使用 golangci-lint
- TypeScript: 使用 ESLint + Prettier
- Git: 使用 Conventional Commits 规范

### 测试策略
- 单元测试: 覆盖率 > 80%
- 集成测试: 覆盖主要业务流程
- E2E 测试: 覆盖关键用户场景

### 部署流程
1. 开发分支提交代码
2. 自动运行测试和代码检查
3. 合并到主分支
4. 自动构建和部署

## 📊 监控和运维

### 系统监控
- **服务监控**: Prometheus + Grafana
- **日志监控**: Loki + Grafana
- **性能监控**: APM 集成
- **告警通知**: 多渠道告警

### 运维工具
- **健康检查**: 服务健康状态监控
- **自动扩缩**: 基于负载的自动扩缩容
- **备份恢复**: 自动化数据备份和恢复
- **版本管理**: 蓝绿部署和回滚

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！
