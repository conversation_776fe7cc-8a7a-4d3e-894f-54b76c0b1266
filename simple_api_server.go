package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	_ "github.com/lib/pq"
	"golang.org/x/crypto/bcrypt"
)

type User struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	IsActive bool   `json:"is_active"`
}

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type LoginResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	User    *User  `json:"user,omitempty"`
	Token   string `json:"token,omitempty"`
}

type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Database  string    `json:"database"`
	Version   string    `json:"version"`
}

var db *sql.DB

func main() {
	// 连接数据库
	var err error
	dbURL := "postgres://godeye:godeye123@localhost:5432/godeye?sslmode=disable"
	db, err = sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err = db.Ping(); err != nil {
		log.Fatalf("数据库ping失败: %v", err)
	}

	log.Println("数据库连接成功")

	// 设置路由
	http.HandleFunc("/health", healthHandler)
	http.HandleFunc("/api/auth/login", loginHandler)
	http.HandleFunc("/api/users", usersHandler)
	http.HandleFunc("/api/dashboard/stats", dashboardStatsHandler)

	// 启用CORS
	http.HandleFunc("/", corsHandler)

	log.Println("API服务器启动在端口 :8081")
	log.Fatal(http.ListenAndServe(":8081", nil))
}

func corsHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 如果不是API路径，返回404
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte("GodEye API Server"))
}

func healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")

	// 检查数据库连接
	dbStatus := "connected"
	if err := db.Ping(); err != nil {
		dbStatus = "disconnected"
	}

	response := HealthResponse{
		Status:    "ok",
		Timestamp: time.Now(),
		Database:  dbStatus,
		Version:   "1.0.0",
	}

	json.NewEncoder(w).Encode(response)
}

func loginHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		response := LoginResponse{
			Success: false,
			Message: "Invalid request format",
		}
		json.NewEncoder(w).Encode(response)
		return
	}

	// 查询用户
	var user User
	var passwordHash string
	query := "SELECT id, username, email, password_hash, is_active FROM users WHERE username = $1"
	err := db.QueryRow(query, req.Username).Scan(&user.ID, &user.Username, &user.Email, &passwordHash, &user.IsActive)

	if err != nil {
		response := LoginResponse{
			Success: false,
			Message: "Invalid username or password",
		}
		json.NewEncoder(w).Encode(response)
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(passwordHash), []byte(req.Password)); err != nil {
		response := LoginResponse{
			Success: false,
			Message: "Invalid username or password",
		}
		json.NewEncoder(w).Encode(response)
		return
	}

	if !user.IsActive {
		response := LoginResponse{
			Success: false,
			Message: "Account is disabled",
		}
		json.NewEncoder(w).Encode(response)
		return
	}

	// 登录成功
	response := LoginResponse{
		Success: true,
		Message: "Login successful",
		User:    &user,
		Token:   "mock-jwt-token-" + fmt.Sprintf("%d", time.Now().Unix()),
	}

	json.NewEncoder(w).Encode(response)
}

func usersHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "GET" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	rows, err := db.Query("SELECT id, username, email, is_active FROM users")
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var users []User
	for rows.Next() {
		var user User
		if err := rows.Scan(&user.ID, &user.Username, &user.Email, &user.IsActive); err != nil {
			continue
		}
		users = append(users, user)
	}

	json.NewEncoder(w).Encode(users)
}

func dashboardStatsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "GET" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	stats := map[string]interface{}{
		"total_users":       1,
		"active_queries":    0,
		"total_results":     0,
		"github_accounts":   0,
		"last_scan_time":    nil,
		"system_status":     "running",
		"database_status":   "connected",
	}

	// 获取实际统计数据
	db.QueryRow("SELECT COUNT(*) FROM users").Scan(&stats["total_users"])
	db.QueryRow("SELECT COUNT(*) FROM queries WHERE is_active = true").Scan(&stats["active_queries"])
	db.QueryRow("SELECT COUNT(*) FROM results").Scan(&stats["total_results"])
	db.QueryRow("SELECT COUNT(*) FROM github_accounts WHERE is_active = true").Scan(&stats["github_accounts"])

	json.NewEncoder(w).Encode(stats)
}
