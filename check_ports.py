#!/usr/bin/env python3
"""
GodEye 系统端口检查脚本
检查各个服务是否在指定端口上运行
"""

import socket
import sys
from datetime import datetime

def check_port(host, port, service_name):
    """检查指定端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ {service_name} (端口 {port}): 服务正在运行")
            return True
        else:
            print(f"❌ {service_name} (端口 {port}): 服务未运行")
            return False
    except Exception as e:
        print(f"❌ {service_name} (端口 {port}): 检查失败 - {e}")
        return False

def main():
    print("=" * 60)
    print("GodEye 系统状态检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 定义要检查的服务 (根据 docker-compose.dev.yml 的端口映射)
    services = [
        ("localhost", 3000, "前端开发服务器 (Next.js)"),
        ("localhost", 8080, "Nginx 网关"),
        ("localhost", 8081, "认证服务 (Auth)"),
        ("localhost", 8082, "监控服务 (Monitor)"),
        ("localhost", 8083, "通知服务 (Notification)"),
        ("localhost", 5433, "PostgreSQL 数据库"),
        ("localhost", 6380, "Redis 缓存"),
        ("localhost", 9201, "Elasticsearch"),
        ("localhost", 5673, "RabbitMQ"),
        ("localhost", 15673, "RabbitMQ 管理界面"),
    ]
    
    running_services = 0
    total_services = len(services)
    
    for host, port, name in services:
        if check_port(host, port, name):
            running_services += 1
    
    print("\n" + "=" * 60)
    print(f"检查结果: {running_services}/{total_services} 个服务正在运行")
    
    if running_services == 0:
        print("⚠️  没有任何服务在运行！请启动 Docker Compose 服务。")
        print("启动命令: cd godeye && docker-compose -f docker-compose.dev.yml up -d")
    elif running_services < total_services:
        print("⚠️  部分服务未运行，系统可能无法正常工作。")
    else:
        print("🎉 所有服务都在正常运行！")
    
    print("=" * 60)
    
    return running_services > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
