events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    sendfile        on;
    keepalive_timeout  65;
    
    # 上游Go应用服务器
    upstream godeye_backend {
        server 127.0.0.1:8080;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # 静态文件服务
        location / {
            root /app/static;
            try_files $uri $uri/ /index.html;
            index index.html;
        }
        
        # API代理到Go后端
        location /api/ {
            proxy_pass http://godeye_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 支持WebSocket
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
        
        # 健康检查
        location /health {
            proxy_pass http://godeye_backend/health;
        }
        
        # 日志配置
        access_log /app/logs/nginx_access.log;
        error_log /app/logs/nginx_error.log;
    }
}
