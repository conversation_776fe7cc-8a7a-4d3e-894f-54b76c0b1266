#!/bin/bash

# GodEye 系统测试和启动脚本
# 用于自动启动系统并进行基础验证

set -e

echo "🚀 GodEye 系统启动和测试脚本"
echo "================================"

# 检查 Docker 环境
echo "📋 检查 Docker 环境..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装或未启动"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker 服务未运行，请启动 Docker Desktop"
    exit 1
fi

echo "✅ Docker 环境正常"

# 检查 Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装"
    exit 1
fi

echo "✅ Docker Compose 可用"

# 进入项目目录
cd "$(dirname "$0")"
echo "📁 当前目录: $(pwd)"

# 停止可能存在的服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.dev.yml down --remove-orphans

# 清理可能的端口占用
echo "🧹 检查端口占用..."
PORTS=(3000 8080 8081 8082 8083 5433 6380 9201 5673 15673)
for port in "${PORTS[@]}"; do
    if lsof -ti:$port &> /dev/null; then
        echo "⚠️  端口 $port 被占用，尝试释放..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 1
    fi
done

# 启动服务
echo "🚀 启动 GodEye 服务..."
docker-compose -f docker-compose.dev.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose -f docker-compose.dev.yml ps

# 检查端口
echo "🔍 检查端口状态..."
for port in "${PORTS[@]}"; do
    if lsof -ti:$port &> /dev/null; then
        echo "✅ 端口 $port 正在使用"
    else
        echo "❌ 端口 $port 未使用"
    fi
done

# 等待更多时间让服务完全启动
echo "⏳ 等待服务完全启动..."
sleep 20

# 测试服务连通性
echo "🧪 测试服务连通性..."

# 测试 Nginx 网关
if curl -s http://localhost:8080 > /dev/null; then
    echo "✅ Nginx 网关 (8080) 可访问"
else
    echo "❌ Nginx 网关 (8080) 不可访问"
fi

# 测试前端服务
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 前端服务 (3000) 可访问"
else
    echo "❌ 前端服务 (3000) 不可访问"
fi

# 测试认证服务
if curl -s http://localhost:8081/health > /dev/null; then
    echo "✅ 认证服务 (8081) 健康检查通过"
else
    echo "❌ 认证服务 (8081) 健康检查失败"
fi

# 测试监控服务
if curl -s http://localhost:8082/health > /dev/null; then
    echo "✅ 监控服务 (8082) 健康检查通过"
else
    echo "❌ 监控服务 (8082) 健康检查失败"
fi

# 测试通知服务
if curl -s http://localhost:8083/health > /dev/null; then
    echo "✅ 通知服务 (8083) 健康检查通过"
else
    echo "❌ 通知服务 (8083) 健康检查失败"
fi

# 显示日志
echo "📋 显示最近的服务日志..."
echo "--- Nginx 日志 ---"
docker-compose -f docker-compose.dev.yml logs --tail=5 nginx-dev

echo "--- 前端日志 ---"
docker-compose -f docker-compose.dev.yml logs --tail=5 frontend-dev

echo "--- 认证服务日志 ---"
docker-compose -f docker-compose.dev.yml logs --tail=5 auth-dev

echo "--- 监控服务日志 ---"
docker-compose -f docker-compose.dev.yml logs --tail=5 monitor-dev

echo "--- 通知服务日志 ---"
docker-compose -f docker-compose.dev.yml logs --tail=5 notification-dev

# 显示访问信息
echo ""
echo "🎉 GodEye 系统启动完成！"
echo "================================"
echo "📱 访问地址:"
echo "   主页: http://localhost:8080"
echo "   前端开发服务器: http://localhost:3000"
echo ""
echo "🔑 默认登录信息:"
echo "   用户名: <EMAIL>"
echo "   密码: admin123"
echo ""
echo "🛠️  管理界面:"
echo "   RabbitMQ: http://localhost:15673 (godeye_dev/dev123)"
echo ""
echo "📊 服务状态:"
echo "   认证服务: http://localhost:8081/health"
echo "   监控服务: http://localhost:8082/health"
echo "   通知服务: http://localhost:8083/health"
echo ""
echo "📝 查看日志:"
echo "   docker-compose -f docker-compose.dev.yml logs -f [service-name]"
echo ""
echo "🛑 停止服务:"
echo "   docker-compose -f docker-compose.dev.yml down"
echo ""

# 运行 Python 端口检查脚本
if command -v python3 &> /dev/null; then
    echo "🐍 运行 Python 端口检查..."
    python3 check_ports.py
else
    echo "⚠️  Python3 未安装，跳过端口检查脚本"
fi

echo "✨ 启动完成！请访问 http://localhost:8080 开始使用 GodEye 系统"
