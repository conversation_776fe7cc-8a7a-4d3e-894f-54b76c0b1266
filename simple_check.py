#!/usr/bin/env python3

import requests
import json
import time

def check_service():
    print("🔍 GodEye 服务检查")
    print("==================")
    
    # 检查不同端口
    ports = [80, 8080, 3000]
    
    for port in ports:
        print(f"\n检查端口 {port}...")
        
        # 健康检查
        try:
            response = requests.get(f"http://localhost:{port}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ 端口 {port} 健康检查通过")
                print(f"响应: {response.text}")
                
                # 如果健康检查通过，尝试登录
                try:
                    login_data = {"username": "admin", "password": "admin123"}
                    login_response = requests.post(
                        f"http://localhost:{port}/api/auth/login", 
                        json=login_data, 
                        timeout=5
                    )
                    
                    if login_response.status_code == 200:
                        data = login_response.json()
                        if data.get("code") == 200:
                            print(f"✅ 端口 {port} 登录测试通过")
                            print(f"Token: {data.get('data', {}).get('token', 'N/A')[:20]}...")
                        else:
                            print(f"⚠️ 端口 {port} 登录失败: {data}")
                    else:
                        print(f"⚠️ 端口 {port} 登录HTTP错误: {login_response.status_code}")
                        
                except Exception as e:
                    print(f"❌ 端口 {port} 登录测试异常: {e}")
                    
            else:
                print(f"❌ 端口 {port} 健康检查失败: HTTP {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 端口 {port} 连接失败")
        except requests.exceptions.Timeout:
            print(f"❌ 端口 {port} 连接超时")
        except Exception as e:
            print(f"❌ 端口 {port} 检查异常: {e}")
    
    print("\n" + "="*50)
    print("检查完成！")

if __name__ == "__main__":
    check_service()
