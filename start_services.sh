#!/bin/bash

# GodEye 服务启动脚本
# 用于启动所有 Docker Compose 服务

set -e

echo "=========================================="
echo "GodEye 系统启动脚本"
echo "=========================================="

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker Desktop"
    exit 1
fi

echo "✅ Docker 正在运行"

# 检查 docker-compose 文件是否存在
if [ ! -f "docker-compose.dev.yml" ]; then
    echo "❌ 找不到 docker-compose.dev.yml 文件"
    echo "请确保在 godeye 目录下运行此脚本"
    exit 1
fi

echo "✅ 找到 docker-compose.dev.yml 文件"

# 停止可能正在运行的服务
echo "🔄 停止现有服务..."
docker-compose -f docker-compose.dev.yml down

# 构建并启动服务
echo "🚀 启动 GodEye 服务..."
docker-compose -f docker-compose.dev.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose -f docker-compose.dev.yml ps

echo ""
echo "=========================================="
echo "启动完成！"
echo "=========================================="
echo "前端访问地址: http://localhost:3000"
echo "Nginx网关地址: http://localhost:8080"
echo "认证服务: http://localhost:8081"
echo "监控服务: http://localhost:8082"
echo "通知服务: http://localhost:8083"
echo "RabbitMQ管理界面: http://localhost:15673"
echo ""
echo "默认登录账号:"
echo "用户名: <EMAIL>"
echo "密码: admin123"
echo "=========================================="

# 运行端口检查
if [ -f "check_ports.py" ]; then
    echo "🔍 运行端口检查..."
    python3 check_ports.py
fi
