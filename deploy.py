#!/usr/bin/env python3

import subprocess
import time
import os
import sys
import requests

def run_cmd(cmd, cwd=None, timeout=60):
    """执行命令"""
    try:
        print(f"🔧 执行: {cmd}")
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        if result.returncode == 0:
            print(f"✅ 成功")
            return True, result.stdout
        else:
            print(f"❌ 失败: {result.stderr}")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print(f"⏰ 超时")
        return False, "Command timeout"
    except Exception as e:
        print(f"💥 异常: {e}")
        return False, str(e)

def check_service(url, timeout=5):
    """检查服务是否可用"""
    try:
        response = requests.get(url, timeout=timeout)
        return response.status_code == 200
    except:
        return False

def main():
    print("🚀 GodEye Python 自动部署")
    print("========================")
    
    # 切换到godeye目录
    godeye_dir = os.path.join(os.getcwd(), "godeye")
    if not os.path.exists(godeye_dir):
        print("❌ godeye目录不存在")
        return False
    
    os.chdir(godeye_dir)
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 1. 停止现有服务
    print("\n1️⃣ 停止现有服务...")
    commands = [
        "docker-compose down --remove-orphans",
        "docker-compose -f docker-compose.simple.yml down --remove-orphans"
    ]
    
    for cmd in commands:
        run_cmd(cmd, timeout=30)
    
    # 2. 清理容器和镜像
    print("\n2️⃣ 清理资源...")
    cleanup_commands = [
        "docker container prune -f",
        "docker image prune -f",
        "docker network prune -f"
    ]
    
    for cmd in cleanup_commands:
        run_cmd(cmd, timeout=30)
    
    # 3. 构建镜像
    print("\n3️⃣ 构建镜像...")
    success, output = run_cmd("docker-compose -f docker-compose.simple.yml build --no-cache", timeout=300)
    if not success:
        print("❌ 镜像构建失败")
        return False
    
    # 4. 启动服务
    print("\n4️⃣ 启动服务...")
    success, output = run_cmd("docker-compose -f docker-compose.simple.yml up -d", timeout=60)
    if not success:
        print("❌ 服务启动失败")
        return False
    
    # 5. 等待服务就绪
    print("\n5️⃣ 等待服务就绪...")
    for i in range(30):
        if check_service("http://localhost:80/health"):
            print(f"✅ 服务就绪 (等待 {i+1} 秒)")
            break
        print(f"⏳ 等待服务启动... ({i+1}/30)")
        time.sleep(2)
    else:
        print("❌ 服务启动超时")
        # 显示日志
        run_cmd("docker-compose -f docker-compose.simple.yml logs --tail=20")
        return False
    
    # 6. 验证服务
    print("\n6️⃣ 验证服务...")
    
    # 检查容器状态
    success, output = run_cmd("docker-compose -f docker-compose.simple.yml ps")
    if success:
        print("📊 容器状态:")
        print(output)
    
    # 测试API
    try:
        # 健康检查
        response = requests.get("http://localhost:80/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print(f"⚠️ 健康检查失败: {response.status_code}")
        
        # 登录测试
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post("http://localhost:80/api/auth/login", json=login_data, timeout=10)
        if response.status_code == 200:
            print("✅ 登录测试通过")
            data = response.json()
            if data.get("code") == 200 and "token" in data.get("data", {}):
                print("✅ Token获取成功")
            else:
                print("⚠️ Token获取失败")
        else:
            print(f"⚠️ 登录测试失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
    
    print("\n🎉 部署完成!")
    print("=============")
    print("🌐 访问地址: http://localhost:80")
    print("👤 用户名: admin")
    print("🔑 密码: admin123")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 未知错误: {e}")
        sys.exit(1)
