#!/bin/bash

echo "🚀 GodEye 简化架构快速测试"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查Docker
echo -e "${BLUE}1. 检查Docker环境${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装${NC}"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker服务未运行${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker环境正常${NC}"

# 停止现有服务
echo -e "${BLUE}2. 清理现有服务${NC}"
docker-compose -f docker-compose.simple.yml down --remove-orphans &> /dev/null
echo -e "${GREEN}✅ 清理完成${NC}"

# 构建并启动
echo -e "${BLUE}3. 构建并启动服务${NC}"
echo "这可能需要几分钟时间..."

if docker-compose -f docker-compose.simple.yml up --build -d; then
    echo -e "${GREEN}✅ 服务启动成功${NC}"
else
    echo -e "${RED}❌ 服务启动失败${NC}"
    echo "查看错误日志:"
    docker-compose -f docker-compose.simple.yml logs
    exit 1
fi

# 等待服务启动
echo -e "${BLUE}4. 等待服务完全启动${NC}"
echo "等待30秒..."
sleep 30

# 检查容器状态
echo -e "${BLUE}5. 检查容器状态${NC}"
docker-compose -f docker-compose.simple.yml ps

# 测试健康检查
echo -e "${BLUE}6. 测试服务连通性${NC}"
for i in {1..10}; do
    if curl -s http://localhost:80/health > /dev/null; then
        echo -e "${GREEN}✅ 服务可访问${NC}"
        break
    else
        echo "尝试 $i/10..."
        sleep 3
    fi
    
    if [ $i -eq 10 ]; then
        echo -e "${RED}❌ 服务无法访问${NC}"
        echo "查看应用日志:"
        docker-compose -f docker-compose.simple.yml logs godeye-app
        exit 1
    fi
done

# 测试API
echo -e "${BLUE}7. 测试API接口${NC}"

# 测试健康检查
health_response=$(curl -s http://localhost:80/health)
echo "健康检查响应: $health_response"

# 测试登录
login_response=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' \
    http://localhost:80/api/auth/login)
echo "登录测试响应: $login_response"

# 显示结果
echo ""
echo -e "${GREEN}🎉 测试完成！${NC}"
echo "=========================="
echo "访问地址: http://localhost:80"
echo "默认登录: admin / admin123"
echo ""
echo "管理命令:"
echo "  查看日志: docker-compose -f docker-compose.simple.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.simple.yml down"
echo "  重启服务: docker-compose -f docker-compose.simple.yml restart"
echo ""
echo "容器状态:"
docker-compose -f docker-compose.simple.yml ps
