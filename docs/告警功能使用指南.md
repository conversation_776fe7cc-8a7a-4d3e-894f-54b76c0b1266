# GodEye 告警功能使用指南

## 概述

GodEye支持多种告警渠道，包括邮件、企业微信、钉钉、飞书和Slack等。本指南将详细介绍如何配置和使用这些告警功能。

## 支持的告警渠道

| 告警渠道 | 支持状态 | 消息格式 | 域名识别 |
|---------|---------|----------|----------|
| 邮件告警 | ✅ | HTML邮件 | SMTP配置 |
| 企业微信 | ✅ | Markdown | qyapi.weixin.qq.com |
| 钉钉 | ✅ | Markdown | oapi.dingtalk.com |
| 飞书 | ✅ | 交互式卡片 | open.feishu.cn, open.larksuite.com |
| Slack | ✅ | 富文本附件 | hooks.slack.com |
| 通用Webhook | ✅ | JSON | 其他域名 |

## 1. 邮件告警配置

### 1.1 SMTP配置
```json
{
  "smtp": {
    "host": "smtp.example.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "your-password",
    "from": "GodEye Security <<EMAIL>>",
    "tls": true
  }
}
```

### 1.2 邮件模板
- 支持HTML格式
- 自动包含任务详情、风险级别、匹配数量
- 包含dashboard链接

## 2. 企业微信告警配置

### 2.1 获取Webhook URL
1. 在企业微信群中添加机器人
2. 获取Webhook URL：`https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY`

### 2.2 消息格式
```json
{
  "msgtype": "markdown",
  "markdown": {
    "content": "### 🔍 GodEye 安全告警 🟠\n\n**任务名称**: 测试任务\n**风险级别**: 🟠 high\n**发现问题**: 5 个匹配项\n**扫描时间**: 2024-01-01 12:00:00\n\n> 在您的监控任务中发现了潜在的安全风险，请及时查看和处理。\n\n[查看详情](https://godeye.example.com/dashboard)"
  }
}
```

### 2.3 风险级别映射
- 🔴 critical (严重)
- 🟠 high (高)
- 🟡 medium (中)
- 🟢 low (低)

## 3. 钉钉告警配置

### 3.1 获取Webhook URL
1. 在钉钉群中添加自定义机器人
2. 获取Webhook URL：`https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN`

### 3.2 消息格式
```json
{
  "msgtype": "markdown",
  "markdown": {
    "title": "🔍 GodEye 安全告警",
    "text": "### 🔍 GodEye 安全告警 🟠\n\n**任务名称**: 测试任务\n**风险级别**: 🟠 high\n**发现问题**: 5 个匹配项\n**扫描时间**: 2024-01-01 12:00:00\n\n> 在您的监控任务中发现了潜在的安全风险，请及时查看和处理。\n\n[查看详情](https://godeye.example.com/dashboard)"
  },
  "at": {
    "atMobiles": [],
    "isAtAll": false
  }
}
```

## 4. 飞书告警配置

### 4.1 获取Webhook URL
1. 在飞书群中添加自定义机器人
2. 获取Webhook URL：`https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_TOKEN`

### 4.2 交互式卡片格式
飞书使用交互式卡片格式，提供更丰富的视觉体验：

```json
{
  "msg_type": "interactive",
  "content": {
    "card": {
      "config": {
        "wide_screen_mode": true
      },
      "header": {
        "title": {
          "content": "🔍 GodEye 安全告警 🟠",
          "tag": "plain_text"
        },
        "template": "orange"
      },
      "elements": [
        {
          "tag": "div",
          "text": {
            "content": "**任务名称**: 测试任务\n**风险级别**: 🟠 high\n**发现问题**: 5 个匹配项\n**扫描时间**: 2024-01-01 12:00:00",
            "tag": "lark_md"
          }
        },
        {
          "tag": "note",
          "elements": [
            {
              "content": "在您的监控任务中发现了潜在的安全风险，请及时查看和处理。",
              "tag": "plain_text"
            }
          ]
        },
        {
          "tag": "action",
          "actions": [
            {
              "tag": "button",
              "text": {
                "content": "查看详情",
                "tag": "plain_text"
              },
              "url": "https://godeye.example.com/dashboard",
              "type": "primary"
            }
          ]
        }
      ]
    }
  }
}
```

### 4.3 卡片颜色映射
- `red`: critical (严重)
- `orange`: high (高)
- `yellow`: medium (中)
- `green`: low (低)

## 5. Slack告警配置

### 5.1 获取Webhook URL
1. 在Slack中创建Incoming Webhook
2. 获取Webhook URL：`*****************************************************************************`

### 5.2 消息格式
```json
{
  "username": "GodEye",
  "icon_emoji": ":shield:",
  "attachments": [
    {
      "color": "danger",
      "title": "🔍 GodEye 安全告警",
      "title_link": "https://godeye.example.com/dashboard",
      "text": "在监控任务中发现了潜在的安全风险",
      "fields": [
        {
          "title": "任务名称",
          "value": "测试任务",
          "short": true
        },
        {
          "title": "风险级别",
          "value": "high",
          "short": true
        },
        {
          "title": "发现问题",
          "value": "5 个匹配项",
          "short": true
        }
      ],
      "footer": "GodEye Security",
      "ts": 1704067200
    }
  ]
}
```

## 6. 告警规则配置

### 6.1 创建告警规则
```bash
curl -X POST http://localhost:8083/api/v1/notification/rules \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "高风险告警",
    "description": "当发现高风险或严重风险时发送告警",
    "event_type": "scan_result",
    "conditions": {
      "risk_level": ["high", "critical"],
      "match_count": {"min": 1}
    },
    "enabled": true
  }'
```

### 6.2 配置告警渠道
```bash
curl -X POST http://localhost:8083/api/v1/notification/channels \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "企业微信群",
    "type": "webhook",
    "config": {
      "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY",
      "timeout": 30
    },
    "enabled": true
  }'
```

## 7. 测试告警功能

### 7.1 测试Webhook连接
```bash
curl -X POST http://localhost:8083/api/v1/notification/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "channel_id": "your-channel-id",
    "test_data": {
      "task_name": "测试任务",
      "risk_level": "high",
      "match_count": 5,
      "scan_time": "2024-01-01 12:00:00",
      "dashboard_url": "https://godeye.example.com/dashboard"
    }
  }'
```

## 8. 故障排除

### 8.1 常见问题
1. **Webhook URL无效**：检查URL格式和访问权限
2. **消息格式错误**：确认消息格式符合平台要求
3. **网络连接问题**：检查防火墙和网络配置
4. **认证失败**：验证Token或密钥是否正确

### 8.2 日志查看
```bash
# 查看notification服务日志
docker-compose logs -f notification

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T12:00:00" notification
```

## 9. 最佳实践

1. **告警分级**：根据风险级别设置不同的告警渠道
2. **避免告警疲劳**：合理设置告警阈值和频率
3. **测试验证**：定期测试告警功能确保正常工作
4. **监控告警**：监控告警发送成功率和响应时间
5. **文档维护**：及时更新Webhook URL和配置信息

通过以上配置，您可以充分利用GodEye的多渠道告警功能，确保及时发现和处理安全风险。
