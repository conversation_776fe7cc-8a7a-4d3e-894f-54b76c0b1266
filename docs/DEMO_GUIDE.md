# GodEye 系统演示指南

## 概述

GodEye 是一个全新的代码泄露监控平台，支持在 GitHub、GitLab、Gitee 等多个代码托管平台进行全量关键词搜索，以及传统的指定仓库监控功能。

## 系统架构

- **后端**: Go + PostgreSQL + Redis + RabbitMQ + Elasticsearch
- **前端**: React + Next.js + TypeScript + Tailwind CSS
- **部署**: Docker Compose (开发环境)

## 功能特性

### 1. 全局关键词搜索
- 支持在多个代码平台进行全量搜索
- 关键词配置和管理
- 多平台账号池管理
- 搜索结果去重和风险评估
- 实时搜索进度跟踪

### 2. 仓库监控
- 指定仓库的持续监控
- 代码变更检测
- 敏感信息识别

### 3. 平台账号管理
- 多账号配置和轮换
- API 配额监控
- 账号健康检查

## 快速开始

### 1. 启动系统

```bash
cd godeye
docker-compose -f docker-compose.dev.yml up -d
```

### 2. 访问前端

打开浏览器访问: http://localhost:3000

### 3. 访问后端API

后端API地址: http://localhost:8082

## API 演示

### 1. 健康检查

```bash
curl http://localhost:8082/health
```

### 2. 获取支持的平台

```bash
curl http://localhost:8082/api/v1/system/platforms
```

### 3. 快速搜索

```bash
curl -X POST http://localhost:8082/api/v1/global-search/search \
  -H "Content-Type: application/json" \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-************" \
  -d '{
    "keywords": {"list": ["password", "secret", "api_key"]},
    "platforms": {"list": ["github"]},
    "search_type": "code",
    "max_results": 50
  }'
```

### 4. 创建全局搜索任务

```bash
curl -X POST http://localhost:8082/api/v1/global-search/tasks \
  -H "Content-Type: application/json" \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-************" \
  -d '{
    "name": "敏感信息监控",
    "description": "监控代码中的敏感信息泄露",
    "keywords": {"list": ["password", "secret", "api_key", "token"]},
    "platforms": {"list": ["github", "gitlab", "gitee"]},
    "schedule_type": "cron",
    "schedule_rule": "0 */6 * * *",
    "enabled": true
  }'
```

### 5. 添加平台账号

```bash
curl -X POST http://localhost:8082/api/v1/accounts \
  -H "Content-Type: application/json" \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-************" \
  -d '{
    "platform": "github",
    "account_type": "token",
    "username": "your_username",
    "token": "your_github_token",
    "enabled": true
  }'
```

## 前端页面

### 1. 全局搜索管理
- **路径**: `/global-search`
- **功能**: 管理全局搜索任务，查看任务状态和结果

### 2. 快速搜索
- **路径**: `/global-search/quick-search`
- **功能**: 执行一次性的快速搜索

### 3. 创建搜索任务
- **路径**: `/global-search/create`
- **功能**: 创建定时或手动的搜索任务

### 4. 搜索结果
- **路径**: `/global-search/results?taskId=xxx`
- **功能**: 查看搜索结果详情

### 5. 平台账号管理
- **路径**: `/accounts`
- **功能**: 管理各平台的API账号

### 6. 添加账号
- **路径**: `/accounts/add`
- **功能**: 添加新的平台账号

### 7. 账号统计
- **路径**: `/accounts/stats`
- **功能**: 查看账号使用统计

## 核心技术实现

### 1. 搜索引擎架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   搜索调度器     │    │   平台适配器     │    │   结果处理器     │
│                │    │                │    │                │
│ - 任务调度      │───▶│ - GitHub API   │───▶│ - 去重算法      │
│ - 工作池管理    │    │ - GitLab API   │    │ - 风险评估      │
│ - 错误重试      │    │ - Gitee API    │    │ - 结果存储      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 账号池管理

- 多账号轮换使用
- API 配额监控
- 自动故障转移
- 账号健康检查

### 3. 搜索结果处理

- 内容哈希去重
- 相似度检测
- 风险等级评估
- 上下文提取

## 开发环境

### 目录结构

```
godeye/
├── services/           # 后端服务
│   ├── auth/          # 认证服务
│   ├── monitor/       # 监控服务
│   └── notification/  # 通知服务
├── frontend/          # 前端应用
├── docs/             # 文档
├── scripts/          # 脚本
└── docker-compose.dev.yml
```

### 服务端口

- 前端: 3000
- 监控服务: 8082
- 认证服务: 8081
- 通知服务: 8083
- PostgreSQL: 5433
- Redis: 6380
- RabbitMQ: 5673 (管理界面: 15673)
- Elasticsearch: 9201

## 注意事项

### 1. API 限制

各平台都有API调用限制，需要合理配置搜索频率和并发数：

- GitHub: 5000 requests/hour (authenticated)
- GitLab: 2000 requests/hour
- Gitee: 1000 requests/hour

### 2. 账号配置

建议为每个平台配置多个账号以提高搜索效率：

- 至少3个GitHub账号
- 至少2个GitLab账号
- 至少2个Gitee账号

### 3. 搜索策略

- 使用精确的关键词避免过多无关结果
- 合理设置搜索深度和结果数量限制
- 定期清理过期的搜索结果

## 故障排除

### 1. 服务启动失败

```bash
# 检查服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看服务日志
docker-compose -f docker-compose.dev.yml logs monitor-service-dev
```

### 2. 数据库连接问题

```bash
# 检查数据库状态
docker-compose -f docker-compose.dev.yml exec postgres-dev psql -U godeye -d godeye -c "\dt"
```

### 3. API 调用失败

- 检查账号配置是否正确
- 验证API token是否有效
- 确认API配额是否充足

## 后续开发计划

1. **认证系统集成**: 完整的用户认证和授权
2. **通知系统**: 邮件、Slack、钉钉等通知方式
3. **报告系统**: 定期生成监控报告
4. **机器学习**: 智能风险评估和误报过滤
5. **可视化**: 更丰富的数据可视化界面
