# GodEye API 接口设计

## 📋 API 设计原则

### 1. RESTful 设计规范
- 使用标准 HTTP 方法 (GET, POST, PUT, DELETE)
- 资源导向的 URL 设计
- 统一的响应格式
- 合理的 HTTP 状态码使用

### 2. 版本控制
- API 版本通过 URL 路径控制: `/api/v1/`
- 向后兼容性保证
- 废弃 API 的优雅过渡

### 3. 安全设计
- JWT Token 认证
- API 限流和防护
- 输入验证和输出过滤
- HTTPS 强制使用

## 🔐 认证服务 API (Auth Service)

### 基础路径: `/api/v1/auth`

#### 1.1 用户认证接口

##### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "john_doe",
  "password": "SecurePassword123!",
  "first_name": "<PERSON>",
  "last_name": "Doe"
}

Response 201:
{
  "success": true,
  "message": "用户注册成功",
  "data": {
    "user_id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "username": "john_doe",
    "is_verified": false
  }
}
```

##### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "remember_me": true
}

Response 200:
{
  "success": true,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "user": {
      "id": "123e4567-e89b-12d3-a456-************",
      "email": "<EMAIL>",
      "username": "john_doe",
      "role": "user",
      "permissions": ["read:tasks", "write:tasks"]
    }
  }
}
```

##### Token 刷新
```http
POST /api/v1/auth/refresh
Content-Type: application/json
Authorization: Bearer <refresh_token>

Response 200:
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600
  }
}
```

##### 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer <access_token>

Response 200:
{
  "success": true,
  "message": "登出成功"
}
```

#### 1.2 用户信息管理

##### 获取用户信息
```http
GET /api/v1/auth/profile
Authorization: Bearer <access_token>

Response 200:
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "username": "john_doe",
    "first_name": "John",
    "last_name": "Doe",
    "avatar_url": "https://example.com/avatar.jpg",
    "role": "user",
    "is_active": true,
    "is_verified": true,
    "last_login_at": "2024-01-01T12:00:00Z",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

##### 更新用户信息
```http
PUT /api/v1/auth/profile
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Smith",
  "avatar_url": "https://example.com/new-avatar.jpg"
}

Response 200:
{
  "success": true,
  "message": "用户信息更新成功",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "first_name": "John",
    "last_name": "Smith",
    "avatar_url": "https://example.com/new-avatar.jpg"
  }
}
```

##### 修改密码
```http
POST /api/v1/auth/change-password
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "current_password": "OldPassword123!",
  "new_password": "NewPassword456!",
  "confirm_password": "NewPassword456!"
}

Response 200:
{
  "success": true,
  "message": "密码修改成功"
}
```

#### 1.3 权限管理接口

##### 获取角色列表
```http
GET /api/v1/auth/roles
Authorization: Bearer <access_token>

Response 200:
{
  "success": true,
  "data": [
    {
      "id": "role-uuid-1",
      "name": "admin",
      "description": "系统管理员",
      "permissions": {"all": true},
      "is_system": true
    },
    {
      "id": "role-uuid-2", 
      "name": "user",
      "description": "普通用户",
      "permissions": {"tasks": ["read", "create", "update"]},
      "is_system": true
    }
  ]
}
```

##### 用户管理 (管理员权限)
```http
GET /api/v1/auth/users?page=1&limit=20&search=john
Authorization: Bearer <admin_token>

Response 200:
{
  "success": true,
  "data": {
    "users": [
      {
        "id": "123e4567-e89b-12d3-a456-************",
        "email": "<EMAIL>",
        "username": "john_doe",
        "role": "user",
        "is_active": true,
        "created_at": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

## 🔍 监控服务 API (Monitor Service)

### 基础路径: `/api/v1/monitor`

#### 2.1 监控任务管理

##### 创建监控任务
```http
POST /api/v1/monitor/tasks
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "项目安全扫描",
  "description": "扫描项目中的敏感信息",
  "platform": "github",
  "repository_url": "https://github.com/user/repo",
  "keywords": ["password", "secret", "api_key"],
  "regex_patterns": ["\\b[A-Za-z0-9]{32}\\b"],
  "file_extensions": [".js", ".py", ".env"],
  "exclude_paths": ["node_modules/", ".git/"],
  "scan_frequency": "daily",
  "priority": 5
}

Response 201:
{
  "success": true,
  "message": "监控任务创建成功",
  "data": {
    "id": "task-uuid-1",
    "name": "项目安全扫描",
    "status": "active",
    "next_scan_at": "2024-01-02T10:00:00Z",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

##### 获取任务列表
```http
GET /api/v1/monitor/tasks?page=1&limit=20&status=active&platform=github
Authorization: Bearer <access_token>

Response 200:
{
  "success": true,
  "data": {
    "tasks": [
      {
        "id": "task-uuid-1",
        "name": "项目安全扫描",
        "platform": "github",
        "repository_url": "https://github.com/user/repo",
        "scan_frequency": "daily",
        "is_active": true,
        "priority": 5,
        "last_scan_at": "2024-01-01T12:00:00Z",
        "next_scan_at": "2024-01-02T12:00:00Z",
        "scan_count": 10,
        "created_at": "2024-01-01T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

##### 启动扫描任务
```http
POST /api/v1/monitor/tasks/{task_id}/start
Authorization: Bearer <access_token>

Response 200:
{
  "success": true,
  "message": "扫描任务已启动",
  "data": {
    "session_id": "session-uuid-1",
    "status": "running",
    "started_at": "2024-01-01T15:00:00Z"
  }
}
```

#### 2.2 扫描结果管理

##### 获取扫描结果
```http
GET /api/v1/monitor/results?task_id=task-uuid-1&status=pending&risk_level=high&page=1&limit=20
Authorization: Bearer <access_token>

Response 200:
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "result-uuid-1",
        "task_id": "task-uuid-1",
        "repository_url": "https://github.com/user/repo",
        "file_path": "config/database.js",
        "line_number": 15,
        "matched_content": "password: 'secret123'",
        "matched_keyword": "password",
        "confidence_score": 0.95,
        "risk_level": "high",
        "severity": "critical",
        "status": "pending",
        "commit_hash": "abc123def456",
        "commit_author": "<EMAIL>",
        "created_at": "2024-01-01T12:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

##### 更新结果状态
```http
PUT /api/v1/monitor/results/{result_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "status": "confirmed",
  "review_comment": "确认为真实的安全风险"
}

Response 200:
{
  "success": true,
  "message": "结果状态更新成功",
  "data": {
    "id": "result-uuid-1",
    "status": "confirmed",
    "reviewed_by": "user-uuid-1",
    "reviewed_at": "2024-01-01T16:00:00Z"
  }
}
```

#### 2.3 统计分析

##### 获取统计数据
```http
GET /api/v1/monitor/stats?period=7d
Authorization: Bearer <access_token>

Response 200:
{
  "success": true,
  "data": {
    "total_tasks": 15,
    "active_tasks": 12,
    "total_results": 156,
    "high_risk_results": 23,
    "pending_results": 45,
    "scan_frequency": {
      "daily": 8,
      "weekly": 4,
      "monthly": 3
    },
    "risk_distribution": {
      "critical": 5,
      "high": 18,
      "medium": 78,
      "low": 55
    }
  }
}
```

## 📢 通知服务 API (Notification Service)

### 基础路径: `/api/v1/notification`

#### 3.1 通知配置管理

##### 创建通知配置
```http
POST /api/v1/notification/configs
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "邮件通知配置",
  "type": "email",
  "config": {
    "smtp_host": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "app_password",
    "from_email": "<EMAIL>",
    "to_emails": ["<EMAIL>"]
  },
  "is_active": true,
  "priority": 5
}

Response 201:
{
  "success": true,
  "message": "通知配置创建成功",
  "data": {
    "id": "config-uuid-1",
    "name": "邮件通知配置",
    "type": "email",
    "is_active": true,
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

##### 测试通知配置
```http
POST /api/v1/notification/test
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "config_id": "config-uuid-1",
  "test_message": "这是一条测试通知"
}

Response 200:
{
  "success": true,
  "message": "测试通知发送成功",
  "data": {
    "sent_at": "2024-01-01T15:00:00Z",
    "status": "sent"
  }
}
```

#### 3.2 通知规则管理

##### 创建通知规则
```http
POST /api/v1/notification/rules
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "高风险结果通知",
  "description": "当发现高风险扫描结果时发送通知",
  "conditions": {
    "risk_level": ["high", "critical"],
    "status": "pending"
  },
  "actions": {
    "notification_configs": ["config-uuid-1"],
    "template": "high_risk_alert"
  },
  "cooldown_minutes": 60,
  "max_triggers_per_hour": 5
}

Response 201:
{
  "success": true,
  "message": "通知规则创建成功",
  "data": {
    "id": "rule-uuid-1",
    "name": "高风险结果通知",
    "is_active": true,
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

#### 3.3 发送记录查询

##### 获取通知记录
```http
GET /api/v1/notification/logs?status=sent&type=email&page=1&limit=20
Authorization: Bearer <access_token>

Response 200:
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log-uuid-1",
        "type": "email",
        "recipient": "<EMAIL>",
        "subject": "发现高风险安全问题",
        "status": "sent",
        "attempts": 1,
        "sent_at": "2024-01-01T12:30:00Z",
        "created_at": "2024-01-01T12:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

## 📊 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 分页响应
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## 🔒 HTTP 状态码规范

- `200 OK` - 请求成功
- `201 Created` - 资源创建成功
- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 未认证
- `403 Forbidden` - 权限不足
- `404 Not Found` - 资源不存在
- `409 Conflict` - 资源冲突
- `422 Unprocessable Entity` - 数据验证失败
- `429 Too Many Requests` - 请求频率限制
- `500 Internal Server Error` - 服务器内部错误

这个 API 设计确保了系统的一致性、可扩展性和易用性，为前端开发和第三方集成提供了清晰的接口规范。
