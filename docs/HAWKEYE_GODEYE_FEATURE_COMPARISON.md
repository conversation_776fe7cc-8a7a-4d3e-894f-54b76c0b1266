# Hawkeye vs GodEye 功能对比与补充计划

## 📋 用户需求回顾

根据用户明确要求：

1. **所有hawkeye项目中的功能，godeye中都必须要有**，包括：
   - 企业微信告警
   - 多账号rotate使用，避免API限制
   - 其他hawkeye核心功能

2. **godeye系统中功能更多**，包括：
   - gitlab、gitee平台监控
   - 监控指定仓库
   - 邮箱告警
   - 飞书告警
   - 钉钉告警
   - 企业微信webhook告警
   - 兼容hawkeye项目功能的同时也要有自己的特色

## 🔍 当前状态分析

### ✅ 已实现的功能

#### 后端服务
- ✅ **认证服务** (JWT认证)
- ✅ **监控服务** (多平台搜索)
- ✅ **通知服务** (支持多种通知渠道)
- ✅ **数据库设计** (PostgreSQL + Redis + Elasticsearch)

#### 平台支持
- ✅ **GitHub** 监控和搜索
- ✅ **GitLab** 基础支持
- ✅ **Gitee** 基础支持

#### 通知渠道 (后端已实现)
- ✅ **邮件通知** (SMTP)
- ✅ **Webhook通知** (通用)
- ✅ **钉钉机器人** (DingTalk)
- ✅ **企业微信** (WeChatWork)
- ✅ **飞书通知** (Feishu)
- ✅ **Slack通知** (Slack)

#### 搜索功能
- ✅ **关键词搜索** (全平台)
- ✅ **仓库监控** (指定仓库)
- ✅ **用户监控** (指定用户)
- ✅ **组织监控** (指定组织)
- ✅ **主题搜索** (Topic搜索)

#### 账号管理
- ✅ **多账号支持** (账号池)
- ✅ **账号轮换** (Round-robin)
- ✅ **API限制管理** (Rate limiting)
- ✅ **账号验证** (实时验证)

### ❌ 发现的问题

#### 1. 账号添加功能问题
- **问题**: 前端账号添加仍然失败
- **原因**: API字段映射不完整
- **状态**: 🔧 需要修复

#### 2. 前端通知配置缺失
- **问题**: 前端缺少完整的通知配置界面
- **影响**: 用户无法配置钉钉、企业微信、飞书等通知
- **状态**: 🔧 需要开发

#### 3. API路由问题
- **问题**: 前端调用错误的API端口
- **影响**: 通知配置页面无法正常工作
- **状态**: 🔧 需要修复

## 🚧 需要补充的功能

### 1. 前端通知配置页面
**优先级**: 🔴 高

需要创建完整的通知配置界面，包括：
- 邮件SMTP配置
- 钉钉机器人配置
- 企业微信webhook配置
- 飞书机器人配置
- 通知规则配置
- 测试发送功能

### 2. 账号添加功能修复
**优先级**: 🔴 高

修复前端账号添加功能：
- 修复API字段映射
- 完善错误处理
- 添加表单验证
- 优化用户体验

### 3. Hawkeye功能移植
**优先级**: 🟡 中

确保所有Hawkeye功能都已移植：
- 多账号轮换机制
- 企业微信告警
- 邮件通知列表管理
- 黑名单/白名单管理
- 统计和趋势分析

### 4. 前端页面完善
**优先级**: 🟡 中

完善前端用户界面：
- 通知历史查看
- 账号使用统计
- 系统健康监控
- 配置导入导出

## 📝 具体实施计划

### 阶段1: 紧急修复 (立即执行)

#### 1.1 修复账号添加功能
```typescript
// 修复前端API调用
// 文件: godeye/frontend/src/pages/accounts/add.tsx
// 确保所有必需字段都正确发送
```

#### 1.2 修复通知页面API调用
```typescript
// 修复API端点
// 文件: godeye/frontend/src/pages/notifications/index.tsx
// 从 localhost:8083 改为 localhost:8080/api/notifications
```

### 阶段2: 通知配置界面开发 (1-2天)

#### 2.1 创建通知渠道配置页面
- 邮件SMTP配置表单
- 钉钉机器人配置表单
- 企业微信webhook配置表单
- 飞书机器人配置表单

#### 2.2 创建通知规则配置页面
- 触发条件设置
- 通知模板选择
- 限流配置
- 测试发送功能

### 阶段3: 功能完善 (2-3天)

#### 3.1 账号管理优化
- 账号使用统计
- 健康状态监控
- 批量导入功能

#### 3.2 监控功能增强
- 实时监控状态
- 搜索结果分析
- 风险评估优化

## 🎯 成功标准

### 功能完整性
- ✅ 所有Hawkeye功能都已移植
- ✅ 新增的GodEye特色功能正常工作
- ✅ 多平台支持 (GitHub/GitLab/Gitee)
- ✅ 完整的通知系统

### 用户体验
- ✅ 账号添加功能正常
- ✅ 通知配置界面完整
- ✅ 错误处理友好
- ✅ 操作流程顺畅

### 技术指标
- ✅ API响应时间 < 2秒
- ✅ 前端页面加载 < 3秒
- ✅ 系统稳定性 > 99%
- ✅ 数据一致性保证

## 🔧 立即行动项

### 1. 修复账号添加 (30分钟)
- 检查前端表单数据
- 修复API字段映射
- 测试端到端流程

### 2. 修复通知页面 (15分钟)
- 修复API端点URL
- 测试页面加载
- 验证数据显示

### 3. 创建通知配置界面 (2小时)
- 设计配置表单
- 实现API集成
- 添加测试功能

---

**下一步**: 立即开始修复账号添加功能和通知页面API调用问题
