# GodEye 微服务架构设计

## 📋 架构概述

GodEye 系统采用微服务架构，将系统功能拆分为三个核心服务，每个服务独立部署、独立扩展，通过 API 网关统一对外提供服务。

### 🏗️ 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层                                │
├─────────────────────────────────────────────────────────────┤
│  Next.js 14 + React 18 + TypeScript + Ant Design + Zustand  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      API 网关层                              │
├─────────────────────────────────────────────────────────────┤
│              Nginx (负载均衡 + 反向代理)                      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      微服务层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   认证服务       │    监控服务      │      通知服务            │
│  (Auth Service) │ (Monitor Service)│ (Notification Service) │
│   Port: 8081    │   Port: 8082     │     Port: 8083         │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据层                                  │
├─────────────────┬─────────────────┬─────────────────────────┤
│   PostgreSQL    │     Redis       │    Elasticsearch        │
│   (主数据库)     │    (缓存)       │     (搜索引擎)           │
│   Port: 5433    │   Port: 6380    │     Port: 9201          │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      消息队列                                │
├─────────────────────────────────────────────────────────────┤
│                    RabbitMQ                                 │
│                   Port: 5673                               │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心微服务设计

### 1. 认证服务 (Auth Service)

#### 1.1 服务职责
- **用户管理**: 用户注册、登录、个人信息管理
- **身份认证**: JWT Token 生成、验证、刷新
- **权限控制**: 基于角色的访问控制 (RBAC)
- **会话管理**: 多设备登录、强制下线
- **安全审计**: 登录日志、操作审计

#### 1.2 技术栈
- **框架**: Go 1.22 + Gin
- **数据库**: PostgreSQL (用户数据) + Redis (会话缓存)
- **认证**: JWT + BCrypt 密码加密
- **中间件**: CORS、限流、日志记录

#### 1.3 API 接口设计
```yaml
# 用户认证
POST   /api/auth/register      # 用户注册
POST   /api/auth/login         # 用户登录
POST   /api/auth/logout        # 用户登出
POST   /api/auth/refresh       # Token 刷新
GET    /api/auth/profile       # 获取用户信息
PUT    /api/auth/profile       # 更新用户信息
POST   /api/auth/change-password # 修改密码

# 权限管理
GET    /api/auth/roles         # 获取角色列表
POST   /api/auth/roles         # 创建角色
PUT    /api/auth/roles/:id     # 更新角色
DELETE /api/auth/roles/:id     # 删除角色
POST   /api/auth/users/:id/roles # 分配角色

# 系统管理
GET    /api/auth/users         # 用户列表
PUT    /api/auth/users/:id     # 更新用户
DELETE /api/auth/users/:id     # 删除用户
GET    /api/auth/audit-logs    # 审计日志
```

#### 1.4 数据模型
```go
// 用户模型
type User struct {
    ID           uuid.UUID `gorm:"type:uuid;primary_key"`
    Email        string    `gorm:"uniqueIndex;not null"`
    Username     string    `gorm:"uniqueIndex;not null"`
    PasswordHash string    `gorm:"not null"`
    FirstName    string
    LastName     string
    AvatarURL    string
    IsActive     bool      `gorm:"default:true"`
    IsVerified   bool      `gorm:"default:false"`
    Role         string    `gorm:"default:user"`
    LastLoginAt  *time.Time
    CreatedAt    time.Time
    UpdatedAt    time.Time
}

// 角色模型
type Role struct {
    ID          uuid.UUID              `gorm:"type:uuid;primary_key"`
    Name        string                 `gorm:"uniqueIndex;not null"`
    Description string
    Permissions map[string]interface{} `gorm:"type:jsonb"`
    CreatedAt   time.Time
    UpdatedAt   time.Time
}
```

### 2. 监控服务 (Monitor Service)

#### 2.1 服务职责
- **仓库扫描**: GitHub/GitLab/Bitbucket 代码仓库扫描
- **内容分析**: 关键词匹配、正则表达式、模式识别
- **任务调度**: 定时扫描、增量扫描、并发控制
- **结果处理**: 数据去重、风险评估、结果存储
- **第三方集成**: GitHub API、其他代码托管平台

#### 2.2 技术栈
- **框架**: Go 1.22 + Gin
- **数据库**: PostgreSQL (任务数据) + Elasticsearch (搜索)
- **消息队列**: RabbitMQ (任务队列)
- **缓存**: Redis (API 限流、结果缓存)
- **外部 API**: GitHub API v4 (GraphQL)

#### 2.3 API 接口设计
```yaml
# 监控任务管理
GET    /api/monitor/tasks      # 获取任务列表
POST   /api/monitor/tasks      # 创建监控任务
GET    /api/monitor/tasks/:id  # 获取任务详情
PUT    /api/monitor/tasks/:id  # 更新任务
DELETE /api/monitor/tasks/:id  # 删除任务
POST   /api/monitor/tasks/:id/start # 启动任务
POST   /api/monitor/tasks/:id/stop  # 停止任务

# 扫描结果
GET    /api/monitor/results    # 获取扫描结果
GET    /api/monitor/results/:id # 结果详情
PUT    /api/monitor/results/:id # 更新结果状态
POST   /api/monitor/results/:id/ignore # 忽略结果
GET    /api/monitor/search     # 搜索结果

# 统计分析
GET    /api/monitor/stats      # 统计数据
GET    /api/monitor/trends     # 趋势分析
GET    /api/monitor/reports    # 生成报告

# 配置管理
GET    /api/monitor/keywords   # 关键词管理
POST   /api/monitor/keywords   # 添加关键词
GET    /api/monitor/patterns   # 模式管理
POST   /api/monitor/patterns   # 添加模式
```

#### 2.4 数据模型
```go
// 监控任务模型
type MonitorTask struct {
    ID            uuid.UUID `gorm:"type:uuid;primary_key"`
    Name          string    `gorm:"not null"`
    Description   string
    UserID        uuid.UUID `gorm:"not null"`
    Platform      string    `gorm:"default:github"`
    RepositoryURL string
    Keywords      []string  `gorm:"type:text[]"`
    RegexPatterns []string  `gorm:"type:text[]"`
    ScanFrequency string    `gorm:"default:daily"`
    IsActive      bool      `gorm:"default:true"`
    LastScanAt    *time.Time
    NextScanAt    *time.Time
    CreatedAt     time.Time
    UpdatedAt     time.Time
}

// 扫描结果模型
type ScanResult struct {
    ID              uuid.UUID `gorm:"type:uuid;primary_key"`
    TaskID          uuid.UUID `gorm:"not null"`
    RepositoryURL   string    `gorm:"not null"`
    FilePath        string    `gorm:"not null"`
    LineNumber      int
    MatchedContent  string
    MatchedKeyword  string
    ConfidenceScore float64   `gorm:"type:decimal(3,2)"`
    RiskLevel       string    `gorm:"default:medium"`
    Status          string    `gorm:"default:pending"`
    IsFalsePositive bool      `gorm:"default:false"`
    CreatedAt       time.Time
    UpdatedAt       time.Time
}
```

### 3. 通知服务 (Notification Service)

#### 3.1 服务职责
- **多渠道通知**: 邮件、Webhook、钉钉、企业微信、Slack
- **通知规则**: 条件触发、频率控制、升级策略
- **模板管理**: 通知模板、变量替换、多语言支持
- **发送管理**: 重试机制、失败处理、发送记录
- **集成管理**: 第三方服务集成、认证管理

#### 3.2 技术栈
- **框架**: Go 1.22 + Gin
- **数据库**: PostgreSQL (配置数据)
- **消息队列**: RabbitMQ (通知队列)
- **邮件服务**: SMTP + HTML 模板
- **HTTP 客户端**: 支持 Webhook 和第三方 API

#### 3.3 API 接口设计
```yaml
# 通知配置
GET    /api/notification/configs     # 获取通知配置
POST   /api/notification/configs     # 创建通知配置
PUT    /api/notification/configs/:id # 更新配置
DELETE /api/notification/configs/:id # 删除配置
POST   /api/notification/test        # 测试通知

# 通知模板
GET    /api/notification/templates   # 获取模板列表
POST   /api/notification/templates   # 创建模板
PUT    /api/notification/templates/:id # 更新模板
DELETE /api/notification/templates/:id # 删除模板

# 发送记录
GET    /api/notification/logs        # 获取发送记录
GET    /api/notification/logs/:id    # 记录详情
POST   /api/notification/retry/:id   # 重试发送

# 通知规则
GET    /api/notification/rules       # 获取规则列表
POST   /api/notification/rules       # 创建规则
PUT    /api/notification/rules/:id   # 更新规则
DELETE /api/notification/rules/:id   # 删除规则
```

#### 3.4 数据模型
```go
// 通知配置模型
type NotificationConfig struct {
    ID        uuid.UUID              `gorm:"type:uuid;primary_key"`
    UserID    uuid.UUID              `gorm:"not null"`
    Type      string                 `gorm:"not null"` // email, webhook, dingtalk
    Config    map[string]interface{} `gorm:"type:jsonb;not null"`
    IsActive  bool                   `gorm:"default:true"`
    CreatedAt time.Time
    UpdatedAt time.Time
}

// 通知记录模型
type NotificationLog struct {
    ID           uuid.UUID `gorm:"type:uuid;primary_key"`
    ConfigID     uuid.UUID `gorm:"not null"`
    ResultID     uuid.UUID `gorm:"not null"`
    Type         string    `gorm:"not null"`
    Status       string    `gorm:"default:pending"`
    Message      string
    ErrorMessage string
    SentAt       *time.Time
    CreatedAt    time.Time
}
```

## 🔄 服务间通信

### 1. 同步通信 (HTTP/REST)
- **认证验证**: 其他服务调用认证服务验证 Token
- **用户信息**: 获取用户基本信息和权限
- **配置查询**: 查询系统配置和用户设置

### 2. 异步通信 (消息队列)
- **扫描任务**: 监控服务发布扫描任务到队列
- **通知触发**: 监控服务发送通知请求到通知服务
- **事件广播**: 系统事件的异步处理

### 3. 数据共享
- **用户数据**: 通过认证服务 API 获取
- **配置数据**: 通过 Redis 缓存共享
- **日志数据**: 统一日志收集和分析

## 🛡️ 安全设计

### 1. 服务间认证
- **内部 Token**: 服务间通信使用内部 JWT Token
- **API 密钥**: 关键接口使用 API Key 认证
- **网络隔离**: 服务间通过内部网络通信

### 2. 数据安全
- **敏感数据加密**: 密码、Token 等敏感信息加密存储
- **传输加密**: HTTPS/TLS 加密传输
- **访问控制**: 基于角色的细粒度权限控制

### 3. 监控和审计
- **访问日志**: 记录所有 API 访问
- **操作审计**: 记录关键操作和数据变更
- **异常监控**: 实时监控异常和安全事件

## 📊 性能设计

### 1. 缓存策略
- **Redis 缓存**: 用户会话、配置数据、API 结果
- **应用缓存**: 内存缓存热点数据
- **CDN 缓存**: 静态资源和前端资源

### 2. 数据库优化
- **索引设计**: 针对查询模式优化索引
- **分区策略**: 大表按时间或用户分区
- **连接池**: 数据库连接池优化

### 3. 并发处理
- **协程池**: Go 协程池处理并发请求
- **队列处理**: 异步任务队列处理
- **限流控制**: API 限流和熔断机制

## 🚀 部署架构

### 1. 容器化部署
- **Docker**: 每个服务独立容器化
- **Docker Compose**: 开发环境一键部署
- **健康检查**: 容器健康状态监控

### 2. 服务发现
- **静态配置**: 开发环境使用静态配置
- **DNS 解析**: 容器间通过服务名通信
- **负载均衡**: Nginx 负载均衡和故障转移

### 3. 监控运维
- **日志收集**: 统一日志收集和分析
- **指标监控**: Prometheus + Grafana 监控
- **告警通知**: 系统异常自动告警

这个微服务架构设计确保了系统的可扩展性、可维护性和高可用性，为 GodEye 系统的长期发展奠定了坚实的技术基础。
