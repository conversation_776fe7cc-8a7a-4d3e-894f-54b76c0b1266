# GodEye 数据库设计

## 📋 设计概述

GodEye 系统采用 PostgreSQL 作为主数据库，Redis 作为缓存层，Elasticsearch 作为搜索引擎。数据库设计遵循微服务架构原则，每个服务拥有独立的数据域，通过 API 进行数据交互。

## 🗄️ PostgreSQL 数据库设计

### 1. 用户认证域 (Auth Domain)

#### 1.1 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON><PERSON><PERSON>(100),
    avatar_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    role VARCHAR(50) DEFAULT 'user',
    last_login_at TIMESTAMP WITH TIME ZONE,
    login_attempts INTEGER DEFAULT 0,
    locked_until TIMES<PERSON><PERSON> WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### 1.2 角色表 (roles)
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '{}',
    is_system BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_is_system ON roles(is_system);
CREATE INDEX idx_roles_permissions ON roles USING GIN(permissions);
```

#### 1.3 用户角色关联表 (user_roles)
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID REFERENCES users(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, role_id)
);

-- 索引
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX idx_user_roles_expires_at ON user_roles(expires_at);
```

#### 1.4 用户会话表 (user_sessions)
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    device_info JSONB,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token_hash ON user_sessions(token_hash);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_is_active ON user_sessions(is_active);
```

### 2. 监控域 (Monitor Domain)

#### 2.1 监控任务表 (monitor_tasks)
```sql
CREATE TABLE monitor_tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    user_id UUID NOT NULL, -- 外键引用，但不使用 REFERENCES
    platform VARCHAR(50) DEFAULT 'github',
    repository_url VARCHAR(500),
    repository_owner VARCHAR(255),
    repository_name VARCHAR(255),
    keywords TEXT[],
    regex_patterns TEXT[],
    file_extensions TEXT[],
    exclude_paths TEXT[],
    scan_frequency VARCHAR(50) DEFAULT 'daily',
    scan_depth INTEGER DEFAULT 100, -- 扫描深度限制
    is_active BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT false,
    priority INTEGER DEFAULT 5, -- 1-10 优先级
    last_scan_at TIMESTAMP WITH TIME ZONE,
    next_scan_at TIMESTAMP WITH TIME ZONE,
    scan_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    last_error TEXT,
    config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_monitor_tasks_user_id ON monitor_tasks(user_id);
CREATE INDEX idx_monitor_tasks_platform ON monitor_tasks(platform);
CREATE INDEX idx_monitor_tasks_is_active ON monitor_tasks(is_active);
CREATE INDEX idx_monitor_tasks_next_scan_at ON monitor_tasks(next_scan_at);
CREATE INDEX idx_monitor_tasks_priority ON monitor_tasks(priority);
CREATE INDEX idx_monitor_tasks_repository ON monitor_tasks(repository_owner, repository_name);
CREATE INDEX idx_monitor_tasks_keywords ON monitor_tasks USING GIN(keywords);
```

#### 2.2 扫描结果表 (scan_results)
```sql
CREATE TABLE scan_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES monitor_tasks(id) ON DELETE CASCADE,
    scan_session_id UUID NOT NULL, -- 扫描会话ID
    repository_url VARCHAR(500) NOT NULL,
    repository_owner VARCHAR(255),
    repository_name VARCHAR(255),
    file_path VARCHAR(1000) NOT NULL,
    file_name VARCHAR(255),
    line_number INTEGER,
    column_number INTEGER,
    matched_content TEXT,
    matched_keyword VARCHAR(255),
    matched_pattern VARCHAR(500),
    context_before TEXT,
    context_after TEXT,
    confidence_score DECIMAL(3,2) DEFAULT 0.5,
    risk_level VARCHAR(20) DEFAULT 'medium',
    severity VARCHAR(20) DEFAULT 'info', -- critical, high, medium, low, info
    status VARCHAR(50) DEFAULT 'pending', -- pending, confirmed, false_positive, ignored, resolved
    is_false_positive BOOLEAN DEFAULT false,
    reviewed_by UUID, -- 审核人员
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_comment TEXT,
    commit_hash VARCHAR(40),
    commit_author VARCHAR(255),
    commit_date TIMESTAMP WITH TIME ZONE,
    file_size INTEGER,
    file_type VARCHAR(50),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_scan_results_task_id ON scan_results(task_id);
CREATE INDEX idx_scan_results_session_id ON scan_results(scan_session_id);
CREATE INDEX idx_scan_results_status ON scan_results(status);
CREATE INDEX idx_scan_results_risk_level ON scan_results(risk_level);
CREATE INDEX idx_scan_results_severity ON scan_results(severity);
CREATE INDEX idx_scan_results_repository ON scan_results(repository_owner, repository_name);
CREATE INDEX idx_scan_results_file_path ON scan_results(file_path);
CREATE INDEX idx_scan_results_created_at ON scan_results(created_at);
CREATE INDEX idx_scan_results_confidence_score ON scan_results(confidence_score);
```

#### 2.3 扫描会话表 (scan_sessions)
```sql
CREATE TABLE scan_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES monitor_tasks(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'running', -- running, completed, failed, cancelled
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    files_scanned INTEGER DEFAULT 0,
    results_found INTEGER DEFAULT 0,
    errors_count INTEGER DEFAULT 0,
    scan_config JSONB DEFAULT '{}',
    error_message TEXT,
    statistics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_scan_sessions_task_id ON scan_sessions(task_id);
CREATE INDEX idx_scan_sessions_status ON scan_sessions(status);
CREATE INDEX idx_scan_sessions_started_at ON scan_sessions(started_at);
```

#### 2.4 关键词库表 (keywords)
```sql
CREATE TABLE keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    risk_level VARCHAR(20) DEFAULT 'medium',
    is_regex BOOLEAN DEFAULT false,
    is_case_sensitive BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    created_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_keywords_keyword ON keywords(keyword);
CREATE INDEX idx_keywords_category ON keywords(category);
CREATE INDEX idx_keywords_risk_level ON keywords(risk_level);
CREATE INDEX idx_keywords_is_active ON keywords(is_active);
```

### 3. 通知域 (Notification Domain)

#### 3.1 通知配置表 (notification_configs)
```sql
CREATE TABLE notification_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL, -- 外键引用，但不使用 REFERENCES
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- email, webhook, dingtalk, wechat, slack
    config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    priority INTEGER DEFAULT 5,
    rate_limit INTEGER DEFAULT 100, -- 每小时限制
    retry_count INTEGER DEFAULT 3,
    timeout_seconds INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_notification_configs_user_id ON notification_configs(user_id);
CREATE INDEX idx_notification_configs_type ON notification_configs(type);
CREATE INDEX idx_notification_configs_is_active ON notification_configs(is_active);
```

#### 3.2 通知规则表 (notification_rules)
```sql
CREATE TABLE notification_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    conditions JSONB NOT NULL, -- 触发条件
    actions JSONB NOT NULL, -- 执行动作
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 5,
    cooldown_minutes INTEGER DEFAULT 60, -- 冷却时间
    max_triggers_per_hour INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_notification_rules_user_id ON notification_rules(user_id);
CREATE INDEX idx_notification_rules_is_active ON notification_rules(is_active);
CREATE INDEX idx_notification_rules_conditions ON notification_rules USING GIN(conditions);
```

#### 3.3 通知记录表 (notification_logs)
```sql
CREATE TABLE notification_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_id UUID REFERENCES notification_configs(id) ON DELETE CASCADE,
    rule_id UUID REFERENCES notification_rules(id) ON DELETE SET NULL,
    result_id UUID, -- 关联的扫描结果
    type VARCHAR(50) NOT NULL,
    recipient VARCHAR(255),
    subject VARCHAR(500),
    content TEXT,
    status VARCHAR(50) DEFAULT 'pending', -- pending, sent, failed, cancelled
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    error_message TEXT,
    response_data JSONB,
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_notification_logs_config_id ON notification_logs(config_id);
CREATE INDEX idx_notification_logs_rule_id ON notification_logs(rule_id);
CREATE INDEX idx_notification_logs_result_id ON notification_logs(result_id);
CREATE INDEX idx_notification_logs_status ON notification_logs(status);
CREATE INDEX idx_notification_logs_created_at ON notification_logs(created_at);
```

### 4. 系统域 (System Domain)

#### 4.1 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value JSONB,
    description TEXT,
    category VARCHAR(100),
    is_public BOOLEAN DEFAULT false,
    is_encrypted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_system_configs_key ON system_configs(key);
CREATE INDEX idx_system_configs_category ON system_configs(category);
```

#### 4.2 操作日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(100),
    session_id VARCHAR(100),
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    duration_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_resource_type ON audit_logs(resource_type);
CREATE INDEX idx_audit_logs_resource_id ON audit_logs(resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_audit_logs_ip_address ON audit_logs(ip_address);
```

## 🔄 Redis 缓存设计

### 1. 缓存键命名规范
```
godeye:{service}:{type}:{id}:{field?}

示例:
- godeye:auth:user:123e4567-e89b-12d3-a456-************
- godeye:auth:session:abc123def456
- godeye:monitor:task:123e4567-e89b-12d3-a456-************:status
- godeye:notification:config:123e4567-e89b-12d3-a456-************
```

### 2. 认证服务缓存策略

#### 2.1 用户会话缓存
```redis
# JWT Token 黑名单 (过期时间 = Token 剩余时间)
SET godeye:auth:blacklist:{token_jti} "1" EX {remaining_seconds}

# 用户会话信息 (过期时间 = 24小时)
HSET godeye:auth:session:{session_id} 
     user_id "123e4567-e89b-12d3-a456-************"
     username "john_doe"
     role "user"
     permissions '["read:tasks", "write:tasks"]'
     last_activity "2024-01-01T12:00:00Z"
EXPIRE godeye:auth:session:{session_id} 86400

# 用户基本信息缓存 (过期时间 = 1小时)
HSET godeye:auth:user:{user_id}
     email "<EMAIL>"
     username "john_doe"
     role "user"
     is_active "true"
     avatar_url "https://example.com/avatar.jpg"
EXPIRE godeye:auth:user:{user_id} 3600
```

#### 2.2 权限缓存
```redis
# 用户权限缓存 (过期时间 = 30分钟)
SET godeye:auth:permissions:{user_id} '["read:tasks", "write:tasks", "read:results"]' EX 1800

# 角色权限缓存 (过期时间 = 1小时)
SET godeye:auth:role:{role_name} '{"permissions": ["read:tasks"], "description": "普通用户"}' EX 3600
```

#### 2.3 登录限制缓存
```redis
# 登录失败次数 (过期时间 = 15分钟)
INCR godeye:auth:login_attempts:{email}
EXPIRE godeye:auth:login_attempts:{email} 900

# 账号锁定状态 (过期时间 = 锁定时长)
SET godeye:auth:locked:{user_id} "1" EX {lock_duration_seconds}
```

### 3. 监控服务缓存策略

#### 3.1 任务状态缓存
```redis
# 任务运行状态 (过期时间 = 1小时)
HSET godeye:monitor:task:{task_id}:status
     status "running"
     started_at "2024-01-01T12:00:00Z"
     progress "45"
     files_scanned "120"
     results_found "5"
EXPIRE godeye:monitor:task:{task_id}:status 3600

# 任务配置缓存 (过期时间 = 30分钟)
SET godeye:monitor:task:{task_id}:config '{"keywords": ["password", "secret"], "scan_frequency": "daily"}' EX 1800
```

#### 3.2 扫描结果缓存
```redis
# 最新扫描结果 (过期时间 = 6小时)
ZADD godeye:monitor:results:{task_id} {timestamp} {result_id}
EXPIRE godeye:monitor:results:{task_id} 21600

# 统计数据缓存 (过期时间 = 1小时)
HSET godeye:monitor:stats:{user_id}
     total_tasks "10"
     active_tasks "8"
     total_results "156"
     high_risk_results "12"
EXPIRE godeye:monitor:stats:{user_id} 3600
```

#### 3.3 API 限流缓存
```redis
# GitHub API 限流 (过期时间 = 1小时)
INCR godeye:monitor:api_limit:github:{user_id}
EXPIRE godeye:monitor:api_limit:github:{user_id} 3600

# 扫描频率限制 (过期时间 = 24小时)
SET godeye:monitor:scan_limit:{task_id} "1" EX 86400
```

### 4. 通知服务缓存策略

#### 4.1 通知配置缓存
```redis
# 用户通知配置 (过期时间 = 1小时)
SET godeye:notification:config:{user_id} '[{"type": "email", "config": {...}}, {"type": "webhook", "config": {...}}]' EX 3600

# 通知模板缓存 (过期时间 = 6小时)
SET godeye:notification:template:{template_id} '{"subject": "发现安全风险", "content": "..."}' EX 21600
```

#### 4.2 发送频率限制
```redis
# 通知发送频率限制 (过期时间 = 1小时)
INCR godeye:notification:rate_limit:{config_id}
EXPIRE godeye:notification:rate_limit:{config_id} 3600

# 通知冷却时间 (过期时间 = 冷却时长)
SET godeye:notification:cooldown:{rule_id} "1" EX {cooldown_seconds}
```

### 5. 系统级缓存

#### 5.1 配置缓存
```redis
# 系统配置缓存 (过期时间 = 1小时)
HSET godeye:system:config
     github_api_rate_limit "5000"
     scan_batch_size "100"
     notification_retry_times "3"
     data_retention_days "90"
EXPIRE godeye:system:config 3600
```

#### 5.2 统计缓存
```redis
# 系统统计数据 (过期时间 = 30分钟)
HSET godeye:system:stats
     total_users "1250"
     active_tasks "456"
     daily_scans "1200"
     total_results "45678"
EXPIRE godeye:system:stats 1800
```

## 📊 Elasticsearch 搜索设计

### 1. 索引设计

#### 1.1 扫描结果索引 (scan_results)
```json
{
  "mappings": {
    "properties": {
      "id": {"type": "keyword"},
      "task_id": {"type": "keyword"},
      "repository_url": {"type": "keyword"},
      "repository_owner": {"type": "keyword"},
      "repository_name": {"type": "keyword"},
      "file_path": {"type": "text", "analyzer": "path_analyzer"},
      "file_name": {"type": "keyword"},
      "matched_content": {"type": "text", "analyzer": "standard"},
      "matched_keyword": {"type": "keyword"},
      "risk_level": {"type": "keyword"},
      "severity": {"type": "keyword"},
      "status": {"type": "keyword"},
      "confidence_score": {"type": "float"},
      "created_at": {"type": "date"},
      "commit_hash": {"type": "keyword"},
      "commit_author": {"type": "keyword"},
      "metadata": {"type": "object", "dynamic": true}
    }
  }
}
```

### 2. 搜索功能

#### 2.1 全文搜索
- 扫描结果内容搜索
- 文件路径搜索
- 仓库名称搜索
- 关键词匹配搜索

#### 2.2 聚合分析
- 风险等级分布
- 文件类型统计
- 时间趋势分析
- 仓库风险排名

这个数据库设计确保了数据的完整性、性能和可扩展性，为 GodEye 系统提供了坚实的数据基础。
