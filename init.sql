-- GodEye 数据库初始化脚本
-- 创建默认管理员用户

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO users (username, password_hash, email, is_active) 
VALUES ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true)
ON CONFLICT (username) DO NOTHING;

-- 插入默认系统设置
INSERT INTO settings (key, value, description) VALUES 
('system_name', '"GodEye"', '系统名称'),
('version', '"1.0.0"', '系统版本'),
('max_search_results', '1000', '最大搜索结果数'),
('search_interval', '3600', '搜索间隔(秒)'),
('rate_limit_threshold', '100', 'API限制阈值')
ON CONFLICT (key) DO NOTHING;

-- 创建全文搜索索引
CREATE INDEX IF NOT EXISTS idx_results_content_search ON results USING gin(to_tsvector('english', content_snippet));
CREATE INDEX IF NOT EXISTS idx_results_repo_search ON results USING gin(to_tsvector('english', repository_name));

-- 创建复合索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_results_query_created ON results(query_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_results_processed_created ON results(is_processed, created_at DESC);

-- 添加约束确保数据完整性
ALTER TABLE results ADD CONSTRAINT chk_score_range CHECK (score >= 0 AND score <= 100);
ALTER TABLE github_accounts ADD CONSTRAINT chk_rate_limit_positive CHECK (rate_limit >= 0);
ALTER TABLE github_accounts ADD CONSTRAINT chk_rate_remaining_positive CHECK (rate_remaining >= 0);

-- 创建触发器自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_github_accounts_updated_at BEFORE UPDATE ON github_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_queries_updated_at BEFORE UPDATE ON queries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notice_configs_updated_at BEFORE UPDATE ON notice_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blacklist_updated_at BEFORE UPDATE ON blacklist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
