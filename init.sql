-- <PERSON><PERSON>ye 数据库初始化脚本
-- 创建表结构和默认数据

-- 创建UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- 创建GitHub账户表
CREATE TABLE IF NOT EXISTS github_accounts (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    token VARCHAR(255) NOT NULL,
    rate_limit INTEGER DEFAULT 5000,
    rate_remaining INTEGER DEFAULT 5000,
    rate_reset TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建查询表
CREATE TABLE IF NOT EXISTS queries (
    id SERIAL PRIMARY KEY,
    keyword VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建结果表
CREATE TABLE IF NOT EXISTS results (
    id SERIAL PRIMARY KEY,
    query_id INTEGER REFERENCES queries(id),
    repository_name VARCHAR(255) NOT NULL,
    repository_url VARCHAR(500) NOT NULL,
    file_path VARCHAR(500),
    file_url VARCHAR(500),
    content_snippet TEXT,
    sha VARCHAR(40),
    score FLOAT DEFAULT 0,
    is_processed BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(repository_url, file_path, sha)
);

-- 创建通知配置表
CREATE TABLE IF NOT EXISTS notice_configs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    webhook_url VARCHAR(500),
    email_to VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建黑名单表
CREATE TABLE IF NOT EXISTS blacklist (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    value VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(type, value)
);

-- 创建设置表
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO users (username, password_hash, email, is_active)
VALUES ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', true)
ON CONFLICT (username) DO NOTHING;

-- 插入默认系统设置
INSERT INTO settings (key, value, description) VALUES 
('system_name', '"GodEye"', '系统名称'),
('version', '"1.0.0"', '系统版本'),
('max_search_results', '1000', '最大搜索结果数'),
('search_interval', '3600', '搜索间隔(秒)'),
('rate_limit_threshold', '100', 'API限制阈值')
ON CONFLICT (key) DO NOTHING;

-- 创建全文搜索索引
CREATE INDEX IF NOT EXISTS idx_results_content_search ON results USING gin(to_tsvector('english', content_snippet));
CREATE INDEX IF NOT EXISTS idx_results_repo_search ON results USING gin(to_tsvector('english', repository_name));

-- 创建复合索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_results_query_created ON results(query_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_results_processed_created ON results(is_processed, created_at DESC);

-- 添加约束确保数据完整性
ALTER TABLE results ADD CONSTRAINT chk_score_range CHECK (score >= 0 AND score <= 100);
ALTER TABLE github_accounts ADD CONSTRAINT chk_rate_limit_positive CHECK (rate_limit >= 0);
ALTER TABLE github_accounts ADD CONSTRAINT chk_rate_remaining_positive CHECK (rate_remaining >= 0);

-- 创建触发器自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_github_accounts_updated_at BEFORE UPDATE ON github_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_queries_updated_at BEFORE UPDATE ON queries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notice_configs_updated_at BEFORE UPDATE ON notice_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blacklist_updated_at BEFORE UPDATE ON blacklist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
