# GodEye 系统诊断报告和启动指南

## 当前状况

### 技术困难说明
在尝试启动和验证 GodEye 系统时，我遇到了终端环境问题：
- 所有命令执行都没有输出响应
- 无法正常执行 Docker 命令
- 端口检查脚本无法运行

### 系统文件完整性检查 ✅
经过检查，所有关键文件都已正确创建：

#### 配置文件
- ✅ `docker-compose.dev.yml` - Docker 编排配置完整
- ✅ `.env.dev` - 环境变量配置正确
- ✅ `nginx.dev.conf` - Nginx 配置文件存在

#### 服务代码
- ✅ 认证服务 (`services/auth/`) - Go 代码完整
- ✅ 监控服务 (`services/monitor/`) - Go 代码完整  
- ✅ 通知服务 (`services/notification/`) - Go 代码完整
- ✅ 前端项目 (`frontend/`) - Next.js 项目完整，依赖已安装

#### 测试工具
- ✅ `test_system_status.html` - 浏览器端系统状态测试页面
- ✅ `check_ports.py` - Python 端口检查脚本
- ✅ `start_services.sh` - 服务启动脚本

## 手动启动指南

### 第一步：确认 Docker 环境
```bash
# 检查 Docker 是否运行
docker --version
docker info

# 检查 Docker Compose
docker-compose --version
```

### 第二步：启动 GodEye 系统
```bash
# 进入项目目录
cd /Users/<USER>/hawkeye/godeye

# 停止可能存在的服务
docker-compose -f docker-compose.dev.yml down

# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps
```

### 第三步：验证服务状态
```bash
# 运行端口检查
python3 check_ports.py

# 或者手动检查端口
lsof -i :3000  # 前端服务
lsof -i :8080  # Nginx 网关
lsof -i :8081  # 认证服务
lsof -i :8082  # 监控服务
lsof -i :8083  # 通知服务
```

## 服务端口映射

| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| 前端 (Next.js) | 3000 | 3000 | 开发服务器 |
| Nginx 网关 | 80 | 8080 | 主要访问入口 |
| 认证服务 | 8080 | 8081 | Auth API |
| 监控服务 | 8080 | 8082 | Monitor API |
| 通知服务 | 8080 | 8083 | Notification API |
| PostgreSQL | 5432 | 5433 | 数据库 |
| Redis | 6379 | 6380 | 缓存 |
| Elasticsearch | 9200 | 9201 | 搜索引擎 |
| RabbitMQ | 5672 | 5673 | 消息队列 |
| RabbitMQ 管理 | 15672 | 15673 | 管理界面 |

## 访问地址

### 主要入口
- **系统主页**: http://localhost:8080
- **前端开发服务器**: http://localhost:3000

### 管理界面
- **RabbitMQ 管理**: http://localhost:15673
  - 用户名: godeye_dev
  - 密码: dev123

### API 端点
- **认证服务**: http://localhost:8081
- **监控服务**: http://localhost:8082  
- **通知服务**: http://localhost:8083

## 默认登录信息
- **用户名**: <EMAIL>
- **密码**: admin123

## 功能验证清单

### 基础功能测试
- [ ] 访问 http://localhost:8080 显示登录页面
- [ ] 使用默认账号登录成功
- [ ] 导航菜单显示完整（包括通知配置）
- [ ] 仪表板页面正常显示

### 账号管理测试
- [ ] 添加新的 GitHub/GitLab/Gitee 账号
- [ ] 账号列表显示正确
- [ ] 账号状态检查功能正常

### 监控功能测试
- [ ] 创建新的监控任务
- [ ] 关键词搜索功能正常
- [ ] 搜索结果显示正确
- [ ] 白名单功能正常（仓库级别和文件级别）

### 通知功能测试
- [ ] 通知渠道配置页面正常
- [ ] 邮件通知配置
- [ ] 企业微信通知配置
- [ ] 飞书通知配置
- [ ] 钉钉通知配置
- [ ] 通知发送测试

## 故障排除

### 如果服务无法启动
1. 检查 Docker Desktop 是否运行
2. 检查端口是否被占用
3. 查看 Docker 日志：`docker-compose -f docker-compose.dev.yml logs`

### 如果前端无法访问
1. 检查 Node.js 依赖：`cd frontend && npm install`
2. 检查环境变量配置
3. 查看前端日志：`docker-compose -f docker-compose.dev.yml logs frontend-dev`

### 如果数据库连接失败
1. 检查 PostgreSQL 容器状态
2. 验证数据库配置
3. 查看数据库日志：`docker-compose -f docker-compose.dev.yml logs postgres-dev`

## 下一步行动

1. **立即执行**: 按照手动启动指南启动系统
2. **验证功能**: 使用功能验证清单逐项测试
3. **报告问题**: 如发现问题，请提供具体的错误信息
4. **完善测试**: 根据测试结果进一步完善系统

## 测试工具使用

### 浏览器测试页面
打开 `file:///Users/<USER>/hawkeye/godeye/test_system_status.html` 进行在线测试

### Python 端口检查
```bash
cd /Users/<USER>/hawkeye/godeye
python3 check_ports.py
```

---

**注意**: 由于当前终端环境限制，我无法直接执行启动命令。请按照上述指南手动启动系统，然后我们可以继续进行功能验证和问题修复。
