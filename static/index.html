<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GodEye - GitHub代码泄露监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .logo p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
        }
        
        .alert {
            padding: 0.75rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            display: none;
        }
        
        .alert.error {
            background: #fee;
            color: #c33;
            border: 1px solid #fcc;
        }
        
        .alert.success {
            background: #efe;
            color: #363;
            border: 1px solid #cfc;
        }
        
        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        
        .status h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.25rem;
        }
        
        .status-ok {
            color: #28a745;
        }
        
        .status-error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🔍 GodEye</h1>
            <p>GitHub代码泄露监控系统</p>
        </div>
        
        <div id="alert" class="alert"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit" class="btn">登录</button>
        </form>
        
        <div class="status">
            <h3>系统状态</h3>
            <div class="status-item">
                <span>服务状态:</span>
                <span id="serviceStatus" class="status-error">检查中...</span>
            </div>
            <div class="status-item">
                <span>数据库:</span>
                <span id="dbStatus" class="status-error">检查中...</span>
            </div>
            <div class="status-item">
                <span>API接口:</span>
                <span id="apiStatus" class="status-error">检查中...</span>
            </div>
        </div>
    </div>

    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('serviceStatus').textContent = '正常';
                    document.getElementById('serviceStatus').className = 'status-ok';
                    document.getElementById('dbStatus').textContent = '连接正常';
                    document.getElementById('dbStatus').className = 'status-ok';
                    document.getElementById('apiStatus').textContent = '可用';
                    document.getElementById('apiStatus').className = 'status-ok';
                } else {
                    throw new Error('服务异常');
                }
            } catch (error) {
                document.getElementById('serviceStatus').textContent = '异常';
                document.getElementById('serviceStatus').className = 'status-error';
                document.getElementById('dbStatus').textContent = '连接失败';
                document.getElementById('dbStatus').className = 'status-error';
                document.getElementById('apiStatus').textContent = '不可用';
                document.getElementById('apiStatus').className = 'status-error';
            }
        }
        
        // 显示提示信息
        function showAlert(message, type = 'error') {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert ${type}`;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }
        
        // 登录处理
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('登录成功！正在跳转...', 'success');
                    localStorage.setItem('token', data.result.token);
                    
                    // 简单的成功页面
                    setTimeout(() => {
                        document.body.innerHTML = `
                            <div class="container">
                                <div class="logo">
                                    <h1>🎉 登录成功</h1>
                                    <p>欢迎使用 GodEye 系统</p>
                                </div>
                                <div class="status">
                                    <h3>用户信息</h3>
                                    <div class="status-item">
                                        <span>用户名:</span>
                                        <span class="status-ok">${data.result.user.username}</span>
                                    </div>
                                    <div class="status-item">
                                        <span>邮箱:</span>
                                        <span class="status-ok">${data.result.user.email || '未设置'}</span>
                                    </div>
                                    <div class="status-item">
                                        <span>状态:</span>
                                        <span class="status-ok">已认证</span>
                                    </div>
                                </div>
                                <p style="text-align: center; margin-top: 2rem; color: #666;">
                                    简化架构测试版本 - 核心功能正常运行
                                </p>
                            </div>
                        `;
                    }, 1000);
                } else {
                    showAlert(data.msg || '登录失败');
                }
            } catch (error) {
                showAlert('网络错误，请检查服务是否正常运行');
            }
        });
        
        // 页面加载时检查系统状态
        checkSystemStatus();
        
        // 每30秒检查一次状态
        setInterval(checkSystemStatus, 30000);
    </script>
</body>
</html>
