#!/usr/bin/env python3

import subprocess
import time
import requests
import sys
import os

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_port(port):
    """检查端口是否可访问"""
    try:
        response = requests.get(f"http://localhost:{port}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def main():
    print("🧹 GodEye Docker管理器")
    print("====================")
    
    # 切换到godeye目录
    godeye_dir = os.path.join(os.getcwd(), "godeye")
    if not os.path.exists(godeye_dir):
        print("❌ godeye目录不存在")
        return
    
    os.chdir(godeye_dir)
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 1. 检查当前Docker状态
    print("\n1. 检查Docker状态...")
    success, stdout, stderr = run_command("docker ps")
    if success:
        print("✅ Docker运行正常")
        if "godeye" in stdout:
            print("🔍 发现GodEye容器正在运行")
    else:
        print("❌ Docker未运行或有问题")
        print(f"错误: {stderr}")
        return
    
    # 2. 停止现有容器
    print("\n2. 停止现有容器...")
    commands = [
        "docker-compose down --remove-orphans",
        "docker-compose -f docker-compose.simple.yml down --remove-orphans"
    ]
    
    for cmd in commands:
        success, stdout, stderr = run_command(cmd)
        if success:
            print(f"✅ 执行成功: {cmd}")
        else:
            print(f"⚠️  执行失败: {cmd}")
    
    # 3. 清理镜像
    print("\n3. 清理GodEye镜像...")
    success, stdout, stderr = run_command("docker images | grep godeye")
    if success and stdout.strip():
        print("🔍 发现GodEye镜像，正在清理...")
        run_command("docker images | grep godeye | awk '{print $3}' | xargs docker rmi -f")
        print("✅ 镜像清理完成")
    else:
        print("ℹ️  没有发现GodEye镜像")
    
    # 4. 检查端口
    print("\n4. 检查端口占用...")
    ports = [80, 3000, 8080]
    for port in ports:
        if check_port(port):
            print(f"⚠️  端口 {port} 被占用")
        else:
            print(f"✅ 端口 {port} 可用")
    
    # 5. 构建新镜像
    print("\n5. 构建新镜像...")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.simple.yml build --no-cache")
    if success:
        print("✅ 镜像构建成功")
    else:
        print("❌ 镜像构建失败")
        print(f"错误: {stderr}")
        return
    
    # 6. 启动服务
    print("\n6. 启动服务...")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.simple.yml up -d")
    if success:
        print("✅ 服务启动成功")
    else:
        print("❌ 服务启动失败")
        print(f"错误: {stderr}")
        return
    
    # 7. 等待服务就绪
    print("\n7. 等待服务就绪...")
    for i in range(30):
        if check_port(80):
            print(f"✅ 服务就绪 (等待 {i+1} 秒)")
            break
        print(f"⏳ 等待服务启动... ({i+1}/30)")
        time.sleep(2)
    else:
        print("❌ 服务启动超时")
        # 显示日志
        success, stdout, stderr = run_command("docker-compose -f docker-compose.simple.yml logs --tail=50")
        if success:
            print("📋 服务日志:")
            print(stdout)
        return
    
    # 8. 基础测试
    print("\n8. 基础功能测试...")
    try:
        # 测试健康检查
        response = requests.get("http://localhost:80/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print(f"⚠️  健康检查失败: {response.status_code}")
        
        # 测试登录
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post("http://localhost:80/api/auth/login", json=login_data, timeout=10)
        if response.status_code == 200:
            print("✅ 登录测试通过")
            data = response.json()
            if "token" in data.get("data", {}):
                print("✅ Token获取成功")
            else:
                print("⚠️  Token获取失败")
        else:
            print(f"⚠️  登录测试失败: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
    
    # 9. 显示状态
    print("\n9. 显示最终状态...")
    success, stdout, stderr = run_command("docker-compose -f docker-compose.simple.yml ps")
    if success:
        print("📊 容器状态:")
        print(stdout)
    
    print("\n🎉 Docker管理完成！")
    print("==================")
    print("🌐 访问地址: http://localhost:80")
    print("👤 用户名: admin")
    print("🔑 密码: admin123")

if __name__ == "__main__":
    main()
