#!/bin/bash

# GodEye 系统一键部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示GodEye Logo
show_logo() {
    echo ""
    echo "  ██████╗  ██████╗ ██████╗ ███████╗██╗   ██╗███████╗"
    echo " ██╔════╝ ██╔═══██╗██╔══██╗██╔════╝╚██╗ ██╔╝██╔════╝"
    echo " ██║  ███╗██║   ██║██║  ██║█████╗   ╚████╔╝ █████╗  "
    echo " ██║   ██║██║   ██║██║  ██║██╔══╝    ╚██╔╝  ██╔══╝  "
    echo " ╚██████╔╝╚██████╔╝██████╔╝███████╗   ██║   ███████╗"
    echo "  ╚═════╝  ╚═════╝ ╚═════╝ ╚══════╝   ╚═╝   ╚══════╝"
    echo ""
    echo "🦅 GodEye - 多平台代码泄露监控系统"
    echo "✨ 支持 GitHub + GitLab + Gitee 全平台监控"
    echo ""
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Docker服务
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 清理旧环境
cleanup_old() {
    log_info "清理旧环境..."
    
    # 停止旧容器
    docker-compose -f docker-compose.simple.yml down --volumes --remove-orphans 2>/dev/null || true
    
    # 清理旧镜像
    docker image prune -f 2>/dev/null || true
    
    # 清理未使用的网络
    docker network prune -f 2>/dev/null || true
    
    log_success "环境清理完成"
}

# 构建GodEye镜像
build_godeye() {
    log_info "构建GodEye应用镜像..."
    
    # 使用简化的Dockerfile构建
    docker build -f Dockerfile.simple -t godeye:latest . --no-cache
    
    if [ $? -eq 0 ]; then
        log_success "GodEye镜像构建成功"
    else
        log_error "GodEye镜像构建失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动GodEye服务..."
    
    # 启动所有服务
    docker-compose -f docker-compose.simple.yml up -d
    
    if [ $? -eq 0 ]; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    local max_attempts=60
    local attempt=0
    
    # 等待PostgreSQL
    while [ $attempt -lt $max_attempts ]; do
        if docker exec godeye-postgres pg_isready -U godeye &>/dev/null; then
            log_success "PostgreSQL已就绪"
            break
        fi
        
        attempt=$((attempt + 1))
        sleep 2
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "PostgreSQL启动超时"
            return 1
        fi
    done
    
    # 等待Redis
    attempt=0
    while [ $attempt -lt $max_attempts ]; do
        if docker exec godeye-redis redis-cli ping &>/dev/null; then
            log_success "Redis已就绪"
            break
        fi
        
        attempt=$((attempt + 1))
        sleep 2
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "Redis启动超时"
            return 1
        fi
    done
    
    # 等待GodEye应用
    attempt=0
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost/health &>/dev/null; then
            log_success "GodEye应用已就绪"
            break
        fi
        
        attempt=$((attempt + 1))
        sleep 3
        
        if [ $attempt -eq $max_attempts ]; then
            log_warning "GodEye应用启动超时，但可能仍在初始化中"
            break
        fi
    done
}

# 显示服务状态
show_status() {
    echo ""
    echo "=================================="
    echo "🦅 GodEye 系统状态"
    echo "=================================="
    
    # 显示容器状态
    docker-compose -f docker-compose.simple.yml ps
    
    echo ""
    echo "🌐 访问地址:"
    echo "   主应用: http://localhost"
    echo "   健康检查: http://localhost/health"
    echo ""
    echo "👤 默认登录信息:"
    echo "   用户名: admin"
    echo "   密码: admin123"
    echo ""
    echo "📊 服务健康检查:"
    
    # 检查各服务状态
    if curl -s http://localhost/health &>/dev/null; then
        echo "   ✅ GodEye应用: 正常"
    else
        echo "   ❌ GodEye应用: 异常"
    fi
    
    if docker exec godeye-postgres pg_isready -U godeye &>/dev/null; then
        echo "   ✅ PostgreSQL: 正常"
    else
        echo "   ❌ PostgreSQL: 异常"
    fi
    
    if docker exec godeye-redis redis-cli ping &>/dev/null; then
        echo "   ✅ Redis: 正常"
    else
        echo "   ❌ Redis: 异常"
    fi
    
    echo ""
    echo "🔧 管理命令:"
    echo "   查看日志: docker-compose -f docker-compose.simple.yml logs -f"
    echo "   停止服务: docker-compose -f docker-compose.simple.yml down"
    echo "   重启服务: docker-compose -f docker-compose.simple.yml restart"
    echo "   运行测试: ./comprehensive_test.sh"
    echo "=================================="
}

# 初始化数据
init_data() {
    log_info "初始化系统数据..."
    
    # 等待数据库完全启动
    sleep 5
    
    # 这里可以添加数据初始化脚本
    # docker exec godeye-app ./init-data.sh
    
    log_success "数据初始化完成"
}

# 主函数
main() {
    show_logo
    
    check_environment
    cleanup_old
    build_godeye
    start_services
    wait_for_services
    init_data
    show_status
    
    echo ""
    log_success "🎉 GodEye系统部署完成！"
    log_info "您现在可以访问 http://localhost 使用GodEye系统"
    
    # 自动打开浏览器
    if command -v open &> /dev/null; then
        log_info "正在打开浏览器..."
        open http://localhost
    elif command -v xdg-open &> /dev/null; then
        log_info "正在打开浏览器..."
        xdg-open http://localhost
    fi
    
    echo ""
    echo "🚀 GodEye特色功能:"
    echo "   ✅ 多平台监控: GitHub + GitLab + Gitee"
    echo "   ✅ 全局关键词搜索"
    echo "   ✅ 智能账号管理"
    echo "   ✅ 多渠道告警: 邮件 + 企业微信 + 飞书 + 钉钉"
    echo "   ✅ 现代化Web界面"
    echo ""
}

# 处理命令行参数
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        show_status
        ;;
    "stop")
        log_info "停止GodEye服务..."
        docker-compose -f docker-compose.simple.yml down
        log_success "服务已停止"
        ;;
    "restart")
        log_info "重启GodEye服务..."
        docker-compose -f docker-compose.simple.yml restart
        log_success "服务已重启"
        ;;
    "logs")
        docker-compose -f docker-compose.simple.yml logs -f
        ;;
    "clean")
        log_info "清理所有数据..."
        docker-compose -f docker-compose.simple.yml down --volumes --remove-orphans
        docker system prune -f
        log_success "清理完成"
        ;;
    *)
        echo "用法: $0 {deploy|status|stop|restart|logs|clean}"
        echo ""
        echo "命令说明:"
        echo "  deploy  - 部署GodEye系统 (默认)"
        echo "  status  - 查看服务状态"
        echo "  stop    - 停止所有服务"
        echo "  restart - 重启所有服务"
        echo "  logs    - 查看服务日志"
        echo "  clean   - 清理所有数据"
        exit 1
        ;;
esac
