#!/bin/bash

# GodEye 自动部署脚本
set -e

echo "🚀 GodEye 自动部署开始"
echo "====================="

# 进入正确目录
cd "$(dirname "$0")"
echo "📁 当前目录: $(pwd)"

# 1. 强制停止所有相关容器
echo "🛑 停止现有容器..."
docker stop $(docker ps -q --filter "name=godeye") 2>/dev/null || true
docker stop $(docker ps -q --filter "name=postgres") 2>/dev/null || true
docker stop $(docker ps -q --filter "name=redis") 2>/dev/null || true

# 2. 删除容器
echo "🗑️  删除容器..."
docker rm $(docker ps -aq --filter "name=godeye") 2>/dev/null || true
docker rm $(docker ps -aq --filter "name=postgres") 2>/dev/null || true
docker rm $(docker ps -aq --filter "name=redis") 2>/dev/null || true

# 3. 清理镜像
echo "🧹 清理镜像..."
docker rmi $(docker images -q --filter "reference=godeye*") 2>/dev/null || true

# 4. 清理网络
echo "🌐 清理网络..."
docker network rm godeye_default 2>/dev/null || true

# 5. 构建新镜像
echo "🔨 构建镜像..."
docker-compose -f docker-compose.simple.yml build --no-cache

# 6. 启动服务
echo "🚀 启动服务..."
docker-compose -f docker-compose.simple.yml up -d

# 7. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 8. 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.simple.yml ps

# 9. 测试连接
echo "🧪 测试连接..."
for i in {1..10}; do
    if curl -s http://localhost:80/health > /dev/null; then
        echo "✅ 服务连接成功!"
        break
    fi
    echo "⏳ 等待连接... ($i/10)"
    sleep 3
done

# 10. 显示最终状态
echo "📊 最终状态:"
docker-compose -f docker-compose.simple.yml ps
echo ""
echo "🎉 部署完成!"
echo "🌐 访问地址: http://localhost:80"
echo "👤 用户名: admin"
echo "🔑 密码: admin123"
