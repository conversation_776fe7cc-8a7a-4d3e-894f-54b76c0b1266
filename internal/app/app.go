package app

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"

	"godeye/internal/config"
	"godeye/internal/handlers"
	"godeye/internal/middleware"
	"godeye/internal/services"
)

// App 应用实例
type App struct {
	config   *config.Config
	db       *sql.DB
	router   *gin.Engine
	services *services.Services
}

// New 创建新的应用实例
func New(cfg *config.Config, db *sql.DB) *App {
	// 设置Gin模式
	gin.SetMode(cfg.GinMode)

	// 创建服务层
	services := services.New(db, cfg)

	// 创建路由
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())

	app := &App{
		config:   cfg,
		db:       db,
		router:   router,
		services: services,
	}

	app.setupRoutes()
	return app
}

// setupRoutes 设置路由
func (a *App) setupRoutes() {
	// 创建处理器
	h := handlers.New(a.services)
	notificationHandler := handlers.NewNotificationHandler(a.services.Notice)
	blacklistHandler := handlers.NewBlacklistHandler(a.services.Blacklist)

	// 静态文件服务
	a.router.Static("/static", "./static")
	a.router.StaticFile("/", "./static/index.html")
	a.router.StaticFile("/favicon.ico", "./static/favicon.ico")

	// 健康检查
	a.router.GET("/health", h.Health)

	// API路由组
	api := a.router.Group("/api")
	{
		// 认证相关（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/login", h.Login)
			auth.POST("/logout", h.Logout)
		}

		// 需要认证的路由
		protected := api.Group("")
		protected.Use(middleware.AuthRequired(a.services.Auth))
		{
			// 用户信息
			protected.GET("/auth/user", h.GetUserInfo)
			protected.GET("/auth/check", h.CheckAuth)

			// GitHub账号管理
			github := protected.Group("/setting/github")
			{
				github.GET("", h.GetGithubAccounts)
				github.POST("", h.CreateGithubAccount)
				github.PUT("/:id", h.UpdateGithubAccount)
				github.DELETE("/:id", h.DeleteGithubAccount)
			}

			// 查询关键词管理
			query := protected.Group("/setting/query")
			{
				query.GET("", h.GetQueries)
				query.POST("", h.CreateQuery)
				query.PUT("/:id", h.UpdateQuery)
				query.DELETE("/:id", h.DeleteQuery)
			}

			// 通知配置
			notice := protected.Group("/setting")
			{
				notice.GET("/notice", notificationHandler.GetNoticeConfigs)
				notice.POST("/notice", notificationHandler.CreateNoticeConfig)
				notice.PUT("/notice/:id", notificationHandler.UpdateNoticeConfig)
				notice.DELETE("/notice/:id", notificationHandler.DeleteNoticeConfig)
				notice.POST("/notice/:id/test", notificationHandler.TestNoticeConfig)
				notice.GET("/notice/types", notificationHandler.GetNotificationTypes)
				
				notice.GET("/webhook", h.GetWebhookConfigs)
				notice.POST("/webhook", h.CreateWebhookConfig)
				notice.DELETE("/webhook", h.DeleteWebhookConfig)
				
				notice.GET("/mail", h.GetMailConfigs)
				notice.POST("/mail", h.CreateMailConfig)
			}

			// 黑名单管理
			blacklist := protected.Group("/setting/blacklist")
			{
				blacklist.GET("", blacklistHandler.GetBlacklist)
				blacklist.POST("", blacklistHandler.CreateBlacklistItem)
				blacklist.PUT("/:id", blacklistHandler.UpdateBlacklistItem)
				blacklist.DELETE("/:id", blacklistHandler.DeleteBlacklistItem)
				blacklist.POST("/repository", blacklistHandler.AddRepositoryToBlacklist)
				blacklist.POST("/file", blacklistHandler.AddFileToBlacklist)
				blacklist.GET("/check", blacklistHandler.CheckBlacklist)
			}

			// 搜索结果
			leakage := protected.Group("/leakage")
			{
				leakage.GET("", h.GetLeakageResults)
				leakage.GET("/code", h.GetLeakageCode)
				leakage.GET("/info", h.GetLeakageInfo)
				leakage.POST("/process", h.ProcessLeakage)
			}

			// 统计数据
			protected.GET("/trend", h.GetTrend)
			protected.GET("/statistic", h.GetStatistic)

			// 通知发送
			notification := protected.Group("/notification")
			{
				notification.POST("/send", notificationHandler.SendNotification)
				notification.POST("/broadcast", notificationHandler.SendToAllChannels)
			}

			// 定时任务管理
			cron := protected.Group("/setting/cron")
			{
				cron.GET("", h.GetCronStatus)
				cron.POST("/start", h.StartCron)
				cron.POST("/stop", h.StopCron)
			}
		}
	}
}

// Run 启动应用
func (a *App) Run() error {
	return a.router.Run(":" + a.config.Port)
}
