package config

import (
	"os"
)

// Config 应用配置
type Config struct {
	Port        string
	DatabaseURL string
	RedisURL    string
	SecretKey   string
	GinMode     string
}

// Load 加载配置
func Load() *Config {
	return &Config{
		Port:        getEnv("PORT", "8080"),
		DatabaseURL: getEnv("DATABASE_URL", "postgres://godeye:godeye123@localhost:5432/godeye?sslmode=disable"),
		RedisURL:    getEnv("REDIS_URL", "redis://localhost:6379/0"),
		SecretKey:   getEnv("SECRET_KEY", "godeye-secret-key-change-in-production"),
		GinMode:     getEnv("GIN_MODE", "debug"),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
