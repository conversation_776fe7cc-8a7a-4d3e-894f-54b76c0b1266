package database

import (
	"database/sql"
	"fmt"

	_ "github.com/lib/pq"
)

// Connect 连接数据库
func Connect(databaseURL string) (*sql.DB, error) {
	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	return db, nil
}

// Migrate 执行数据库迁移
func Migrate(db *sql.DB) error {
	migrations := []string{
		createUsersTable,
		createGithubAccountsTable,
		createQueriesTable,
		createResultsTable,
		createBlacklistTable,
		createNoticeTable,
		createSettingsTable,
		createIndexes,
	}

	for _, migration := range migrations {
		if _, err := db.Exec(migration); err != nil {
			return fmt.Errorf("执行迁移失败: %w", err)
		}
	}

	return nil
}

const createUsersTable = `
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);`

const createGithubAccountsTable = `
CREATE TABLE IF NOT EXISTS github_accounts (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    token VARCHAR(255) NOT NULL,
    rate_limit INTEGER DEFAULT 5000,
    rate_remaining INTEGER DEFAULT 5000,
    rate_reset TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`

const createQueriesTable = `
CREATE TABLE IF NOT EXISTS queries (
    id SERIAL PRIMARY KEY,
    keyword VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`

const createResultsTable = `
CREATE TABLE IF NOT EXISTS results (
    id SERIAL PRIMARY KEY,
    query_id INTEGER REFERENCES queries(id),
    repository_name VARCHAR(255) NOT NULL,
    repository_url VARCHAR(500) NOT NULL,
    file_path VARCHAR(500),
    file_url VARCHAR(500),
    content_snippet TEXT,
    sha VARCHAR(40),
    score FLOAT DEFAULT 0,
    is_processed BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(repository_url, file_path, sha)
);`

const createBlacklistTable = `
CREATE TABLE IF NOT EXISTS blacklist (
    id SERIAL PRIMARY KEY,
    type VARCHAR(20) NOT NULL CHECK (type IN ('repository', 'file')),
    value VARCHAR(500) NOT NULL,
    reason TEXT,
    created_by VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(type, value)
);`

const createNoticeTable = `
CREATE TABLE IF NOT EXISTS notice_configs (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('email', 'wechat', 'dingtalk', 'feishu', 'slack')),
    config TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`

const createSettingsTable = `
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`

const createIndexes = `
CREATE INDEX IF NOT EXISTS idx_results_query_id ON results(query_id);
CREATE INDEX IF NOT EXISTS idx_results_created_at ON results(created_at);
CREATE INDEX IF NOT EXISTS idx_results_repository_name ON results(repository_name);
CREATE INDEX IF NOT EXISTS idx_github_accounts_username ON github_accounts(username);
CREATE INDEX IF NOT EXISTS idx_queries_keyword ON queries(keyword);
CREATE INDEX IF NOT EXISTS idx_blacklist_type_value ON blacklist(type, value);
`
