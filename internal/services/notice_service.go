package services

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/smtp"
	"time"

	"godeye/internal/models"
)

// NoticeService 通知服务接口
type NoticeService interface {
	// 通知配置管理
	GetNoticeConfigs() ([]*models.NoticeConfig, error)
	CreateNoticeConfig(config *models.CreateNoticeConfigRequest) (*models.NoticeConfig, error)
	UpdateNoticeConfig(id int, config *models.UpdateNoticeConfigRequest) (*models.NoticeConfig, error)
	DeleteNoticeConfig(id int) error

	// 发送通知
	SendNotification(configID int, title, content string) error
	SendToAllActiveChannels(title, content string) error

	// 测试通知
	TestNotification(config *models.NoticeConfig) error
}

// noticeService 通知服务实现
type noticeService struct {
	db   *sql.DB
	repo models.NoticeConfigRepository
}

// NewNoticeService 创建通知服务
func NewNoticeService(db *sql.DB) NoticeService {
	return &noticeService{
		db:   db,
		repo: models.NewNoticeConfigRepository(db),
	}
}

// GetNoticeConfigs 获取通知配置
func (s *noticeService) GetNoticeConfigs() ([]*models.NoticeConfig, error) {
	return s.repo.GetAll()
}

// CreateNoticeConfig 创建通知配置
func (s *noticeService) CreateNoticeConfig(req *models.CreateNoticeConfigRequest) (*models.NoticeConfig, error) {
	config := &models.NoticeConfig{
		Name:        req.Name,
		Type:        req.Type,
		Config:      req.Config,
		IsActive:    req.IsActive,
		Description: req.Description,
	}

	if err := s.repo.Create(config); err != nil {
		return nil, err
	}

	return config, nil
}

// UpdateNoticeConfig 更新通知配置
func (s *noticeService) UpdateNoticeConfig(id int, req *models.UpdateNoticeConfigRequest) (*models.NoticeConfig, error) {
	config, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}

	if config == nil {
		return nil, fmt.Errorf("通知配置不存在")
	}

	if req.Name != "" {
		config.Name = req.Name
	}
	if req.Config != "" {
		config.Config = req.Config
	}
	if req.IsActive != nil {
		config.IsActive = *req.IsActive
	}
	if req.Description != "" {
		config.Description = req.Description
	}

	if err := s.repo.Update(config); err != nil {
		return nil, err
	}

	return config, nil
}

// DeleteNoticeConfig 删除通知配置
func (s *noticeService) DeleteNoticeConfig(id int) error {
	return s.repo.Delete(id)
}

// SendNotification 发送通知
func (s *noticeService) SendNotification(configID int, title, content string) error {
	config, err := s.repo.GetByID(configID)
	if err != nil {
		return err
	}

	if config == nil || !config.IsActive {
		return fmt.Errorf("通知配置不存在或未激活")
	}

	return s.sendByType(config, title, content)
}

// SendToAllActiveChannels 发送到所有活跃通道
func (s *noticeService) SendToAllActiveChannels(title, content string) error {
	configs, err := s.repo.GetActive()
	if err != nil {
		return err
	}

	var lastError error
	for _, config := range configs {
		if err := s.sendByType(config, title, content); err != nil {
			lastError = err
			// 记录错误但继续发送其他通道
		}
	}

	return lastError
}

// TestNotification 测试通知
func (s *noticeService) TestNotification(config *models.NoticeConfig) error {
	testTitle := "GodEye 通知测试"
	testContent := fmt.Sprintf("这是一条来自 GodEye 系统的测试通知。\n时间: %s", time.Now().Format("2006-01-02 15:04:05"))

	return s.sendByType(config, testTitle, testContent)
}

// sendByType 根据类型发送通知
func (s *noticeService) sendByType(config *models.NoticeConfig, title, content string) error {
	switch config.Type {
	case "email":
		return s.sendEmail(config, title, content)
	case "wechat":
		return s.sendWechat(config, title, content)
	case "dingtalk":
		return s.sendDingtalk(config, title, content)
	case "feishu":
		return s.sendFeishu(config, title, content)
	case "slack":
		return s.sendSlack(config, title, content)
	default:
		return fmt.Errorf("不支持的通知类型: %s", config.Type)
	}
}

// sendEmail 发送邮件通知
func (s *noticeService) sendEmail(config *models.NoticeConfig, title, content string) error {
	var emailConfig models.EmailConfig
	if err := json.Unmarshal([]byte(config.Config), &emailConfig); err != nil {
		return fmt.Errorf("邮件配置解析失败: %w", err)
	}

	// 构建邮件内容
	message := fmt.Sprintf("To: %s\r\n", emailConfig.To)
	message += fmt.Sprintf("Subject: %s\r\n", title)
	message += "Content-Type: text/html; charset=UTF-8\r\n"
	message += "\r\n"
	message += fmt.Sprintf(`
		<html>
		<body>
			<h2>%s</h2>
			<div style="white-space: pre-wrap;">%s</div>
			<hr>
			<p><small>来自 GodEye 代码泄露监控系统</small></p>
		</body>
		</html>
	`, title, content)

	// 发送邮件
	auth := smtp.PlainAuth("", emailConfig.Username, emailConfig.Password, emailConfig.Host)
	addr := fmt.Sprintf("%s:%d", emailConfig.Host, emailConfig.Port)

	return smtp.SendMail(addr, auth, emailConfig.From, []string{emailConfig.To}, []byte(message))
}

// sendWechat 发送企业微信通知
func (s *noticeService) sendWechat(config *models.NoticeConfig, title, content string) error {
	var wechatConfig models.WechatConfig
	if err := json.Unmarshal([]byte(config.Config), &wechatConfig); err != nil {
		return fmt.Errorf("企业微信配置解析失败: %w", err)
	}

	// 构建消息体
	message := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]string{
			"content": fmt.Sprintf("%s\n\n%s", title, content),
		},
	}

	return s.sendWebhook(wechatConfig.WebhookURL, message)
}

// sendDingtalk 发送钉钉通知
func (s *noticeService) sendDingtalk(config *models.NoticeConfig, title, content string) error {
	var dingtalkConfig models.DingtalkConfig
	if err := json.Unmarshal([]byte(config.Config), &dingtalkConfig); err != nil {
		return fmt.Errorf("钉钉配置解析失败: %w", err)
	}

	// 构建消息体
	message := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]string{
			"content": fmt.Sprintf("%s\n\n%s", title, content),
		},
	}

	return s.sendWebhook(dingtalkConfig.WebhookURL, message)
}

// sendFeishu 发送飞书通知
func (s *noticeService) sendFeishu(config *models.NoticeConfig, title, content string) error {
	var feishuConfig models.FeishuConfig
	if err := json.Unmarshal([]byte(config.Config), &feishuConfig); err != nil {
		return fmt.Errorf("飞书配置解析失败: %w", err)
	}

	// 构建消息体
	message := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]string{
			"text": fmt.Sprintf("%s\n\n%s", title, content),
		},
	}

	return s.sendWebhook(feishuConfig.WebhookURL, message)
}

// sendSlack 发送Slack通知
func (s *noticeService) sendSlack(config *models.NoticeConfig, title, content string) error {
	var slackConfig models.SlackConfig
	if err := json.Unmarshal([]byte(config.Config), &slackConfig); err != nil {
		return fmt.Errorf("Slack配置解析失败: %w", err)
	}

	// 构建消息体
	message := map[string]interface{}{
		"text": fmt.Sprintf("*%s*\n\n%s", title, content),
	}

	return s.sendWebhook(slackConfig.WebhookURL, message)
}

// sendWebhook 发送Webhook请求
func (s *noticeService) sendWebhook(url string, message map[string]interface{}) error {
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("消息序列化失败: %w", err)
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("发送失败，状态码: %d", resp.StatusCode)
	}

	return nil
}
