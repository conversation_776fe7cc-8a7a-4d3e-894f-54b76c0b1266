package services

import "godeye/internal/models"

// QueryService 查询服务接口
type QueryService interface {
	CreateQuery(req *models.CreateQueryRequest) (*models.Query, error)
	GetQueries() ([]*models.Query, error)
	GetQuery(id int) (*models.Query, error)
	UpdateQuery(id int, req *models.UpdateQueryRequest) (*models.Query, error)
	DeleteQuery(id int) error
	GetActiveQueries() ([]*models.Query, error)
}

// queryService 查询服务实现
type queryService struct {
	repo models.QueryRepository
}

// NewQueryService 创建查询服务
func NewQueryService(repo models.QueryRepository) QueryService {
	return &queryService{repo: repo}
}

// CreateQuery 创建查询
func (s *queryService) CreateQuery(req *models.CreateQueryRequest) (*models.Query, error) {
	query := &models.Query{
		Keyword:     req.Keyword,
		Description: req.Description,
		IsActive:    true,
	}
	
	if err := s.repo.Create(query); err != nil {
		return nil, err
	}
	
	return query, nil
}

// GetQueries 获取所有查询
func (s *queryService) GetQueries() ([]*models.Query, error) {
	return s.repo.GetAll()
}

// GetQuery 获取单个查询
func (s *queryService) GetQuery(id int) (*models.Query, error) {
	return s.repo.GetByID(id)
}

// UpdateQuery 更新查询
func (s *queryService) UpdateQuery(id int, req *models.UpdateQueryRequest) (*models.Query, error) {
	query, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	if query == nil {
		return nil, nil
	}
	
	if req.Keyword != "" {
		query.Keyword = req.Keyword
	}
	
	if req.Description != "" {
		query.Description = req.Description
	}
	
	if req.IsActive != nil {
		query.IsActive = *req.IsActive
	}
	
	if err := s.repo.Update(query); err != nil {
		return nil, err
	}
	
	return query, nil
}

// DeleteQuery 删除查询
func (s *queryService) DeleteQuery(id int) error {
	return s.repo.Delete(id)
}

// GetActiveQueries 获取活跃查询
func (s *queryService) GetActiveQueries() ([]*models.Query, error) {
	return s.repo.GetActive()
}
