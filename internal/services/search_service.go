package services

import (
	"fmt"
	"log"

	"godeye/internal/models"
)

// SearchService 搜索服务接口
type SearchService interface {
	ExecuteSearch(queryID int) error
	ExecuteAllActiveSearches() error
}

// searchService 搜索服务实现
type searchService struct {
	githubService    GithubService
	queryService     QueryService
	resultService    ResultService
	blacklistService BlacklistService
}

// NewSearchService 创建搜索服务
func NewSearchService(githubService GithubService, queryService QueryService, resultService ResultService, blacklistService BlacklistService) SearchService {
	return &searchService{
		githubService:    githubService,
		queryService:     queryService,
		resultService:    resultService,
		blacklistService: blacklistService,
	}
}

// ExecuteSearch 执行单个查询的搜索
func (s *searchService) ExecuteSearch(queryID int) error {
	// 获取查询信息
	query, err := s.queryService.GetQuery(queryID)
	if err != nil {
		return err
	}
	
	if query == nil || !query.IsActive {
		return fmt.Errorf("查询不存在或未激活")
	}
	
	// 获取可用的GitHub账号
	account, err := s.githubService.GetActiveAccount()
	if err != nil {
		return err
	}
	
	// 执行代码搜索
	codeResults, err := s.githubService.SearchCode(query.Keyword, account)
	if err != nil {
		log.Printf("代码搜索失败: %v", err)
		return err
	}
	
	// 处理搜索结果
	for _, result := range codeResults {
		repoName := result.Repository.GetFullName()
		filePath := result.GetPath()

		// 检查黑名单
		if s.blacklistService != nil {
			shouldSkip, reason, err := s.blacklistService.ShouldSkipResult(repoName, filePath)
			if err != nil {
				log.Printf("检查黑名单失败: %v", err)
				continue
			}
			if shouldSkip {
				log.Printf("跳过黑名单结果: %s/%s - %s", repoName, filePath, reason)
				continue
			}
		}

		req := &models.CreateResultRequest{
			QueryID:        query.ID,
			RepositoryName: repoName,
			RepositoryURL:  result.Repository.GetHTMLURL(),
			FilePath:       filePath,
			FileURL:        result.GetHTMLURL(),
			ContentSnippet: result.GetTextMatches()[0].GetFragment(),
			SHA:            result.GetSHA(),
			Score:          float64(result.GetScore()),
		}

		_, err := s.resultService.CreateResult(req)
		if err != nil {
			log.Printf("创建结果失败: %v", err)
		}
	}
	
	log.Printf("查询 %s 完成，找到 %d 个结果", query.Keyword, len(codeResults))
	return nil
}

// ExecuteAllActiveSearches 执行所有活跃查询的搜索
func (s *searchService) ExecuteAllActiveSearches() error {
	queries, err := s.queryService.GetActiveQueries()
	if err != nil {
		return err
	}
	
	for _, query := range queries {
		if err := s.ExecuteSearch(query.ID); err != nil {
			log.Printf("执行查询 %s 失败: %v", query.Keyword, err)
		}
	}
	
	return nil
}
