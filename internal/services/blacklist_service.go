package services

import (
	"database/sql"
	"fmt"
	"strings"

	"godeye/internal/models"
)

// BlacklistService 黑名单服务接口
type BlacklistService interface {
	// 黑名单管理
	GetBlacklist() ([]*models.BlacklistItem, error)
	GetBlacklistByType(itemType string) ([]*models.BlacklistItem, error)
	CreateBlacklistItem(req *models.CreateBlacklistRequest, createdBy string) (*models.BlacklistItem, error)
	UpdateBlacklistItem(id int, req *models.UpdateBlacklistRequest) (*models.BlacklistItem, error)
	DeleteBlacklistItem(id int) error
	
	// 黑名单检查
	IsRepositoryBlacklisted(repoName string) (bool, error)
	IsFileBlacklisted(filePath string) (bool, error)
	ShouldSkipResult(repoName, filePath string) (bool, string, error)
	
	// 批量操作
	AddRepositoryToBlacklist(repoName, reason, createdBy string) error
	AddFileToBlacklist(filePath, reason, createdBy string) error
	GetRepositoryBlacklist() ([]string, error)
	GetFileBlacklist() ([]string, error)
}

// blacklistService 黑名单服务实现
type blacklistService struct {
	db   *sql.DB
	repo models.BlacklistRepository
}

// NewBlacklistService 创建黑名单服务
func NewBlacklistService(db *sql.DB) BlacklistService {
	return &blacklistService{
		db:   db,
		repo: models.NewBlacklistRepository(db),
	}
}

// GetBlacklist 获取黑名单
func (s *blacklistService) GetBlacklist() ([]*models.BlacklistItem, error) {
	return s.repo.GetAll()
}

// GetBlacklistByType 根据类型获取黑名单
func (s *blacklistService) GetBlacklistByType(itemType string) ([]*models.BlacklistItem, error) {
	if itemType != "repository" && itemType != "file" {
		return nil, fmt.Errorf("无效的黑名单类型: %s", itemType)
	}
	return s.repo.GetByType(itemType)
}

// CreateBlacklistItem 创建黑名单项
func (s *blacklistService) CreateBlacklistItem(req *models.CreateBlacklistRequest, createdBy string) (*models.BlacklistItem, error) {
	// 验证类型
	if req.Type != "repository" && req.Type != "file" {
		return nil, fmt.Errorf("无效的黑名单类型: %s", req.Type)
	}
	
	// 验证值
	if strings.TrimSpace(req.Value) == "" {
		return nil, fmt.Errorf("黑名单值不能为空")
	}
	
	// 检查是否已存在
	exists, err := s.repo.IsBlacklisted(req.Type, req.Value)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, fmt.Errorf("该项已在黑名单中")
	}
	
	item := &models.BlacklistItem{
		Type:      req.Type,
		Value:     strings.TrimSpace(req.Value),
		Reason:    req.Reason,
		CreatedBy: createdBy,
	}
	
	if err := s.repo.Create(item); err != nil {
		return nil, err
	}
	
	return item, nil
}

// UpdateBlacklistItem 更新黑名单项
func (s *blacklistService) UpdateBlacklistItem(id int, req *models.UpdateBlacklistRequest) (*models.BlacklistItem, error) {
	item, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	if item == nil {
		return nil, fmt.Errorf("黑名单项不存在")
	}
	
	if req.Reason != "" {
		item.Reason = req.Reason
	}
	
	if err := s.repo.Update(item); err != nil {
		return nil, err
	}
	
	return item, nil
}

// DeleteBlacklistItem 删除黑名单项
func (s *blacklistService) DeleteBlacklistItem(id int) error {
	item, err := s.repo.GetByID(id)
	if err != nil {
		return err
	}
	
	if item == nil {
		return fmt.Errorf("黑名单项不存在")
	}
	
	return s.repo.Delete(id)
}

// IsRepositoryBlacklisted 检查仓库是否在黑名单中
func (s *blacklistService) IsRepositoryBlacklisted(repoName string) (bool, error) {
	return s.repo.IsBlacklisted("repository", repoName)
}

// IsFileBlacklisted 检查文件是否在黑名单中
func (s *blacklistService) IsFileBlacklisted(filePath string) (bool, error) {
	// 检查完整路径
	if exists, err := s.repo.IsBlacklisted("file", filePath); err != nil {
		return false, err
	} else if exists {
		return true, nil
	}
	
	// 检查文件名
	parts := strings.Split(filePath, "/")
	if len(parts) > 0 {
		fileName := parts[len(parts)-1]
		if exists, err := s.repo.IsBlacklisted("file", fileName); err != nil {
			return false, err
		} else if exists {
			return true, nil
		}
	}
	
	// 检查文件扩展名
	if strings.Contains(filePath, ".") {
		parts := strings.Split(filePath, ".")
		if len(parts) > 1 {
			ext := "." + parts[len(parts)-1]
			if exists, err := s.repo.IsBlacklisted("file", ext); err != nil {
				return false, err
			} else if exists {
				return true, nil
			}
		}
	}
	
	return false, nil
}

// ShouldSkipResult 检查搜索结果是否应该跳过
func (s *blacklistService) ShouldSkipResult(repoName, filePath string) (bool, string, error) {
	// 检查仓库黑名单
	if repoBlacklisted, err := s.IsRepositoryBlacklisted(repoName); err != nil {
		return false, "", err
	} else if repoBlacklisted {
		return true, "仓库在黑名单中", nil
	}
	
	// 检查文件黑名单
	if filePath != "" {
		if fileBlacklisted, err := s.IsFileBlacklisted(filePath); err != nil {
			return false, "", err
		} else if fileBlacklisted {
			return true, "文件在黑名单中", nil
		}
	}
	
	return false, "", nil
}

// AddRepositoryToBlacklist 添加仓库到黑名单
func (s *blacklistService) AddRepositoryToBlacklist(repoName, reason, createdBy string) error {
	req := &models.CreateBlacklistRequest{
		Type:   "repository",
		Value:  repoName,
		Reason: reason,
	}
	
	_, err := s.CreateBlacklistItem(req, createdBy)
	return err
}

// AddFileToBlacklist 添加文件到黑名单
func (s *blacklistService) AddFileToBlacklist(filePath, reason, createdBy string) error {
	req := &models.CreateBlacklistRequest{
		Type:   "file",
		Value:  filePath,
		Reason: reason,
	}
	
	_, err := s.CreateBlacklistItem(req, createdBy)
	return err
}

// GetRepositoryBlacklist 获取仓库黑名单列表
func (s *blacklistService) GetRepositoryBlacklist() ([]string, error) {
	return s.repo.GetRepositoryBlacklist()
}

// GetFileBlacklist 获取文件黑名单列表
func (s *blacklistService) GetFileBlacklist() ([]string, error) {
	return s.repo.GetFileBlacklist()
}
