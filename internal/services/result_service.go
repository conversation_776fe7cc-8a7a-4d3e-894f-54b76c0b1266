package services

import "godeye/internal/models"

// ResultService 结果服务接口
type ResultService interface {
	CreateResult(req *models.CreateResultRequest) (*models.Result, error)
	GetResults(filter *models.ResultFilter) ([]*models.ResultResponse, error)
	GetResult(id int) (*models.Result, error)
	MarkAsProcessed(id int) error
	DeleteResult(id int) error
	GetStatistics() (*models.ResultStatistics, error)
}

// resultService 结果服务实现
type resultService struct {
	repo models.ResultRepository
}

// NewResultService 创建结果服务
func NewResultService(repo models.ResultRepository) ResultService {
	return &resultService{repo: repo}
}

// CreateResult 创建结果
func (s *resultService) CreateResult(req *models.CreateResultRequest) (*models.Result, error) {
	// 检查重复
	isDuplicate, err := s.repo.CheckDuplicate(req.RepositoryURL, req.FilePath, req.SHA)
	if err != nil {
		return nil, err
	}
	
	if isDuplicate {
		return nil, nil // 重复结果，不创建
	}
	
	result := &models.Result{
		QueryID:        req.QueryID,
		RepositoryName: req.RepositoryName,
		RepositoryURL:  req.RepositoryURL,
		FilePath:       req.FilePath,
		FileURL:        req.FileURL,
		ContentSnippet: req.ContentSnippet,
		SHA:            req.SHA,
		Score:          req.Score,
		IsProcessed:    false,
	}
	
	if err := s.repo.Create(result); err != nil {
		return nil, err
	}
	
	return result, nil
}

// GetResults 获取结果列表
func (s *resultService) GetResults(filter *models.ResultFilter) ([]*models.ResultResponse, error) {
	return s.repo.GetWithQuery(filter)
}

// GetResult 获取单个结果
func (s *resultService) GetResult(id int) (*models.Result, error) {
	return s.repo.GetByID(id)
}

// MarkAsProcessed 标记为已处理
func (s *resultService) MarkAsProcessed(id int) error {
	return s.repo.MarkAsProcessed(id)
}

// DeleteResult 删除结果
func (s *resultService) DeleteResult(id int) error {
	return s.repo.Delete(id)
}

// GetStatistics 获取统计信息
func (s *resultService) GetStatistics() (*models.ResultStatistics, error) {
	return s.repo.GetStatistics()
}
