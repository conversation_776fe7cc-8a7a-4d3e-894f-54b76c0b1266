package services

import (
	"database/sql"

	"godeye/internal/config"
	"godeye/internal/models"
)

// Services 服务集合
type Services struct {
	Auth      AuthService
	Github    GithubService
	Query     QueryService
	Result    ResultService
	Notice    NoticeService
	Search    SearchService
	Blacklist BlacklistService
}

// New 创建服务集合
func New(db *sql.DB, cfg *config.Config) *Services {
	// 创建仓库
	userRepo := models.NewUserRepository(db)
	githubRepo := models.NewGithubAccountRepository(db)
	queryRepo := models.NewQueryRepository(db)
	resultRepo := models.NewResultRepository(db)
	
	// 创建服务
	authService := NewAuthService(userRepo, cfg.SecretKey)
	githubService := NewGithubService(githubRepo)
	queryService := NewQueryService(queryRepo)
	resultService := NewResultService(resultRepo)
	noticeService := NewNoticeService(db)
	blacklistService := NewBlacklistService(db)
	searchService := NewSearchService(githubService, queryService, resultService, blacklistService)

	return &Services{
		Auth:      authService,
		Github:    githubService,
		Query:     queryService,
		Result:    resultService,
		Notice:    noticeService,
		Search:    searchService,
		Blacklist: blacklistService,
	}
}
