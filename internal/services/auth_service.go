package services

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"

	"godeye/internal/models"
)

// AuthService 认证服务接口
type AuthService interface {
	Login(username, password string) (*models.UserResponse, string, error)
	Register(req *models.CreateUserRequest) (*models.UserResponse, error)
	ValidateToken(tokenString string) (*models.User, error)
	GetUserByID(id int) (*models.User, error)
}

// authService 认证服务实现
type authService struct {
	userRepo  models.UserRepository
	secretKey string
}

// NewAuthService 创建认证服务
func NewAuthService(userRepo models.UserRepository, secretKey string) AuthService {
	return &authService{
		userRepo:  userRepo,
		secretKey: secretKey,
	}
}

// JWTClaims JWT声明
type JWTClaims struct {
	UserID   int    `json:"user_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// Login 用户登录
func (s *authService) Login(username, password string) (*models.UserResponse, string, error) {
	// 获取用户
	user, err := s.userRepo.GetByUsername(username)
	if err != nil {
		return nil, "", err
	}
	
	if user == nil {
		return nil, "", errors.New("用户不存在")
	}
	
	if !user.IsActive {
		return nil, "", errors.New("用户已被禁用")
	}
	
	// 验证密码
	if !models.CheckPassword(password, user.PasswordHash) {
		return nil, "", errors.New("密码错误")
	}
	
	// 更新最后登录时间
	if err := s.userRepo.UpdateLastLogin(user.ID); err != nil {
		// 记录错误但不影响登录
	}
	
	// 生成JWT token
	token, err := s.generateToken(user)
	if err != nil {
		return nil, "", err
	}
	
	return user.ToResponse(), token, nil
}

// Register 用户注册
func (s *authService) Register(req *models.CreateUserRequest) (*models.UserResponse, error) {
	// 检查用户是否已存在
	existingUser, err := s.userRepo.GetByUsername(req.Username)
	if err != nil {
		return nil, err
	}
	
	if existingUser != nil {
		return nil, errors.New("用户名已存在")
	}
	
	// 哈希密码
	hashedPassword, err := models.HashPassword(req.Password)
	if err != nil {
		return nil, err
	}
	
	// 创建用户
	user := &models.User{
		Username:     req.Username,
		PasswordHash: hashedPassword,
		Email:        req.Email,
		IsActive:     true,
	}
	
	if err := s.userRepo.Create(user); err != nil {
		return nil, err
	}
	
	return user.ToResponse(), nil
}

// ValidateToken 验证JWT token
func (s *authService) ValidateToken(tokenString string) (*models.User, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.secretKey), nil
	})
	
	if err != nil {
		return nil, err
	}
	
	if !token.Valid {
		return nil, errors.New("无效的token")
	}
	
	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return nil, errors.New("无效的token声明")
	}
	
	// 获取用户信息
	user, err := s.userRepo.GetByID(claims.UserID)
	if err != nil {
		return nil, err
	}
	
	if user == nil {
		return nil, errors.New("用户不存在")
	}
	
	if !user.IsActive {
		return nil, errors.New("用户已被禁用")
	}
	
	return user, nil
}

// GetUserByID 根据ID获取用户
func (s *authService) GetUserByID(id int) (*models.User, error) {
	return s.userRepo.GetByID(id)
}

// generateToken 生成JWT token
func (s *authService) generateToken(user *models.User) (string, error) {
	claims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "godeye",
		},
	}
	
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.secretKey))
}
