package services

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"github.com/google/go-github/v56/github"
	"golang.org/x/oauth2"

	"godeye/internal/models"
)

// GithubService GitHub服务接口
type GithubService interface {
	CreateAccount(req *models.CreateGithubAccountRequest) (*models.GithubAccountResponse, error)
	GetAccounts() ([]*models.GithubAccountResponse, error)
	GetAccount(id int) (*models.GithubAccountResponse, error)
	UpdateAccount(id int, req *models.UpdateGithubAccountRequest) (*models.GithubAccountResponse, error)
	DeleteAccount(id int) error
	GetActiveAccount() (*models.GithubAccount, error)
	ValidateAccount(username, token string) error
	UpdateRateLimit(account *models.GithubAccount, client *github.Client) error
	SearchCode(query string, account *models.GithubAccount) ([]*github.CodeResult, error)
	SearchRepositories(query string, account *models.GithubAccount) ([]*github.Repository, error)
}

// githubService GitHub服务实现
type githubService struct {
	repo models.GithubAccountRepository
}

// NewGithubService 创建GitHub服务
func NewGithubService(repo models.GithubAccountRepository) GithubService {
	return &githubService{repo: repo}
}

// CreateAccount 创建GitHub账号
func (s *githubService) CreateAccount(req *models.CreateGithubAccountRequest) (*models.GithubAccountResponse, error) {
	// 验证账号
	if err := s.ValidateAccount(req.Username, req.Token); err != nil {
		return nil, fmt.Errorf("账号验证失败: %w", err)
	}
	
	// 检查是否已存在
	existing, err := s.repo.GetByUsername(req.Username)
	if err != nil {
		return nil, err
	}
	
	if existing != nil {
		return nil, errors.New("账号已存在")
	}
	
	// 创建账号
	account := &models.GithubAccount{
		Username:      req.Username,
		Token:         req.Token,
		RateLimit:     5000,
		RateRemaining: 5000,
		IsActive:      true,
	}
	
	if err := s.repo.Create(account); err != nil {
		return nil, err
	}
	
	return account.ToResponse(false), nil
}

// GetAccounts 获取所有账号
func (s *githubService) GetAccounts() ([]*models.GithubAccountResponse, error) {
	accounts, err := s.repo.GetAll()
	if err != nil {
		return nil, err
	}
	
	var responses []*models.GithubAccountResponse
	for _, account := range accounts {
		responses = append(responses, account.ToResponse(false))
	}
	
	return responses, nil
}

// GetAccount 获取单个账号
func (s *githubService) GetAccount(id int) (*models.GithubAccountResponse, error) {
	account, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	if account == nil {
		return nil, errors.New("账号不存在")
	}
	
	return account.ToResponse(true), nil
}

// UpdateAccount 更新账号
func (s *githubService) UpdateAccount(id int, req *models.UpdateGithubAccountRequest) (*models.GithubAccountResponse, error) {
	account, err := s.repo.GetByID(id)
	if err != nil {
		return nil, err
	}
	
	if account == nil {
		return nil, errors.New("账号不存在")
	}
	
	// 更新字段
	if req.Username != "" {
		account.Username = req.Username
	}
	
	if req.Token != "" {
		// 验证新token
		if err := s.ValidateAccount(account.Username, req.Token); err != nil {
			return nil, fmt.Errorf("新token验证失败: %w", err)
		}
		account.Token = req.Token
	}
	
	if req.IsActive != nil {
		account.IsActive = *req.IsActive
	}
	
	if err := s.repo.Update(account); err != nil {
		return nil, err
	}
	
	return account.ToResponse(false), nil
}

// DeleteAccount 删除账号
func (s *githubService) DeleteAccount(id int) error {
	return s.repo.Delete(id)
}

// GetActiveAccount 获取可用的活跃账号
func (s *githubService) GetActiveAccount() (*models.GithubAccount, error) {
	accounts, err := s.repo.GetActive()
	if err != nil {
		return nil, err
	}
	
	if len(accounts) == 0 {
		return nil, errors.New("没有可用的GitHub账号")
	}
	
	// 过滤掉API限制用完的账号
	var availableAccounts []*models.GithubAccount
	for _, account := range accounts {
		if account.RateRemaining > 10 { // 保留一些余量
			availableAccounts = append(availableAccounts, account)
		}
	}
	
	if len(availableAccounts) == 0 {
		return nil, errors.New("所有GitHub账号API限制已用完")
	}
	
	// 随机选择一个账号（负载均衡）
	rand.Seed(time.Now().UnixNano())
	return availableAccounts[rand.Intn(len(availableAccounts))], nil
}

// ValidateAccount 验证GitHub账号
func (s *githubService) ValidateAccount(username, token string) error {
	ctx := context.Background()
	
	// 创建OAuth2 token
	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: token},
	)
	tc := oauth2.NewClient(ctx, ts)
	
	// 创建GitHub客户端
	client := github.NewClient(tc)
	
	// 获取用户信息验证token
	user, _, err := client.Users.Get(ctx, "")
	if err != nil {
		return fmt.Errorf("GitHub API调用失败: %w", err)
	}
	
	// 验证用户名是否匹配
	if user.GetLogin() != username {
		return errors.New("用户名与token不匹配")
	}
	
	return nil
}

// UpdateRateLimit 更新API限制信息
func (s *githubService) UpdateRateLimit(account *models.GithubAccount, client *github.Client) error {
	ctx := context.Background()
	
	// 获取API限制信息
	rateLimit, _, err := client.RateLimit.Get(ctx)
	if err != nil {
		return err
	}
	
	// 更新数据库
	return s.repo.UpdateRateLimit(
		account.ID,
		rateLimit.Core.Limit,
		rateLimit.Core.Remaining,
		rateLimit.Core.Reset.Time,
	)
}

// SearchCode 搜索代码
func (s *githubService) SearchCode(query string, account *models.GithubAccount) ([]*github.CodeResult, error) {
	ctx := context.Background()
	
	// 创建GitHub客户端
	client := s.createClient(account.Token)
	
	// 执行搜索
	opts := &github.SearchOptions{
		Sort:  "indexed",
		Order: "desc",
		ListOptions: github.ListOptions{
			PerPage: 100,
		},
	}
	
	result, _, err := client.Search.Code(ctx, query, opts)
	if err != nil {
		return nil, err
	}
	
	// 更新API限制
	if err := s.UpdateRateLimit(account, client); err != nil {
		// 记录错误但不影响搜索结果
	}
	
	return result.CodeResults, nil
}

// SearchRepositories 搜索仓库
func (s *githubService) SearchRepositories(query string, account *models.GithubAccount) ([]*github.Repository, error) {
	ctx := context.Background()
	
	// 创建GitHub客户端
	client := s.createClient(account.Token)
	
	// 执行搜索
	opts := &github.SearchOptions{
		Sort:  "updated",
		Order: "desc",
		ListOptions: github.ListOptions{
			PerPage: 100,
		},
	}
	
	result, _, err := client.Search.Repositories(ctx, query, opts)
	if err != nil {
		return nil, err
	}
	
	// 更新API限制
	if err := s.UpdateRateLimit(account, client); err != nil {
		// 记录错误但不影响搜索结果
	}
	
	return result.Repositories, nil
}

// createClient 创建GitHub客户端
func (s *githubService) createClient(token string) *github.Client {
	ctx := context.Background()
	
	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: token},
	)
	tc := oauth2.NewClient(ctx, ts)
	
	return github.NewClient(tc)
}
