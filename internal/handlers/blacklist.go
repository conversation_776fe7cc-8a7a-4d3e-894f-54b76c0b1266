package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"godeye/internal/models"
	"godeye/internal/services"
)

// BlacklistHandler 黑名单处理器
type BlacklistHandler struct {
	blacklistService services.BlacklistService
}

// NewBlacklistHandler 创建黑名单处理器
func NewBlacklistHandler(blacklistService services.BlacklistService) *BlacklistHandler {
	return &BlacklistHandler{
		blacklistService: blacklistService,
	}
}

// GetBlacklist 获取黑名单列表
func (h *BlacklistHandler) GetBlacklist(c *gin.Context) {
	itemType := c.Query("type") // repository 或 file
	
	var items []*models.BlacklistItem
	var err error
	
	if itemType != "" {
		items, err = h.blacklistService.GetBlacklistByType(itemType)
	} else {
		items, err = h.blacklistService.GetBlacklist()
	}
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "获取黑名单失败",
			"data": nil,
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "success",
		"data": items,
	})
}

// CreateBlacklistItem 创建黑名单项
func (h *BlacklistHandler) CreateBlacklistItem(c *gin.Context) {
	var req models.CreateBlacklistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	// 从上下文获取用户信息
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": 401,
			"msg":  "用户未认证",
			"data": nil,
		})
		return
	}
	
	user, ok := userInterface.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "用户信息错误",
			"data": nil,
		})
		return
	}

	item, err := h.blacklistService.CreateBlacklistItem(&req, user.Username)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "创建黑名单项失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "创建成功",
		"data": item,
	})
}

// UpdateBlacklistItem 更新黑名单项
func (h *BlacklistHandler) UpdateBlacklistItem(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的ID",
			"data": nil,
		})
		return
	}

	var req models.UpdateBlacklistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	item, err := h.blacklistService.UpdateBlacklistItem(id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "更新黑名单项失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "更新成功",
		"data": item,
	})
}

// DeleteBlacklistItem 删除黑名单项
func (h *BlacklistHandler) DeleteBlacklistItem(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的ID",
			"data": nil,
		})
		return
	}

	if err := h.blacklistService.DeleteBlacklistItem(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "删除黑名单项失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
		"data": nil,
	})
}

// AddRepositoryToBlacklist 添加仓库到黑名单
func (h *BlacklistHandler) AddRepositoryToBlacklist(c *gin.Context) {
	var req struct {
		Repository string `json:"repository" binding:"required"`
		Reason     string `json:"reason"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	// 从上下文获取用户信息
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": 401,
			"msg":  "用户未认证",
			"data": nil,
		})
		return
	}
	
	user, ok := userInterface.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "用户信息错误",
			"data": nil,
		})
		return
	}

	if err := h.blacklistService.AddRepositoryToBlacklist(req.Repository, req.Reason, user.Username); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "添加仓库到黑名单失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "添加成功",
		"data": nil,
	})
}

// AddFileToBlacklist 添加文件到黑名单
func (h *BlacklistHandler) AddFileToBlacklist(c *gin.Context) {
	var req struct {
		FilePath string `json:"file_path" binding:"required"`
		Reason   string `json:"reason"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	// 从上下文获取用户信息
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": 401,
			"msg":  "用户未认证",
			"data": nil,
		})
		return
	}
	
	user, ok := userInterface.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "用户信息错误",
			"data": nil,
		})
		return
	}

	if err := h.blacklistService.AddFileToBlacklist(req.FilePath, req.Reason, user.Username); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "添加文件到黑名单失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "添加成功",
		"data": nil,
	})
}

// CheckBlacklist 检查黑名单状态
func (h *BlacklistHandler) CheckBlacklist(c *gin.Context) {
	repository := c.Query("repository")
	filePath := c.Query("file_path")

	if repository == "" && filePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请提供仓库名或文件路径",
			"data": nil,
		})
		return
	}

	result := map[string]interface{}{
		"repository_blacklisted": false,
		"file_blacklisted":       false,
		"should_skip":            false,
		"reason":                 "",
	}

	if repository != "" {
		if blacklisted, err := h.blacklistService.IsRepositoryBlacklisted(repository); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code": 500,
				"msg":  "检查仓库黑名单失败: " + err.Error(),
				"data": nil,
			})
			return
		} else {
			result["repository_blacklisted"] = blacklisted
		}
	}

	if filePath != "" {
		if blacklisted, err := h.blacklistService.IsFileBlacklisted(filePath); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code": 500,
				"msg":  "检查文件黑名单失败: " + err.Error(),
				"data": nil,
			})
			return
		} else {
			result["file_blacklisted"] = blacklisted
		}
	}

	if repository != "" && filePath != "" {
		if shouldSkip, reason, err := h.blacklistService.ShouldSkipResult(repository, filePath); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code": 500,
				"msg":  "检查黑名单状态失败: " + err.Error(),
				"data": nil,
			})
			return
		} else {
			result["should_skip"] = shouldSkip
			result["reason"] = reason
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "success",
		"data": result,
	})
}
