package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"godeye/internal/models"
)

// GetGithubAccounts 获取GitHub账号列表
func (h *Handlers) GetGithubAccounts(c *gin.Context) {
	accounts, err := h.services.Github.GetAccounts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": 500,
			"msg":    "获取账号列表失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status": 200,
		"msg":    "获取账号列表成功",
		"result": accounts,
	})
}

// CreateGithubAccount 创建GitHub账号
func (h *Handlers) CreateGithubAccount(c *gin.Context) {
	var req models.CreateGithubAccountRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    "请求参数错误: " + err.Error(),
		})
		return
	}
	
	account, err := h.services.Github.CreateAccount(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"status": 201,
		"msg":    "创建账号成功",
		"result": account,
	})
}

// UpdateGithubAccount 更新GitHub账号
func (h *Handlers) UpdateGithubAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    "无效的账号ID",
		})
		return
	}
	
	var req models.UpdateGithubAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    "请求参数错误: " + err.Error(),
		})
		return
	}
	
	account, err := h.services.Github.UpdateAccount(id, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status": 200,
		"msg":    "更新账号成功",
		"result": account,
	})
}

// DeleteGithubAccount 删除GitHub账号
func (h *Handlers) DeleteGithubAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    "无效的账号ID",
		})
		return
	}
	
	if err := h.services.Github.DeleteAccount(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": 500,
			"msg":    "删除账号失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status": 200,
		"msg":    "删除账号成功",
	})
}
