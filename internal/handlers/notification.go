package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"godeye/internal/models"
	"godeye/internal/services"
)

// NotificationHandler 通知处理器
type NotificationHandler struct {
	noticeService services.NoticeService
}

// NewNotificationHandler 创建通知处理器
func NewNotificationHandler(noticeService services.NoticeService) *NotificationHandler {
	return &NotificationHandler{
		noticeService: noticeService,
	}
}

// GetNoticeConfigs 获取通知配置列表
func (h *NotificationHandler) GetNoticeConfigs(c *gin.Context) {
	configs, err := h.noticeService.GetNoticeConfigs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "获取通知配置失败",
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "success",
		"data": configs,
	})
}

// CreateNoticeConfig 创建通知配置
func (h *NotificationHandler) CreateNoticeConfig(c *gin.Context) {
	var req models.CreateNoticeConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	config, err := h.noticeService.CreateNoticeConfig(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "创建通知配置失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "创建成功",
		"data": config,
	})
}

// UpdateNoticeConfig 更新通知配置
func (h *NotificationHandler) UpdateNoticeConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的配置ID",
			"data": nil,
		})
		return
	}

	var req models.UpdateNoticeConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	config, err := h.noticeService.UpdateNoticeConfig(id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "更新通知配置失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "更新成功",
		"data": config,
	})
}

// DeleteNoticeConfig 删除通知配置
func (h *NotificationHandler) DeleteNoticeConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的配置ID",
			"data": nil,
		})
		return
	}

	if err := h.noticeService.DeleteNoticeConfig(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "删除通知配置失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
		"data": nil,
	})
}

// TestNoticeConfig 测试通知配置
func (h *NotificationHandler) TestNoticeConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的配置ID",
			"data": nil,
		})
		return
	}

	// 获取配置
	configs, err := h.noticeService.GetNoticeConfigs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "获取通知配置失败",
			"data": nil,
		})
		return
	}

	var targetConfig *models.NoticeConfig
	for _, config := range configs {
		if config.ID == id {
			targetConfig = config
			break
		}
	}

	if targetConfig == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  "通知配置不存在",
			"data": nil,
		})
		return
	}

	// 测试通知
	if err := h.noticeService.TestNotification(targetConfig); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "测试通知失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "测试通知发送成功",
		"data": nil,
	})
}

// SendNotification 发送通知
func (h *NotificationHandler) SendNotification(c *gin.Context) {
	var req struct {
		ConfigID int    `json:"config_id" binding:"required"`
		Title    string `json:"title" binding:"required"`
		Content  string `json:"content" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	if err := h.noticeService.SendNotification(req.ConfigID, req.Title, req.Content); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "发送通知失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "通知发送成功",
		"data": nil,
	})
}

// SendToAllChannels 发送到所有通道
func (h *NotificationHandler) SendToAllChannels(c *gin.Context) {
	var req struct {
		Title   string `json:"title" binding:"required"`
		Content string `json:"content" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误: " + err.Error(),
			"data": nil,
		})
		return
	}

	if err := h.noticeService.SendToAllActiveChannels(req.Title, req.Content); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  "发送通知失败: " + err.Error(),
			"data": nil,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "通知发送成功",
		"data": nil,
	})
}

// GetNotificationTypes 获取支持的通知类型
func (h *NotificationHandler) GetNotificationTypes(c *gin.Context) {
	types := []map[string]interface{}{
		{
			"type":        "email",
			"name":        "邮件通知",
			"description": "通过SMTP发送邮件通知",
			"fields": []map[string]interface{}{
				{"name": "host", "label": "SMTP服务器", "type": "text", "required": true},
				{"name": "port", "label": "端口", "type": "number", "required": true, "default": 587},
				{"name": "username", "label": "用户名", "type": "text", "required": true},
				{"name": "password", "label": "密码", "type": "password", "required": true},
				{"name": "from", "label": "发件人", "type": "email", "required": true},
				{"name": "to", "label": "收件人", "type": "email", "required": true},
			},
		},
		{
			"type":        "wechat",
			"name":        "企业微信",
			"description": "通过企业微信机器人发送通知",
			"fields": []map[string]interface{}{
				{"name": "webhook_url", "label": "Webhook URL", "type": "url", "required": true},
			},
		},
		{
			"type":        "dingtalk",
			"name":        "钉钉",
			"description": "通过钉钉机器人发送通知",
			"fields": []map[string]interface{}{
				{"name": "webhook_url", "label": "Webhook URL", "type": "url", "required": true},
				{"name": "secret", "label": "加签密钥", "type": "text", "required": false},
			},
		},
		{
			"type":        "feishu",
			"name":        "飞书",
			"description": "通过飞书机器人发送通知",
			"fields": []map[string]interface{}{
				{"name": "webhook_url", "label": "Webhook URL", "type": "url", "required": true},
				{"name": "secret", "label": "签名校验", "type": "text", "required": false},
			},
		},
		{
			"type":        "slack",
			"name":        "Slack",
			"description": "通过Slack Webhook发送通知",
			"fields": []map[string]interface{}{
				{"name": "webhook_url", "label": "Webhook URL", "type": "url", "required": true},
			},
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "success",
		"data": types,
	})
}
