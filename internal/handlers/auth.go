package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"godeye/internal/middleware"
	"godeye/internal/models"
)

// Login 用户登录
func (h *Handlers) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    "请求参数错误: " + err.<PERSON>rror(),
		})
		return
	}
	
	user, token, err := h.services.Auth.Login(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status": 401,
			"msg":    err.<PERSON>rror(),
		})
		return
	}
	
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"status": 200,
		"msg":    "登录成功",
		"result": gin.H{
			"user":  user,
			"token": token,
		},
	})
}

// Logout 用户登出
func (h *Handlers) Logout(c *gin.Context) {
	// JWT是无状态的，客户端删除token即可
	c.J<PERSON>(http.StatusOK, gin.H{
		"status": 200,
		"msg":    "登出成功",
	})
}

// GetUserInfo 获取用户信息
func (h *Handlers) GetUserInfo(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status": 401,
			"msg":    "用户信息不存在",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status": 200,
		"msg":    "获取用户信息成功",
		"result": user.ToResponse(),
	})
}

// CheckAuth 检查认证状态
func (h *Handlers) CheckAuth(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"status": 401,
			"msg":    "未认证",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"status": 200,
		"msg":    "已认证",
		"result": gin.H{
			"authenticated": true,
			"user":          user.ToResponse(),
		},
	})
}

// Register 用户注册
func (h *Handlers) Register(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    "请求参数错误: " + err.Error(),
		})
		return
	}
	
	user, err := h.services.Auth.Register(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status": 400,
			"msg":    err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{
		"status": 201,
		"msg":    "注册成功",
		"result": user,
	})
}
