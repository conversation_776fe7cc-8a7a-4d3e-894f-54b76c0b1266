package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"godeye/internal/models"
	"godeye/internal/services"
)

// AuthRequired 认证中间件
func AuthRequired(authService services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"status": 401,
				"msg":    "缺少认证信息",
			})
			c.Abort()
			return
		}
		
		// 检查Bearer token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"status": 401,
				"msg":    "认证格式错误",
			})
			c.Abort()
			return
		}
		
		token := parts[1]
		
		// 验证token
		user, err := authService.ValidateToken(token)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"status": 401,
				"msg":    "认证失败: " + err.<PERSON>r(),
			})
			c.Abort()
			return
		}
		
		// 将用户信息存储到上下文
		c.Set("user", user)
		c.Set("user_id", user.ID)
		c.Set("username", user.Username)
		
		c.Next()
	}
}

// GetCurrentUser 从上下文获取当前用户
func GetCurrentUser(c *gin.Context) (*models.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	
	u, ok := user.(*models.User)
	return u, ok
}

// GetCurrentUserID 从上下文获取当前用户ID
func GetCurrentUserID(c *gin.Context) (int, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	
	id, ok := userID.(int)
	return id, ok
}
