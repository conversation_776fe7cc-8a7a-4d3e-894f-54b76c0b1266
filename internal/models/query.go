package models

import (
	"database/sql"
	"time"
)

// Query 查询关键词模型
type Query struct {
	ID          int       `json:"id" db:"id"`
	Keyword     string    `json:"keyword" db:"keyword"`
	Description string    `json:"description" db:"description"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// CreateQueryRequest 创建查询请求
type CreateQueryRequest struct {
	Keyword     string `json:"keyword" binding:"required"`
	Description string `json:"description"`
}

// UpdateQueryRequest 更新查询请求
type UpdateQueryRequest struct {
	Keyword     string `json:"keyword"`
	Description string `json:"description"`
	IsActive    *bool  `json:"is_active"`
}

// QueryRepository 查询仓库接口
type QueryRepository interface {
	Create(query *Query) error
	GetByID(id int) (*Query, error)
	GetAll() ([]*Query, error)
	GetActive() ([]*Query, error)
	Update(query *Query) error
	Delete(id int) error
}

// queryRepository 查询仓库实现
type queryRepository struct {
	db *sql.DB
}

// NewQueryRepository 创建查询仓库
func NewQueryRepository(db *sql.DB) QueryRepository {
	return &queryRepository{db: db}
}

// Create 创建查询
func (r *queryRepository) Create(query *Query) error {
	sql := `
		INSERT INTO queries (keyword, description, is_active)
		VALUES ($1, $2, $3)
		RETURNING id, created_at, updated_at`
	
	return r.db.QueryRow(sql, query.Keyword, query.Description, query.IsActive).
		Scan(&query.ID, &query.CreatedAt, &query.UpdatedAt)
}

// GetByID 根据ID获取查询
func (r *queryRepository) GetByID(id int) (*Query, error) {
	query := &Query{}
	sql := `
		SELECT id, keyword, description, is_active, created_at, updated_at
		FROM queries WHERE id = $1`
	
	err := r.db.QueryRow(sql, id).Scan(
		&query.ID, &query.Keyword, &query.Description, &query.IsActive,
		&query.CreatedAt, &query.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	
	return query, err
}

// GetAll 获取所有查询
func (r *queryRepository) GetAll() ([]*Query, error) {
	sql := `
		SELECT id, keyword, description, is_active, created_at, updated_at
		FROM queries ORDER BY created_at DESC`
	
	rows, err := r.db.Query(sql)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var queries []*Query
	for rows.Next() {
		query := &Query{}
		err := rows.Scan(
			&query.ID, &query.Keyword, &query.Description, &query.IsActive,
			&query.CreatedAt, &query.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		queries = append(queries, query)
	}
	
	return queries, rows.Err()
}

// GetActive 获取活跃的查询
func (r *queryRepository) GetActive() ([]*Query, error) {
	sql := `
		SELECT id, keyword, description, is_active, created_at, updated_at
		FROM queries WHERE is_active = true ORDER BY created_at DESC`
	
	rows, err := r.db.Query(sql)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var queries []*Query
	for rows.Next() {
		query := &Query{}
		err := rows.Scan(
			&query.ID, &query.Keyword, &query.Description, &query.IsActive,
			&query.CreatedAt, &query.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		queries = append(queries, query)
	}
	
	return queries, rows.Err()
}

// Update 更新查询
func (r *queryRepository) Update(query *Query) error {
	sql := `
		UPDATE queries 
		SET keyword = $1, description = $2, is_active = $3, updated_at = CURRENT_TIMESTAMP
		WHERE id = $4`
	
	_, err := r.db.Exec(sql, query.Keyword, query.Description, query.IsActive, query.ID)
	return err
}

// Delete 删除查询
func (r *queryRepository) Delete(id int) error {
	sql := `DELETE FROM queries WHERE id = $1`
	_, err := r.db.Exec(sql, id)
	return err
}
