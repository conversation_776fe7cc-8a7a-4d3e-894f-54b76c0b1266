package models

import (
	"database/sql"
	"time"
)

// GithubAccount GitHub账号模型
type GithubAccount struct {
	ID            int       `json:"id" db:"id"`
	Username      string    `json:"username" db:"username"`
	Token         string    `json:"token" db:"token"`
	RateLimit     int       `json:"rate_limit" db:"rate_limit"`
	RateRemaining int       `json:"rate_remaining" db:"rate_remaining"`
	RateReset     *time.Time `json:"rate_reset" db:"rate_reset"`
	IsActive      bool      `json:"is_active" db:"is_active"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
}

// CreateGithubAccountRequest 创建GitHub账号请求
type CreateGithubAccountRequest struct {
	Username string `json:"username" binding:"required"`
	Token    string `json:"token" binding:"required"`
}

// UpdateGithubAccountRequest 更新GitHub账号请求
type UpdateGithubAccountRequest struct {
	Username string `json:"username"`
	Token    string `json:"token"`
	IsActive *bool  `json:"is_active"`
}

// GithubAccountResponse GitHub账号响应
type GithubAccountResponse struct {
	ID            int       `json:"id"`
	Username      string    `json:"username"`
	Token         string    `json:"token,omitempty"` // 可选择性返回token
	RateLimit     int       `json:"rate_limit"`
	RateRemaining int       `json:"rate_remaining"`
	RateReset     *time.Time `json:"rate_reset"`
	IsActive      bool      `json:"is_active"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// GithubAccountRepository GitHub账号仓库接口
type GithubAccountRepository interface {
	Create(account *GithubAccount) error
	GetByID(id int) (*GithubAccount, error)
	GetByUsername(username string) (*GithubAccount, error)
	GetAll() ([]*GithubAccount, error)
	GetActive() ([]*GithubAccount, error)
	Update(account *GithubAccount) error
	UpdateRateLimit(id int, limit, remaining int, reset time.Time) error
	Delete(id int) error
}

// githubAccountRepository GitHub账号仓库实现
type githubAccountRepository struct {
	db *sql.DB
}

// NewGithubAccountRepository 创建GitHub账号仓库
func NewGithubAccountRepository(db *sql.DB) GithubAccountRepository {
	return &githubAccountRepository{db: db}
}

// Create 创建GitHub账号
func (r *githubAccountRepository) Create(account *GithubAccount) error {
	query := `
		INSERT INTO github_accounts (username, token, rate_limit, rate_remaining, is_active)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id, created_at, updated_at`
	
	return r.db.QueryRow(query, account.Username, account.Token, 
		account.RateLimit, account.RateRemaining, account.IsActive).
		Scan(&account.ID, &account.CreatedAt, &account.UpdatedAt)
}

// GetByID 根据ID获取GitHub账号
func (r *githubAccountRepository) GetByID(id int) (*GithubAccount, error) {
	account := &GithubAccount{}
	query := `
		SELECT id, username, token, rate_limit, rate_remaining, rate_reset, 
		       is_active, created_at, updated_at
		FROM github_accounts WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&account.ID, &account.Username, &account.Token, &account.RateLimit,
		&account.RateRemaining, &account.RateReset, &account.IsActive,
		&account.CreatedAt, &account.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	
	return account, err
}

// GetByUsername 根据用户名获取GitHub账号
func (r *githubAccountRepository) GetByUsername(username string) (*GithubAccount, error) {
	account := &GithubAccount{}
	query := `
		SELECT id, username, token, rate_limit, rate_remaining, rate_reset, 
		       is_active, created_at, updated_at
		FROM github_accounts WHERE username = $1`
	
	err := r.db.QueryRow(query, username).Scan(
		&account.ID, &account.Username, &account.Token, &account.RateLimit,
		&account.RateRemaining, &account.RateReset, &account.IsActive,
		&account.CreatedAt, &account.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	
	return account, err
}

// GetAll 获取所有GitHub账号
func (r *githubAccountRepository) GetAll() ([]*GithubAccount, error) {
	query := `
		SELECT id, username, token, rate_limit, rate_remaining, rate_reset, 
		       is_active, created_at, updated_at
		FROM github_accounts ORDER BY created_at DESC`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var accounts []*GithubAccount
	for rows.Next() {
		account := &GithubAccount{}
		err := rows.Scan(
			&account.ID, &account.Username, &account.Token, &account.RateLimit,
			&account.RateRemaining, &account.RateReset, &account.IsActive,
			&account.CreatedAt, &account.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		accounts = append(accounts, account)
	}
	
	return accounts, rows.Err()
}

// GetActive 获取活跃的GitHub账号
func (r *githubAccountRepository) GetActive() ([]*GithubAccount, error) {
	query := `
		SELECT id, username, token, rate_limit, rate_remaining, rate_reset, 
		       is_active, created_at, updated_at
		FROM github_accounts WHERE is_active = true ORDER BY rate_remaining DESC`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var accounts []*GithubAccount
	for rows.Next() {
		account := &GithubAccount{}
		err := rows.Scan(
			&account.ID, &account.Username, &account.Token, &account.RateLimit,
			&account.RateRemaining, &account.RateReset, &account.IsActive,
			&account.CreatedAt, &account.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		accounts = append(accounts, account)
	}
	
	return accounts, rows.Err()
}

// Update 更新GitHub账号
func (r *githubAccountRepository) Update(account *GithubAccount) error {
	query := `
		UPDATE github_accounts 
		SET username = $1, token = $2, rate_limit = $3, rate_remaining = $4, 
		    rate_reset = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
		WHERE id = $7`
	
	_, err := r.db.Exec(query, account.Username, account.Token, account.RateLimit,
		account.RateRemaining, account.RateReset, account.IsActive, account.ID)
	return err
}

// UpdateRateLimit 更新API限制信息
func (r *githubAccountRepository) UpdateRateLimit(id int, limit, remaining int, reset time.Time) error {
	query := `
		UPDATE github_accounts 
		SET rate_limit = $1, rate_remaining = $2, rate_reset = $3, updated_at = CURRENT_TIMESTAMP
		WHERE id = $4`
	
	_, err := r.db.Exec(query, limit, remaining, reset, id)
	return err
}

// Delete 删除GitHub账号
func (r *githubAccountRepository) Delete(id int) error {
	query := `DELETE FROM github_accounts WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

// ToResponse 转换为响应格式
func (g *GithubAccount) ToResponse(includeToken bool) *GithubAccountResponse {
	resp := &GithubAccountResponse{
		ID:            g.ID,
		Username:      g.Username,
		RateLimit:     g.RateLimit,
		RateRemaining: g.RateRemaining,
		RateReset:     g.RateReset,
		IsActive:      g.IsActive,
		CreatedAt:     g.CreatedAt,
		UpdatedAt:     g.UpdatedAt,
	}
	
	if includeToken {
		resp.Token = g.Token
	}
	
	return resp
}
