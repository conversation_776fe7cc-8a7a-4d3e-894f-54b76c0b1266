package models

import (
	"database/sql"
	"time"
)

// NoticeConfig 通知配置模型
type NoticeConfig struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Type        string    `json:"type" db:"type"`
	Config      string    `json:"config" db:"config"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	Description string    `json:"description" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// CreateNoticeConfigRequest 创建通知配置请求
type CreateNoticeConfigRequest struct {
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required"`
	Config      string `json:"config" binding:"required"`
	IsActive    bool   `json:"is_active"`
	Description string `json:"description"`
}

// UpdateNoticeConfigRequest 更新通知配置请求
type UpdateNoticeConfigRequest struct {
	Name        string `json:"name"`
	Config      string `json:"config"`
	IsActive    *bool  `json:"is_active"`
	Description string `json:"description"`
}

// 各种通知配置结构

// EmailConfig 邮件配置
type EmailConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	To       string `json:"to"`
}

// WechatConfig 企业微信配置
type WechatConfig struct {
	WebhookURL string `json:"webhook_url"`
}

// DingtalkConfig 钉钉配置
type DingtalkConfig struct {
	WebhookURL string `json:"webhook_url"`
	Secret     string `json:"secret,omitempty"`
}

// FeishuConfig 飞书配置
type FeishuConfig struct {
	WebhookURL string `json:"webhook_url"`
	Secret     string `json:"secret,omitempty"`
}

// SlackConfig Slack配置
type SlackConfig struct {
	WebhookURL string `json:"webhook_url"`
}

// NoticeConfigRepository 通知配置仓库接口
type NoticeConfigRepository interface {
	Create(config *NoticeConfig) error
	GetByID(id int) (*NoticeConfig, error)
	GetAll() ([]*NoticeConfig, error)
	GetActive() ([]*NoticeConfig, error)
	GetByType(noticeType string) ([]*NoticeConfig, error)
	Update(config *NoticeConfig) error
	Delete(id int) error
}

// noticeConfigRepository 通知配置仓库实现
type noticeConfigRepository struct {
	db *sql.DB
}

// NewNoticeConfigRepository 创建通知配置仓库
func NewNoticeConfigRepository(db *sql.DB) NoticeConfigRepository {
	return &noticeConfigRepository{db: db}
}

// Create 创建通知配置
func (r *noticeConfigRepository) Create(config *NoticeConfig) error {
	query := `
		INSERT INTO notice_configs (name, type, config, is_active, description)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id, created_at, updated_at`
	
	return r.db.QueryRow(query, config.Name, config.Type, config.Config, 
		config.IsActive, config.Description).
		Scan(&config.ID, &config.CreatedAt, &config.UpdatedAt)
}

// GetByID 根据ID获取通知配置
func (r *noticeConfigRepository) GetByID(id int) (*NoticeConfig, error) {
	config := &NoticeConfig{}
	query := `
		SELECT id, name, type, config, is_active, description, created_at, updated_at
		FROM notice_configs WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&config.ID, &config.Name, &config.Type, &config.Config,
		&config.IsActive, &config.Description, &config.CreatedAt, &config.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	
	return config, err
}

// GetAll 获取所有通知配置
func (r *noticeConfigRepository) GetAll() ([]*NoticeConfig, error) {
	query := `
		SELECT id, name, type, config, is_active, description, created_at, updated_at
		FROM notice_configs ORDER BY created_at DESC`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var configs []*NoticeConfig
	for rows.Next() {
		config := &NoticeConfig{}
		err := rows.Scan(
			&config.ID, &config.Name, &config.Type, &config.Config,
			&config.IsActive, &config.Description, &config.CreatedAt, &config.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		configs = append(configs, config)
	}
	
	return configs, rows.Err()
}

// GetActive 获取活跃的通知配置
func (r *noticeConfigRepository) GetActive() ([]*NoticeConfig, error) {
	query := `
		SELECT id, name, type, config, is_active, description, created_at, updated_at
		FROM notice_configs WHERE is_active = true ORDER BY created_at DESC`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var configs []*NoticeConfig
	for rows.Next() {
		config := &NoticeConfig{}
		err := rows.Scan(
			&config.ID, &config.Name, &config.Type, &config.Config,
			&config.IsActive, &config.Description, &config.CreatedAt, &config.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		configs = append(configs, config)
	}
	
	return configs, rows.Err()
}

// GetByType 根据类型获取通知配置
func (r *noticeConfigRepository) GetByType(noticeType string) ([]*NoticeConfig, error) {
	query := `
		SELECT id, name, type, config, is_active, description, created_at, updated_at
		FROM notice_configs WHERE type = $1 ORDER BY created_at DESC`
	
	rows, err := r.db.Query(query, noticeType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var configs []*NoticeConfig
	for rows.Next() {
		config := &NoticeConfig{}
		err := rows.Scan(
			&config.ID, &config.Name, &config.Type, &config.Config,
			&config.IsActive, &config.Description, &config.CreatedAt, &config.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		configs = append(configs, config)
	}
	
	return configs, rows.Err()
}

// Update 更新通知配置
func (r *noticeConfigRepository) Update(config *NoticeConfig) error {
	query := `
		UPDATE notice_configs 
		SET name = $1, type = $2, config = $3, is_active = $4, description = $5, updated_at = CURRENT_TIMESTAMP
		WHERE id = $6`
	
	_, err := r.db.Exec(query, config.Name, config.Type, config.Config,
		config.IsActive, config.Description, config.ID)
	return err
}

// Delete 删除通知配置
func (r *noticeConfigRepository) Delete(id int) error {
	query := `DELETE FROM notice_configs WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}
