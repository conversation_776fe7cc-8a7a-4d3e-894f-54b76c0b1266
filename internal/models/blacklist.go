package models

import (
	"database/sql"
	"time"
)

// BlacklistItem 黑名单项模型
type BlacklistItem struct {
	ID          int       `json:"id" db:"id"`
	Type        string    `json:"type" db:"type"`         // "repository" 或 "file"
	Value       string    `json:"value" db:"value"`       // 仓库名或文件路径
	Reason      string    `json:"reason" db:"reason"`     // 加入黑名单的原因
	CreatedBy   string    `json:"created_by" db:"created_by"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// CreateBlacklistRequest 创建黑名单请求
type CreateBlacklistRequest struct {
	Type   string `json:"type" binding:"required"`   // "repository" 或 "file"
	Value  string `json:"value" binding:"required"`  // 仓库名或文件路径
	Reason string `json:"reason"`                    // 原因
}

// UpdateBlacklistRequest 更新黑名单请求
type UpdateBlacklistRequest struct {
	Reason string `json:"reason"`
}

// BlacklistRepository 黑名单仓库接口
type BlacklistRepository interface {
	Create(item *BlacklistItem) error
	GetByID(id int) (*BlacklistItem, error)
	GetAll() ([]*BlacklistItem, error)
	GetByType(itemType string) ([]*BlacklistItem, error)
	Update(item *BlacklistItem) error
	Delete(id int) error
	IsBlacklisted(itemType, value string) (bool, error)
	GetRepositoryBlacklist() ([]string, error)
	GetFileBlacklist() ([]string, error)
}

// blacklistRepository 黑名单仓库实现
type blacklistRepository struct {
	db *sql.DB
}

// NewBlacklistRepository 创建黑名单仓库
func NewBlacklistRepository(db *sql.DB) BlacklistRepository {
	return &blacklistRepository{db: db}
}

// Create 创建黑名单项
func (r *blacklistRepository) Create(item *BlacklistItem) error {
	query := `
		INSERT INTO blacklist (type, value, reason, created_by)
		VALUES ($1, $2, $3, $4)
		RETURNING id, created_at, updated_at`
	
	return r.db.QueryRow(query, item.Type, item.Value, item.Reason, item.CreatedBy).
		Scan(&item.ID, &item.CreatedAt, &item.UpdatedAt)
}

// GetByID 根据ID获取黑名单项
func (r *blacklistRepository) GetByID(id int) (*BlacklistItem, error) {
	item := &BlacklistItem{}
	query := `
		SELECT id, type, value, reason, created_by, created_at, updated_at
		FROM blacklist WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&item.ID, &item.Type, &item.Value, &item.Reason,
		&item.CreatedBy, &item.CreatedAt, &item.UpdatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	
	return item, err
}

// GetAll 获取所有黑名单项
func (r *blacklistRepository) GetAll() ([]*BlacklistItem, error) {
	query := `
		SELECT id, type, value, reason, created_by, created_at, updated_at
		FROM blacklist ORDER BY created_at DESC`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var items []*BlacklistItem
	for rows.Next() {
		item := &BlacklistItem{}
		err := rows.Scan(
			&item.ID, &item.Type, &item.Value, &item.Reason,
			&item.CreatedBy, &item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		items = append(items, item)
	}
	
	return items, rows.Err()
}

// GetByType 根据类型获取黑名单项
func (r *blacklistRepository) GetByType(itemType string) ([]*BlacklistItem, error) {
	query := `
		SELECT id, type, value, reason, created_by, created_at, updated_at
		FROM blacklist WHERE type = $1 ORDER BY created_at DESC`
	
	rows, err := r.db.Query(query, itemType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var items []*BlacklistItem
	for rows.Next() {
		item := &BlacklistItem{}
		err := rows.Scan(
			&item.ID, &item.Type, &item.Value, &item.Reason,
			&item.CreatedBy, &item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		items = append(items, item)
	}
	
	return items, rows.Err()
}

// Update 更新黑名单项
func (r *blacklistRepository) Update(item *BlacklistItem) error {
	query := `
		UPDATE blacklist 
		SET reason = $1, updated_at = CURRENT_TIMESTAMP
		WHERE id = $2`
	
	_, err := r.db.Exec(query, item.Reason, item.ID)
	return err
}

// Delete 删除黑名单项
func (r *blacklistRepository) Delete(id int) error {
	query := `DELETE FROM blacklist WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

// IsBlacklisted 检查是否在黑名单中
func (r *blacklistRepository) IsBlacklisted(itemType, value string) (bool, error) {
	query := `SELECT COUNT(*) FROM blacklist WHERE type = $1 AND value = $2`
	
	var count int
	err := r.db.QueryRow(query, itemType, value).Scan(&count)
	if err != nil {
		return false, err
	}
	
	return count > 0, nil
}

// GetRepositoryBlacklist 获取仓库黑名单列表
func (r *blacklistRepository) GetRepositoryBlacklist() ([]string, error) {
	query := `SELECT value FROM blacklist WHERE type = 'repository'`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var repositories []string
	for rows.Next() {
		var repo string
		if err := rows.Scan(&repo); err != nil {
			return nil, err
		}
		repositories = append(repositories, repo)
	}
	
	return repositories, rows.Err()
}

// GetFileBlacklist 获取文件黑名单列表
func (r *blacklistRepository) GetFileBlacklist() ([]string, error) {
	query := `SELECT value FROM blacklist WHERE type = 'file'`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var files []string
	for rows.Next() {
		var file string
		if err := rows.Scan(&file); err != nil {
			return nil, err
		}
		files = append(files, file)
	}
	
	return files, rows.Err()
}
