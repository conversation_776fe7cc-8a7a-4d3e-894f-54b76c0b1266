package models

import (
	"database/sql"
	"time"
)

// Result 搜索结果模型
type Result struct {
	ID              int       `json:"id" db:"id"`
	QueryID         int       `json:"query_id" db:"query_id"`
	RepositoryName  string    `json:"repository_name" db:"repository_name"`
	RepositoryURL   string    `json:"repository_url" db:"repository_url"`
	FilePath        string    `json:"file_path" db:"file_path"`
	FileURL         string    `json:"file_url" db:"file_url"`
	ContentSnippet  string    `json:"content_snippet" db:"content_snippet"`
	SHA             string    `json:"sha" db:"sha"`
	Score           float64   `json:"score" db:"score"`
	IsProcessed     bool      `json:"is_processed" db:"is_processed"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
}

// CreateResultRequest 创建结果请求
type CreateResultRequest struct {
	QueryID        int     `json:"query_id" binding:"required"`
	RepositoryName string  `json:"repository_name" binding:"required"`
	RepositoryURL  string  `json:"repository_url" binding:"required"`
	FilePath       string  `json:"file_path"`
	FileURL        string  `json:"file_url"`
	ContentSnippet string  `json:"content_snippet"`
	SHA            string  `json:"sha"`
	Score          float64 `json:"score"`
}

// ResultResponse 结果响应
type ResultResponse struct {
	ID              int       `json:"id"`
	QueryID         int       `json:"query_id"`
	QueryKeyword    string    `json:"query_keyword,omitempty"`
	RepositoryName  string    `json:"repository_name"`
	RepositoryURL   string    `json:"repository_url"`
	FilePath        string    `json:"file_path"`
	FileURL         string    `json:"file_url"`
	ContentSnippet  string    `json:"content_snippet"`
	SHA             string    `json:"sha"`
	Score           float64   `json:"score"`
	IsProcessed     bool      `json:"is_processed"`
	CreatedAt       time.Time `json:"created_at"`
}

// ResultFilter 结果过滤器
type ResultFilter struct {
	QueryID     *int    `json:"query_id"`
	IsProcessed *bool   `json:"is_processed"`
	MinScore    *float64 `json:"min_score"`
	MaxScore    *float64 `json:"max_score"`
	Limit       int     `json:"limit"`
	Offset      int     `json:"offset"`
}

// ResultRepository 结果仓库接口
type ResultRepository interface {
	Create(result *Result) error
	GetByID(id int) (*Result, error)
	GetAll(filter *ResultFilter) ([]*Result, error)
	GetWithQuery(filter *ResultFilter) ([]*ResultResponse, error)
	Update(result *Result) error
	MarkAsProcessed(id int) error
	Delete(id int) error
	GetStatistics() (*ResultStatistics, error)
	CheckDuplicate(repositoryURL, filePath, sha string) (bool, error)
}

// ResultStatistics 结果统计
type ResultStatistics struct {
	TotalResults     int `json:"total_results"`
	ProcessedResults int `json:"processed_results"`
	TodayResults     int `json:"today_results"`
	WeekResults      int `json:"week_results"`
}

// resultRepository 结果仓库实现
type resultRepository struct {
	db *sql.DB
}

// NewResultRepository 创建结果仓库
func NewResultRepository(db *sql.DB) ResultRepository {
	return &resultRepository{db: db}
}

// Create 创建结果
func (r *resultRepository) Create(result *Result) error {
	query := `
		INSERT INTO results (query_id, repository_name, repository_url, file_path, 
		                    file_url, content_snippet, sha, score, is_processed)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id, created_at`
	
	return r.db.QueryRow(query, result.QueryID, result.RepositoryName, result.RepositoryURL,
		result.FilePath, result.FileURL, result.ContentSnippet, result.SHA, result.Score, result.IsProcessed).
		Scan(&result.ID, &result.CreatedAt)
}

// GetByID 根据ID获取结果
func (r *resultRepository) GetByID(id int) (*Result, error) {
	result := &Result{}
	query := `
		SELECT id, query_id, repository_name, repository_url, file_path, file_url,
		       content_snippet, sha, score, is_processed, created_at
		FROM results WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&result.ID, &result.QueryID, &result.RepositoryName, &result.RepositoryURL,
		&result.FilePath, &result.FileURL, &result.ContentSnippet, &result.SHA,
		&result.Score, &result.IsProcessed, &result.CreatedAt,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	
	return result, err
}

// GetAll 获取所有结果
func (r *resultRepository) GetAll(filter *ResultFilter) ([]*Result, error) {
	query := `
		SELECT id, query_id, repository_name, repository_url, file_path, file_url,
		       content_snippet, sha, score, is_processed, created_at
		FROM results WHERE 1=1`
	
	args := []interface{}{}
	argIndex := 1
	
	if filter.QueryID != nil {
		query += ` AND query_id = $` + string(rune(argIndex+'0'))
		args = append(args, *filter.QueryID)
		argIndex++
	}
	
	if filter.IsProcessed != nil {
		query += ` AND is_processed = $` + string(rune(argIndex+'0'))
		args = append(args, *filter.IsProcessed)
		argIndex++
	}
	
	if filter.MinScore != nil {
		query += ` AND score >= $` + string(rune(argIndex+'0'))
		args = append(args, *filter.MinScore)
		argIndex++
	}
	
	if filter.MaxScore != nil {
		query += ` AND score <= $` + string(rune(argIndex+'0'))
		args = append(args, *filter.MaxScore)
		argIndex++
	}
	
	query += ` ORDER BY created_at DESC`
	
	if filter.Limit > 0 {
		query += ` LIMIT $` + string(rune(argIndex+'0'))
		args = append(args, filter.Limit)
		argIndex++
	}
	
	if filter.Offset > 0 {
		query += ` OFFSET $` + string(rune(argIndex+'0'))
		args = append(args, filter.Offset)
	}
	
	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var results []*Result
	for rows.Next() {
		result := &Result{}
		err := rows.Scan(
			&result.ID, &result.QueryID, &result.RepositoryName, &result.RepositoryURL,
			&result.FilePath, &result.FileURL, &result.ContentSnippet, &result.SHA,
			&result.Score, &result.IsProcessed, &result.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		results = append(results, result)
	}
	
	return results, rows.Err()
}

// GetWithQuery 获取带查询信息的结果
func (r *resultRepository) GetWithQuery(filter *ResultFilter) ([]*ResultResponse, error) {
	query := `
		SELECT r.id, r.query_id, q.keyword, r.repository_name, r.repository_url, 
		       r.file_path, r.file_url, r.content_snippet, r.sha, r.score, 
		       r.is_processed, r.created_at
		FROM results r
		LEFT JOIN queries q ON r.query_id = q.id
		WHERE 1=1`
	
	args := []interface{}{}
	argIndex := 1
	
	if filter.QueryID != nil {
		query += ` AND r.query_id = $` + string(rune(argIndex+'0'))
		args = append(args, *filter.QueryID)
		argIndex++
	}
	
	if filter.IsProcessed != nil {
		query += ` AND r.is_processed = $` + string(rune(argIndex+'0'))
		args = append(args, *filter.IsProcessed)
		argIndex++
	}
	
	query += ` ORDER BY r.created_at DESC`
	
	if filter.Limit > 0 {
		query += ` LIMIT $` + string(rune(argIndex+'0'))
		args = append(args, filter.Limit)
		argIndex++
	}
	
	if filter.Offset > 0 {
		query += ` OFFSET $` + string(rune(argIndex+'0'))
		args = append(args, filter.Offset)
	}
	
	rows, err := r.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var results []*ResultResponse
	for rows.Next() {
		result := &ResultResponse{}
		err := rows.Scan(
			&result.ID, &result.QueryID, &result.QueryKeyword, &result.RepositoryName,
			&result.RepositoryURL, &result.FilePath, &result.FileURL, &result.ContentSnippet,
			&result.SHA, &result.Score, &result.IsProcessed, &result.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		results = append(results, result)
	}
	
	return results, rows.Err()
}

// Update 更新结果
func (r *resultRepository) Update(result *Result) error {
	query := `
		UPDATE results 
		SET query_id = $1, repository_name = $2, repository_url = $3, file_path = $4,
		    file_url = $5, content_snippet = $6, sha = $7, score = $8, is_processed = $9
		WHERE id = $10`
	
	_, err := r.db.Exec(query, result.QueryID, result.RepositoryName, result.RepositoryURL,
		result.FilePath, result.FileURL, result.ContentSnippet, result.SHA, result.Score,
		result.IsProcessed, result.ID)
	return err
}

// MarkAsProcessed 标记为已处理
func (r *resultRepository) MarkAsProcessed(id int) error {
	query := `UPDATE results SET is_processed = true WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

// Delete 删除结果
func (r *resultRepository) Delete(id int) error {
	query := `DELETE FROM results WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

// GetStatistics 获取统计信息
func (r *resultRepository) GetStatistics() (*ResultStatistics, error) {
	stats := &ResultStatistics{}
	
	// 总结果数
	err := r.db.QueryRow(`SELECT COUNT(*) FROM results`).Scan(&stats.TotalResults)
	if err != nil {
		return nil, err
	}
	
	// 已处理结果数
	err = r.db.QueryRow(`SELECT COUNT(*) FROM results WHERE is_processed = true`).Scan(&stats.ProcessedResults)
	if err != nil {
		return nil, err
	}
	
	// 今日结果数
	err = r.db.QueryRow(`SELECT COUNT(*) FROM results WHERE created_at >= CURRENT_DATE`).Scan(&stats.TodayResults)
	if err != nil {
		return nil, err
	}
	
	// 本周结果数
	err = r.db.QueryRow(`SELECT COUNT(*) FROM results WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'`).Scan(&stats.WeekResults)
	if err != nil {
		return nil, err
	}
	
	return stats, nil
}

// CheckDuplicate 检查重复
func (r *resultRepository) CheckDuplicate(repositoryURL, filePath, sha string) (bool, error) {
	var count int
	query := `SELECT COUNT(*) FROM results WHERE repository_url = $1 AND file_path = $2 AND sha = $3`
	err := r.db.QueryRow(query, repositoryURL, filePath, sha).Scan(&count)
	return count > 0, err
}
