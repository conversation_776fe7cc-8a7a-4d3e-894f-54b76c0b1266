package models

import (
	"database/sql"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// User 用户模型
type User struct {
	ID           int       `json:"id" db:"id"`
	Username     string    `json:"username" db:"username"`
	PasswordHash string    `json:"-" db:"password_hash"`
	Email        string    `json:"email" db:"email"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	LastLogin    *time.Time `json:"last_login" db:"last_login"`
	IsActive     bool      `json:"is_active" db:"is_active"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Password string `json:"password" binding:"required,min=6"`
	Email    string `json:"email" binding:"email"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID        int       `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
	LastLogin *time.Time `json:"last_login"`
	IsActive  bool      `json:"is_active"`
}

// UserRepository 用户仓库接口
type UserRepository interface {
	Create(user *User) error
	GetByUsername(username string) (*User, error)
	GetByID(id int) (*User, error)
	UpdateLastLogin(id int) error
	Update(user *User) error
	Delete(id int) error
}

// userRepository 用户仓库实现
type userRepository struct {
	db *sql.DB
}

// NewUserRepository 创建用户仓库
func NewUserRepository(db *sql.DB) UserRepository {
	return &userRepository{db: db}
}

// Create 创建用户
func (r *userRepository) Create(user *User) error {
	query := `
		INSERT INTO users (username, password_hash, email, is_active)
		VALUES ($1, $2, $3, $4)
		RETURNING id, created_at`
	
	return r.db.QueryRow(query, user.Username, user.PasswordHash, user.Email, user.IsActive).
		Scan(&user.ID, &user.CreatedAt)
}

// GetByUsername 根据用户名获取用户
func (r *userRepository) GetByUsername(username string) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, password_hash, email, created_at, last_login, is_active
		FROM users WHERE username = $1`
	
	err := r.db.QueryRow(query, username).Scan(
		&user.ID, &user.Username, &user.PasswordHash, &user.Email,
		&user.CreatedAt, &user.LastLogin, &user.IsActive,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	
	return user, err
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(id int) (*User, error) {
	user := &User{}
	query := `
		SELECT id, username, password_hash, email, created_at, last_login, is_active
		FROM users WHERE id = $1`
	
	err := r.db.QueryRow(query, id).Scan(
		&user.ID, &user.Username, &user.PasswordHash, &user.Email,
		&user.CreatedAt, &user.LastLogin, &user.IsActive,
	)
	
	if err == sql.ErrNoRows {
		return nil, nil
	}
	
	return user, err
}

// UpdateLastLogin 更新最后登录时间
func (r *userRepository) UpdateLastLogin(id int) error {
	query := `UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

// Update 更新用户
func (r *userRepository) Update(user *User) error {
	query := `
		UPDATE users 
		SET username = $1, password_hash = $2, email = $3, is_active = $4
		WHERE id = $5`
	
	_, err := r.db.Exec(query, user.Username, user.PasswordHash, user.Email, user.IsActive, user.ID)
	return err
}

// Delete 删除用户
func (r *userRepository) Delete(id int) error {
	query := `DELETE FROM users WHERE id = $1`
	_, err := r.db.Exec(query, id)
	return err
}

// HashPassword 哈希密码
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// CheckPassword 验证密码
func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:        u.ID,
		Username:  u.Username,
		Email:     u.Email,
		CreatedAt: u.CreatedAt,
		LastLogin: u.LastLogin,
		IsActive:  u.IsActive,
	}
}
