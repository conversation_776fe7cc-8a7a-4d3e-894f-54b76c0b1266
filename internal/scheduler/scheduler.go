package scheduler

import (
	"database/sql"
	"log"
	"time"

	"godeye/internal/config"
	"godeye/internal/services"
)

// Scheduler 调度器
type Scheduler struct {
	services *services.Services
	ticker   *time.Ticker
	done     chan bool
}

// New 创建调度器
func New(db *sql.DB, cfg *config.Config) *Scheduler {
	services := services.New(db, cfg)
	
	return &Scheduler{
		services: services,
		done:     make(chan bool),
	}
}

// Start 启动调度器
func (s *Scheduler) Start() {
	log.Println("启动定时任务调度器...")
	
	// 每5分钟执行一次搜索任务
	s.ticker = time.NewTicker(5 * time.Minute)
	
	// 立即执行一次
	go s.executeSearchTasks()
	
	for {
		select {
		case <-s.ticker.C:
			go s.executeSearchTasks()
		case <-s.done:
			log.Println("停止定时任务调度器")
			return
		}
	}
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	if s.ticker != nil {
		s.ticker.Stop()
	}
	s.done <- true
}

// executeSearchTasks 执行搜索任务
func (s *Scheduler) executeSearchTasks() {
	log.Println("执行定时搜索任务...")
	
	if err := s.services.Search.ExecuteAllActiveSearches(); err != nil {
		log.Printf("执行搜索任务失败: %v", err)
	} else {
		log.Println("搜索任务执行完成")
	}
}
