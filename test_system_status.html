<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GodEye 系统状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ccc;
        }
        .status-success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .status-warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>GodEye 系统状态测试</h1>
        <p>这个页面用于测试 GodEye 系统各个服务的状态</p>
        
        <div>
            <button onclick="testAllServices()">测试所有服务</button>
            <button onclick="testFrontend()">测试前端</button>
            <button onclick="testAuth()">测试认证服务</button>
            <button onclick="testMonitor()">测试监控服务</button>
            <button onclick="testNotification()">测试通知服务</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div id="results"></div>
        <div class="log" id="log"></div>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('results').innerHTML = '';
        }

        function addResult(service, status, message) {
            const resultsDiv = document.getElementById('results');
            const statusClass = status === 'success' ? 'status-success' : 
                               status === 'warning' ? 'status-warning' : 'status-error';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `status-item ${statusClass}`;
            resultDiv.innerHTML = `<strong>${service}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        async function testService(name, url) {
            log(`测试 ${name} - ${url}`);
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.text();
                    log(`${name} 响应成功: ${response.status}`);
                    addResult(name, 'success', `服务正常运行 (${response.status})`);
                    return true;
                } else {
                    log(`${name} 响应错误: ${response.status}`);
                    addResult(name, 'error', `服务响应错误 (${response.status})`);
                    return false;
                }
            } catch (error) {
                log(`${name} 连接失败: ${error.message}`);
                addResult(name, 'error', `无法连接到服务: ${error.message}`);
                return false;
            }
        }

        async function testFrontend() {
            log('开始测试前端服务...');
            await testService('前端服务 (3000)', 'http://localhost:3000');
            await testService('Nginx网关 (8080)', 'http://localhost:8080');
        }

        async function testAuth() {
            log('开始测试认证服务...');
            await testService('认证服务健康检查', 'http://localhost:8081/health');
            await testService('认证服务API', 'http://localhost:8081/api/auth/status');
        }

        async function testMonitor() {
            log('开始测试监控服务...');
            await testService('监控服务健康检查', 'http://localhost:8082/health');
            await testService('监控服务API', 'http://localhost:8082/api/monitor/status');
        }

        async function testNotification() {
            log('开始测试通知服务...');
            await testService('通知服务健康检查', 'http://localhost:8083/health');
            await testService('通知服务API', 'http://localhost:8083/api/notification/status');
        }

        async function testAllServices() {
            log('开始全面测试所有服务...');
            clearLog();
            
            await testFrontend();
            await testAuth();
            await testMonitor();
            await testNotification();
            
            log('所有服务测试完成');
        }

        // 页面加载时自动测试
        window.onload = function() {
            log('页面加载完成，准备测试系统状态');
            addResult('测试页面', 'success', '测试页面已成功加载');
        };
    </script>
</body>
</html>
