#!/bin/bash

echo "🧹 快速清理和测试"
echo "=================="

cd godeye

# 1. 停止所有容器
echo "停止容器..."
docker-compose down --remove-orphans 2>/dev/null || true
docker-compose -f docker-compose.simple.yml down --remove-orphans 2>/dev/null || true

# 2. 清理镜像
echo "清理镜像..."
docker images | grep godeye | awk '{print $3}' | xargs docker rmi -f 2>/dev/null || true

# 3. 构建
echo "构建镜像..."
docker-compose -f docker-compose.simple.yml build --no-cache

# 4. 启动
echo "启动服务..."
docker-compose -f docker-compose.simple.yml up -d

# 5. 等待
echo "等待服务启动..."
sleep 20

# 6. 检查状态
echo "检查服务状态..."
docker-compose -f docker-compose.simple.yml ps

# 7. 测试健康检查
echo "测试健康检查..."
curl -s http://localhost:80/health || echo "健康检查失败"

# 8. 显示日志
echo "显示日志..."
docker-compose -f docker-compose.simple.yml logs --tail=30

echo "完成！"
