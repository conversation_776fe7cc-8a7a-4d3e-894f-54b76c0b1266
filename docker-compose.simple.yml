version: '3.8'

services:
  # 数据层：PostgreSQL + Redis
  postgres:
    image: postgres:13
    container_name: godeye-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: godeye
      POSTGRES_USER: godeye
      POSTGRES_PASSWORD: godeye123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - godeye-network

  redis:
    image: redis:6-alpine
    container_name: godeye-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - godeye-network

  # 应用层：Go后端 + React前端（单体架构）
  godeye-app:
    build: .
    container_name: godeye-app
    restart: unless-stopped
    environment:
      - SECRET_KEY=godeye-production-secret-key-change-me
      - DATABASE_URL=*****************************************/godeye?sslmode=disable
      - REDIS_URL=redis://redis:6379/0
      - GIN_MODE=release
    ports:
      - "80:80"
    depends_on:
      - postgres
      - redis
    networks:
      - godeye-network
    volumes:
      - ./logs:/app/logs

volumes:
  postgres_data:
  redis_data:

networks:
  godeye-network:
    driver: bridge
