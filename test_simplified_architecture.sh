#!/bin/bash

echo "🧪 GodEye 简化架构功能测试"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_function() {
    local test_name="$1"
    local test_command="$2"
    local expected_status="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}测试 $TOTAL_TESTS: $test_name${NC}"
    
    if eval "$test_command"; then
        if [ "$expected_status" = "success" ]; then
            echo -e "${GREEN}✅ 通过${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}❌ 失败 (预期失败但实际成功)${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        if [ "$expected_status" = "fail" ]; then
            echo -e "${GREEN}✅ 通过 (预期失败)${NC}"
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            echo -e "${RED}❌ 失败${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    fi
    echo ""
}

# HTTP测试函数
http_test() {
    local test_name="$1"
    local url="$2"
    local expected_status="$3"
    local method="${4:-GET}"
    local data="$5"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${BLUE}HTTP测试 $TOTAL_TESTS: $test_name${NC}"
    echo "URL: $url"
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X POST -H "Content-Type: application/json" -d "$data" "$url")
    else
        response=$(curl -s -w "%{http_code}" "$url")
    fi
    
    http_code="${response: -3}"
    response_body="${response%???}"
    
    echo "HTTP状态码: $http_code"
    echo "响应内容: $response_body"
    
    if [ "$http_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ 失败 (期望: $expected_status, 实际: $http_code)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    echo ""
}

echo "🔍 1. 检查Docker环境"
echo "-------------------"

test_function "Docker是否安装" "command -v docker >/dev/null 2>&1" "success"
test_function "Docker服务是否运行" "docker info >/dev/null 2>&1" "success"

echo "🏗️ 2. 构建和启动服务"
echo "-------------------"

echo "停止现有服务..."
docker-compose -f docker-compose.simple.yml down --remove-orphans >/dev/null 2>&1

echo "构建并启动简化架构..."
if docker-compose -f docker-compose.simple.yml up --build -d; then
    echo -e "${GREEN}✅ 服务启动成功${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ 服务启动失败${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo "等待服务完全启动..."
sleep 30

echo "📊 3. 检查容器状态"
echo "-------------------"

echo "当前运行的容器:"
docker-compose -f docker-compose.simple.yml ps

# 检查各个容器是否运行
test_function "PostgreSQL容器运行" "docker-compose -f docker-compose.simple.yml ps | grep -q 'godeye-postgres.*Up'" "success"
test_function "Redis容器运行" "docker-compose -f docker-compose.simple.yml ps | grep -q 'godeye-redis.*Up'" "success"
test_function "GodEye应用容器运行" "docker-compose -f docker-compose.simple.yml ps | grep -q 'godeye-app.*Up'" "success"

echo "🔗 4. 测试服务连通性"
echo "-------------------"

# 测试数据库连接
test_function "PostgreSQL连接" "docker exec godeye-postgres pg_isready -U godeye >/dev/null 2>&1" "success"
test_function "Redis连接" "docker exec godeye-redis redis-cli ping | grep -q PONG" "success"

echo "🌐 5. 测试HTTP接口"
echo "-------------------"

# 等待应用完全启动
echo "等待应用完全启动..."
sleep 10

# 测试健康检查
http_test "健康检查接口" "http://localhost:80/health" "200"

# 测试API接口
http_test "认证检查接口(未认证)" "http://localhost:80/api/auth/check" "401"

# 测试登录接口
login_data='{"username":"admin","password":"admin123"}'
http_test "管理员登录" "http://localhost:80/api/auth/login" "200" "POST" "$login_data"

echo "📈 6. 架构对比验证"
echo "-------------------"

echo "容器数量对比:"
current_containers=$(docker-compose -f docker-compose.simple.yml ps -q | wc -l)
echo "当前架构容器数: $current_containers"
echo "原复杂架构容器数: 9"
echo "简化率: $(( (9 - current_containers) * 100 / 9 ))%"

if [ "$current_containers" -le 3 ]; then
    echo -e "${GREEN}✅ 架构简化成功${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ 架构简化失败${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo "🧪 7. 功能完整性测试"
echo "-------------------"

# 测试数据库表是否创建
test_function "数据库表创建" "docker exec godeye-postgres psql -U godeye -d godeye -c '\dt' | grep -q users" "success"

# 测试默认用户是否创建
test_function "默认管理员用户" "docker exec godeye-postgres psql -U godeye -d godeye -c 'SELECT username FROM users WHERE username='\''admin'\'';' | grep -q admin" "success"

echo "📋 8. 日志检查"
echo "-------------"

echo "PostgreSQL日志:"
docker-compose -f docker-compose.simple.yml logs --tail=3 postgres

echo "Redis日志:"
docker-compose -f docker-compose.simple.yml logs --tail=3 redis

echo "GodEye应用日志:"
docker-compose -f docker-compose.simple.yml logs --tail=5 godeye-app

echo "🎯 9. 性能基准测试"
echo "-------------------"

# 测试启动时间
start_time=$(date +%s)
docker-compose -f docker-compose.simple.yml restart godeye-app >/dev/null 2>&1
sleep 5

# 等待服务可用
while ! curl -s http://localhost:80/health >/dev/null 2>&1; do
    sleep 1
done

end_time=$(date +%s)
startup_time=$((end_time - start_time))

echo "应用重启时间: ${startup_time}秒"

if [ "$startup_time" -lt 30 ]; then
    echo -e "${GREEN}✅ 启动时间优秀 (<30秒)${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
elif [ "$startup_time" -lt 60 ]; then
    echo -e "${YELLOW}⚠️ 启动时间良好 (<60秒)${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    echo -e "${RED}❌ 启动时间过长 (>60秒)${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
fi
TOTAL_TESTS=$((TOTAL_TESTS + 1))

echo "📊 10. 测试结果汇总"
echo "==================="

echo "总测试数: $TOTAL_TESTS"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

success_rate=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))
echo "成功率: $success_rate%"

if [ "$success_rate" -ge 90 ]; then
    echo -e "${GREEN}🎉 测试结果: 优秀${NC}"
    exit_code=0
elif [ "$success_rate" -ge 70 ]; then
    echo -e "${YELLOW}⚠️ 测试结果: 良好${NC}"
    exit_code=0
else
    echo -e "${RED}❌ 测试结果: 需要改进${NC}"
    exit_code=1
fi

echo ""
echo "🚀 简化架构验证完成！"
echo "====================="
echo "访问地址: http://localhost:80"
echo "默认登录: admin / admin123"
echo ""
echo "管理命令:"
echo "  查看日志: docker-compose -f docker-compose.simple.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.simple.yml down"
echo "  重启服务: docker-compose -f docker-compose.simple.yml restart"

exit $exit_code
