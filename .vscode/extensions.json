{
  "recommendations": [
    // Go 开发
    "golang.go",
    
    // 前端开发
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "dbaeumer.vscode-eslint",
    
    // React/Next.js
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    
    // Docker 和容器
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    
    // 数据库
    "ms-mssql.mssql",
    "cweijan.vscode-postgresql-client2",
    
    // 版本控制
    "eamodio.gitlens",
    "mhutchie.git-graph",
    
    // 文件和项目管理
    "ms-vscode.vscode-yaml",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-json",
    "yzhang.markdown-all-in-one",
    
    // 代码质量和测试
    "ms-vscode.test-adapter-converter",
    "hbenl.vscode-test-explorer",
    "ryanluker.vscode-coverage-gutters",
    
    // 实用工具
    "gruntfuggly.todo-tree",
    "humao.rest-client",
    "ms-vscode.vscode-todo-highlight",
    "streetsidesoftware.code-spell-checker",
    
    // 主题和图标
    "zhuangtongfa.material-theme",
    "pkief.material-icon-theme",
    
    // API 开发
    "42crunch.vscode-openapi",
    "rangav.vscode-thunder-client",
    
    // 环境变量
    "mikestead.dotenv",
    
    // 代码片段
    "ms-vscode.vscode-typescript-next",
    "xabikos.javascriptsnippets",
    "bradlc.vscode-tailwindcss",
    
    // 调试和性能
    "ms-vscode.vscode-js-profile-flame",
    "ms-vscode.vscode-js-profile-table",
    
    // 协作和文档
    "ms-vsliveshare.vsliveshare",
    "shd101wyy.markdown-preview-enhanced"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify"
  ]
}
