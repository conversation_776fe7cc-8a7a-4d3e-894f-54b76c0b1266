#!/bin/bash

echo "🧹 GodEye Docker环境清理和启动"
echo "=============================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 进入godeye目录
cd "$(dirname "$0")"

echo -e "${BLUE}1. 停止所有GodEye相关容器${NC}"
# 停止旧的复杂架构容器
docker-compose down --remove-orphans 2>/dev/null || true

# 停止简化架构容器
docker-compose -f docker-compose.simple.yml down --remove-orphans 2>/dev/null || true

echo -e "${GREEN}✅ 容器停止完成${NC}"

echo -e "${BLUE}2. 清理GodEye相关镜像${NC}"
# 删除GodEye相关镜像
docker images | grep godeye | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true

# 清理悬空镜像
docker image prune -f

echo -e "${GREEN}✅ 镜像清理完成${NC}"

echo -e "${BLUE}3. 清理网络和卷${NC}"
# 清理网络
docker network prune -f

# 清理卷（谨慎操作）
docker volume ls | grep godeye | awk '{print $2}' | xargs -r docker volume rm 2>/dev/null || true

echo -e "${GREEN}✅ 网络和卷清理完成${NC}"

echo -e "${BLUE}4. 检查端口占用${NC}"
# 检查80端口是否被占用
if lsof -i :80 >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  端口80被占用，尝试释放...${NC}"
    # 尝试停止占用80端口的进程
    lsof -ti :80 | xargs -r kill -9 2>/dev/null || true
    sleep 2
fi

# 检查3000端口
if lsof -i :3000 >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  端口3000被占用，尝试释放...${NC}"
    lsof -ti :3000 | xargs -r kill -9 2>/dev/null || true
    sleep 2
fi

# 检查8080端口
if lsof -i :8080 >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  端口8080被占用，尝试释放...${NC}"
    lsof -ti :8080 | xargs -r kill -9 2>/dev/null || true
    sleep 2
fi

echo -e "${GREEN}✅ 端口检查完成${NC}"

echo -e "${BLUE}5. 构建新的简化架构${NC}"
# 构建新镜像
if docker-compose -f docker-compose.simple.yml build --no-cache; then
    echo -e "${GREEN}✅ 镜像构建成功${NC}"
else
    echo -e "${RED}❌ 镜像构建失败${NC}"
    exit 1
fi

echo -e "${BLUE}6. 启动简化架构服务${NC}"
# 启动服务
if docker-compose -f docker-compose.simple.yml up -d; then
    echo -e "${GREEN}✅ 服务启动成功${NC}"
else
    echo -e "${RED}❌ 服务启动失败${NC}"
    echo "查看日志:"
    docker-compose -f docker-compose.simple.yml logs
    exit 1
fi

echo -e "${BLUE}7. 等待服务就绪${NC}"
# 等待服务启动
sleep 15

echo -e "${BLUE}8. 检查服务状态${NC}"
# 显示容器状态
echo "容器状态:"
docker-compose -f docker-compose.simple.yml ps

echo ""
echo "服务日志 (最近20行):"
docker-compose -f docker-compose.simple.yml logs --tail=20

echo -e "${BLUE}9. 健康检查${NC}"
# 健康检查
for i in {1..30}; do
    if curl -s http://localhost:80/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 健康检查通过 (尝试 $i/30)${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待服务响应... ($i/30)${NC}"
        sleep 2
    fi
    
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ 健康检查失败${NC}"
        echo "详细日志:"
        docker-compose -f docker-compose.simple.yml logs godeye-app
        exit 1
    fi
done

echo -e "${BLUE}10. 基础功能测试${NC}"
# 测试登录
echo "测试用户登录..."
login_response=$(curl -s -X POST -H "Content-Type: application/json" \
    -d '{"username":"admin","password":"admin123"}' \
    http://localhost:80/api/auth/login 2>/dev/null)

if echo "$login_response" | grep -q '"code":200' 2>/dev/null; then
    echo -e "${GREEN}✅ 登录测试通过${NC}"
    
    # 提取token
    token=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    # 测试用户信息获取
    echo "测试获取用户信息..."
    user_response=$(curl -s -H "Authorization: Bearer $token" \
        http://localhost:80/api/auth/user 2>/dev/null)
    
    if echo "$user_response" | grep -q '"code":200' 2>/dev/null; then
        echo -e "${GREEN}✅ 用户信息获取测试通过${NC}"
    else
        echo -e "${YELLOW}⚠️  用户信息获取测试失败${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  登录测试失败，但服务已启动${NC}"
    echo "登录响应: $login_response"
fi

echo ""
echo -e "${GREEN}🎉 GodEye系统清理和启动完成！${NC}"
echo "=================================="
echo -e "${BLUE}🌐 访问地址: http://localhost:80${NC}"
echo -e "${BLUE}👤 默认用户: admin${NC}"
echo -e "${BLUE}🔑 默认密码: admin123${NC}"
echo ""
echo -e "${BLUE}📋 管理命令:${NC}"
echo "  查看日志: docker-compose -f docker-compose.simple.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.simple.yml down"
echo "  重启服务: docker-compose -f docker-compose.simple.yml restart"
echo ""
echo -e "${BLUE}🧪 运行完整测试: ./comprehensive_test.sh${NC}"
