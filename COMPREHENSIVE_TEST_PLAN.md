# GodEye 系统全面测试计划

## 测试概述

本文档提供了 GodEye 系统的详细测试计划，确保所有功能都能正常工作。

## 测试环境准备

### 前置条件
- [ ] Docker Desktop 已启动
- [ ] 所有服务已通过 Docker Compose 启动
- [ ] 端口检查显示所有服务正常运行
- [ ] 浏览器可以访问 http://localhost:8080

## 第一阶段：基础功能测试

### 1.1 系统访问测试
- [ ] **测试项**: 访问主页 http://localhost:8080
  - **期望结果**: 显示登录页面，无导航菜单
  - **验证点**: 页面加载正常，无 JavaScript 错误

- [ ] **测试项**: 访问前端开发服务器 http://localhost:3000
  - **期望结果**: 自动重定向到登录页面
  - **验证点**: 重定向功能正常

### 1.2 用户认证测试
- [ ] **测试项**: 使用错误凭据登录
  - **输入**: 用户名: <EMAIL>, 密码: wrong
  - **期望结果**: 显示登录失败错误信息

- [ ] **测试项**: 使用正确凭据登录
  - **输入**: 用户名: <EMAIL>, 密码: admin123
  - **期望结果**: 登录成功，跳转到仪表板页面

- [ ] **测试项**: 登录后导航菜单显示
  - **期望结果**: 显示完整导航菜单，包括：
    - 仪表板
    - 账号管理
    - 监控管理
    - 搜索结果
    - 通知配置 ✨ (新修复的功能)

## 第二阶段：账号管理功能测试

### 2.1 账号添加测试
- [ ] **测试项**: 添加 GitHub 账号
  - **操作**: 点击"添加账号" → 选择 GitHub → 填写信息
  - **输入**: 
    - 平台: GitHub
    - 用户名: test_github_user
    - Token: ghp_test_token_123
  - **期望结果**: 账号添加成功，列表中显示新账号

- [ ] **测试项**: 添加 GitLab 账号
  - **操作**: 添加 GitLab 账号
  - **输入**:
    - 平台: GitLab
    - 用户名: test_gitlab_user
    - Token: glpat_test_token_123
  - **期望结果**: 账号添加成功

- [ ] **测试项**: 添加 Gitee 账号
  - **操作**: 添加 Gitee 账号
  - **输入**:
    - 平台: Gitee
    - 用户名: test_gitee_user
    - Token: gitee_test_token_123
  - **期望结果**: 账号添加成功

### 2.2 账号管理测试
- [ ] **测试项**: 查看账号列表
  - **期望结果**: 显示所有已添加的账号，包含状态信息

- [ ] **测试项**: 编辑账号信息
  - **操作**: 点击编辑按钮，修改账号信息
  - **期望结果**: 修改成功，列表更新

- [ ] **测试项**: 删除账号
  - **操作**: 点击删除按钮，确认删除
  - **期望结果**: 账号从列表中移除

## 第三阶段：监控功能测试

### 3.1 监控任务创建
- [ ] **测试项**: 创建关键词监控任务
  - **操作**: 监控管理 → 新建监控
  - **输入**:
    - 任务名称: 密码泄露监控
    - 关键词: password, 密码, secret
    - 监控平台: GitHub, GitLab, Gitee
    - 扫描频率: 每小时
  - **期望结果**: 任务创建成功

- [ ] **测试项**: 创建仓库监控任务
  - **操作**: 新建仓库监控
  - **输入**:
    - 仓库URL: https://github.com/test/repo
    - 监控类型: 指定仓库
  - **期望结果**: 仓库监控任务创建成功

### 3.2 搜索功能测试
- [ ] **测试项**: 手动触发搜索
  - **操作**: 点击"立即搜索"按钮
  - **期望结果**: 搜索任务开始执行

- [ ] **测试项**: 查看搜索结果
  - **操作**: 访问搜索结果页面
  - **期望结果**: 显示搜索到的代码片段和仓库信息

### 3.3 白名单功能测试
- [ ] **测试项**: 添加仓库到白名单
  - **操作**: 在搜索结果中点击"加入仓库白名单"
  - **期望结果**: 仓库添加到白名单，后续不再扫描

- [ ] **测试项**: 添加文件到白名单
  - **操作**: 在搜索结果中点击"加入文件白名单"
  - **期望结果**: 特定文件添加到白名单

- [ ] **测试项**: 查看白名单管理
  - **操作**: 访问白名单管理页面
  - **期望结果**: 显示所有白名单项目，可以移除

## 第四阶段：通知功能测试

### 4.1 通知渠道配置
- [ ] **测试项**: 配置邮件通知
  - **操作**: 通知配置 → 新建通知渠道 → 邮件
  - **输入**:
    - 类型: 邮件
    - SMTP服务器: smtp.gmail.com
    - 端口: 587
    - 用户名: <EMAIL>
    - 密码: app_password
  - **期望结果**: 邮件配置保存成功

- [ ] **测试项**: 配置企业微信通知
  - **操作**: 新建企业微信通知渠道
  - **输入**:
    - Webhook URL: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=test
  - **期望结果**: 企业微信配置保存成功

- [ ] **测试项**: 配置飞书通知
  - **操作**: 新建飞书通知渠道
  - **输入**:
    - Webhook URL: https://open.feishu.cn/open-apis/bot/v2/hook/test
  - **期望结果**: 飞书配置保存成功

- [ ] **测试项**: 配置钉钉通知
  - **操作**: 新建钉钉通知渠道
  - **输入**:
    - Webhook URL: https://oapi.dingtalk.com/robot/send?access_token=test
  - **期望结果**: 钉钉配置保存成功

### 4.2 通知发送测试
- [ ] **测试项**: 测试邮件发送
  - **操作**: 点击"发送测试邮件"
  - **期望结果**: 收到测试邮件

- [ ] **测试项**: 测试企业微信发送
  - **操作**: 点击"发送测试消息"
  - **期望结果**: 企业微信群收到测试消息

## 第五阶段：集成测试

### 5.1 端到端流程测试
- [ ] **测试项**: 完整监控流程
  - **操作**: 创建监控任务 → 等待搜索 → 查看结果 → 接收通知
  - **期望结果**: 整个流程正常工作

- [ ] **测试项**: 白名单生效测试
  - **操作**: 添加白名单 → 再次搜索 → 验证结果
  - **期望结果**: 白名单项目不再出现在结果中

### 5.2 性能测试
- [ ] **测试项**: 大量关键词搜索
  - **操作**: 添加多个关键词进行搜索
  - **期望结果**: 系统响应正常，无超时

- [ ] **测试项**: 并发用户测试
  - **操作**: 多个浏览器窗口同时操作
  - **期望结果**: 系统稳定运行

## 第六阶段：错误处理测试

### 6.1 网络错误测试
- [ ] **测试项**: API 服务停止时的处理
  - **操作**: 停止某个后端服务
  - **期望结果**: 前端显示友好的错误信息

### 6.2 数据验证测试
- [ ] **测试项**: 无效输入处理
  - **操作**: 输入无效的 Token 或 URL
  - **期望结果**: 显示验证错误信息

## 测试报告模板

### 测试结果记录
```
测试项目: [测试名称]
执行时间: [时间]
测试结果: ✅ 通过 / ❌ 失败 / ⚠️ 部分通过
问题描述: [如果失败，描述具体问题]
截图: [如有必要，提供截图]
```

### 问题追踪
- **高优先级问题**: 影响核心功能的问题
- **中优先级问题**: 影响用户体验的问题  
- **低优先级问题**: 小的UI或文案问题

## 测试完成标准

系统被认为测试通过需要满足：
- [ ] 所有高优先级测试项目通过
- [ ] 90% 以上的中优先级测试项目通过
- [ ] 核心用户流程完整可用
- [ ] 无阻塞性错误

---

**使用说明**: 请按照测试计划逐项执行，记录测试结果，发现问题及时反馈以便修复。
